{"version": 3, "sources": ["../../../../src/build/webpack/plugins/jsconfig-paths-plugin.ts"], "names": ["path", "debug", "log", "asterisk", "hasZeroOrOneAsteriskCharacter", "str", "seenAsterisk", "i", "length", "charCodeAt", "pathIsRelative", "testPath", "test", "tryParsePattern", "pattern", "indexOfStar", "indexOf", "undefined", "prefix", "slice", "suffix", "isPatternMatch", "candidate", "startsWith", "endsWith", "findBestPatternMatch", "values", "getPattern", "matchedValue", "longestMatchPrefixLength", "v", "matchPatternOrExact", "patternStrings", "patterns", "patternString", "push", "_", "isString", "text", "matchedText", "substring", "patternText", "forEachBail", "array", "iterator", "callback", "next", "loop", "err", "result", "NODE_MODULES_REGEX", "JsConfigPathsPlugin", "constructor", "paths", "resolvedBaseUrl", "jsConfigPlugin", "apply", "resolver", "target", "ensureH<PERSON>", "getHook", "tapAsync", "request", "resolveContext", "pathsKeys", "Object", "keys", "moduleName", "match", "posix", "isAbsolute", "process", "platform", "win32", "matchedPattern", "matchedStar", "matchedPatternText", "triedPaths", "subst", "pathCallback", "curPath", "replace", "join", "obj", "assign", "doResolve", "resolverErr", "resolverResult"], "mappings": "AAAA;;;;CAIC,GACD,OAAOA,UAAU,OAAM;AAEvB,SAASC,KAAK,QAAQ,2BAA0B;AAEhD,MAAMC,MAAMD,MAAM;AAOlB,MAAME,WAAW;AAEjB,OAAO,SAASC,8BAA8BC,GAAW;IACvD,IAAIC,eAAe;IACnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,IAAIF,IAAII,UAAU,CAACF,OAAOJ,UAAU;YAClC,IAAI,CAACG,cAAc;gBACjBA,eAAe;YACjB,OAAO;gBACL,6BAA6B;gBAC7B,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAEA;;CAEC,GACD,OAAO,SAASI,eAAeC,QAAgB;IAC7C,OAAO,kBAAkBC,IAAI,CAACD;AAChC;AAEA,OAAO,SAASE,gBAAgBC,OAAe;IAC7C,qEAAqE;IACrE,MAAMC,cAAcD,QAAQE,OAAO,CAAC;IACpC,OAAOD,gBAAgB,CAAC,IACpBE,YACA;QACEC,QAAQJ,QAAQK,KAAK,CAAC,GAAGJ;QACzBK,QAAQN,QAAQK,KAAK,CAACJ,cAAc;IACtC;AACN;AAEA,SAASM,eAAe,EAAEH,MAAM,EAAEE,MAAM,EAAW,EAAEE,SAAiB;IACpE,OACEA,UAAUd,MAAM,IAAIU,OAAOV,MAAM,GAAGY,OAAOZ,MAAM,IACjDc,UAAUC,UAAU,CAACL,WACrBI,UAAUE,QAAQ,CAACJ;AAEvB;AAEA,8EAA8E,GAC9E,OAAO,SAASK,qBACdC,MAAoB,EACpBC,UAAiC,EACjCL,SAAiB;IAEjB,IAAIM;IACJ,8CAA8C;IAC9C,IAAIC,2BAA2B,CAAC;IAEhC,KAAK,MAAMC,KAAKJ,OAAQ;QACtB,MAAMZ,UAAUa,WAAWG;QAC3B,IACET,eAAeP,SAASQ,cACxBR,QAAQI,MAAM,CAACV,MAAM,GAAGqB,0BACxB;YACAA,2BAA2Bf,QAAQI,MAAM,CAACV,MAAM;YAChDoB,eAAeE;QACjB;IACF;IAEA,OAAOF;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASG,oBACdC,cAAiC,EACjCV,SAAiB;IAEjB,MAAMW,WAAsB,EAAE;IAC9B,KAAK,MAAMC,iBAAiBF,eAAgB;QAC1C,IAAI,CAAC5B,8BAA8B8B,gBAAgB;QACnD,MAAMpB,UAAUD,gBAAgBqB;QAChC,IAAIpB,SAAS;YACXmB,SAASE,IAAI,CAACrB;QAChB,OAAO,IAAIoB,kBAAkBZ,WAAW;YACtC,wDAAwD;YACxD,OAAOY;QACT;IACF;IAEA,OAAOT,qBAAqBQ,UAAU,CAACG,IAAMA,GAAGd;AAClD;AAEA;;CAEC,GACD,OAAO,SAASe,SAASC,IAAa;IACpC,OAAO,OAAOA,SAAS;AACzB;AAEA;;;CAGC,GACD,OAAO,SAASC,YAAYzB,OAAgB,EAAEQ,SAAiB;IAC7D,OAAOA,UAAUkB,SAAS,CACxB1B,QAAQI,MAAM,CAACV,MAAM,EACrBc,UAAUd,MAAM,GAAGM,QAAQM,MAAM,CAACZ,MAAM;AAE5C;AAEA,OAAO,SAASiC,YAAY,EAAEvB,MAAM,EAAEE,MAAM,EAAW;IACrD,OAAO,CAAC,EAAEF,OAAO,CAAC,EAAEE,OAAO,CAAC;AAC9B;AAEA;;;CAGC,GACD,SAASsB,YACPC,KAAe,EACfC,QAGS,EACTC,QAA2C;IAE3C,IAAIF,MAAMnC,MAAM,KAAK,GAAG,OAAOqC;IAE/B,IAAItC,IAAI;IACR,MAAMuC,OAAO;QACX,IAAIC,OAA4B9B;QAChC2B,SAASD,KAAK,CAACpC,IAAI,EAAE,CAACyC,KAAKC;YACzB,IAAID,OAAOC,WAAWhC,aAAaV,KAAKoC,MAAMnC,MAAM,EAAE;gBACpD,OAAOqC,SAASG,KAAKC;YACvB;YACA,IAAIF,SAAS,OAAO,MAAOD;YAC3BC,OAAO;QACT;QACA,IAAI,CAACA,MAAMA,OAAO;QAClB,OAAOA;IACT;IACA,MAAOD;AACT;AAEA,MAAMI,qBAAqB;AAI3B;;;;CAIC,GACD,OAAO,MAAMC;IAKXC,YAAYC,KAAY,EAAEC,eAAuB,CAAE;QACjD,IAAI,CAACD,KAAK,GAAGA;QACb,IAAI,CAACC,eAAe,GAAGA;QACvB,IAAI,CAACC,cAAc,GAAG;QACtBrD,IAAI,4CAA4CmD;QAChDnD,IAAI,wBAAwBoD;IAC9B;IACAE,MAAMC,QAAa,EAAE;QACnB,MAAMC,SAASD,SAASE,UAAU,CAAC;QACnCF,SACGG,OAAO,CAAC,qBACRC,QAAQ,CACP,uBACA,CACEC,SACAC,gBACAlB;YAEA,MAAMQ,QAAQ,IAAI,CAACA,KAAK;YACxB,MAAMW,YAAYC,OAAOC,IAAI,CAACb;YAE9B,mCAAmC;YACnC,IAAIW,UAAUxD,MAAM,KAAK,GAAG;gBAC1BN,IAAI;gBACJ,OAAO2C;YACT;YAEA,MAAMsB,aAAaL,QAAQA,OAAO;YAElC,gEAAgE;YAChE,IAAIA,QAAQ9D,IAAI,CAACoE,KAAK,CAAClB,qBAAqB;gBAC1ChD,IAAI,oDAAoDiE;gBACxD,OAAOtB;YACT;YAEA,IACE7C,KAAKqE,KAAK,CAACC,UAAU,CAACH,eACrBI,QAAQC,QAAQ,KAAK,WAAWxE,KAAKyE,KAAK,CAACH,UAAU,CAACH,aACvD;gBACAjE,IAAI,iDAAiDiE;gBACrD,OAAOtB;YACT;YAEA,IAAInC,eAAeyD,aAAa;gBAC9BjE,IAAI,gDAAgDiE;gBACpD,OAAOtB;YACT;YAEA,oDAAoD;YAEpD,oGAAoG;YACpG,MAAM6B,iBAAiB3C,oBAAoBiC,WAAWG;YACtD,IAAI,CAACO,gBAAgB;gBACnBxE,IAAI,iDAAiDiE;gBACrD,OAAOtB;YACT;YAEA,MAAM8B,cAActC,SAASqC,kBACzBzD,YACAsB,YAAYmC,gBAAgBP;YAChC,MAAMS,qBAAqBvC,SAASqC,kBAChCA,iBACAjC,YAAYiC;YAEhB,IAAIG,aAAa,EAAE;YAEnBnC,YACEW,KAAK,CAACuB,mBAAmB,EACzB,CAACE,OAAOC;gBACN,MAAMC,UAAUL,cACZG,MAAMG,OAAO,CAAC,KAAKN,eACnBG;gBACJ,8BAA8B;gBAC9B,IAAIE,QAAQxD,QAAQ,CAAC,UAAU;oBAC7B,0BAA0B;oBAC1B,OAAOuD;gBACT;gBACA,MAAMzD,YAAYtB,KAAKkF,IAAI,CAAC,IAAI,CAAC5B,eAAe,EAAE0B;gBAClD,MAAMG,MAAMlB,OAAOmB,MAAM,CAAC,CAAC,GAAGtB,SAAS;oBACrCA,SAASxC;gBACX;gBACAmC,SAAS4B,SAAS,CAChB3B,QACAyB,KACA,CAAC,4CAA4C,EAAEP,mBAAmB,IAAI,EAAEtD,UAAU,CAAC,EACnFyC,gBACA,CAACuB,aAAkBC;oBACjB,IAAID,eAAeC,mBAAmBtE,WAAW;wBAC/C4D,WAAW1C,IAAI,CAACb;wBAChB,0BAA0B;wBAC1B,OAAOyD;oBACT;oBACA,OAAOA,aAAaO,aAAaC;gBACnC;YAEJ,GACA1C;QAEJ;IAEN;AACF"}