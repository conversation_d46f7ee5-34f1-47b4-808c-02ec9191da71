/**
 * @license React
 * react-server-dom-webpack-server.browser.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("react-dom"),ba=require("react"),l=null,m=0;function n(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<m&&(a.enqueue(new Uint8Array(l.buffer,0,m)),l=new Uint8Array(512),m=0),a.enqueue(b);else{var d=l.length-m;d<b.byteLength&&(0===d?a.enqueue(l):(l.set(b.subarray(0,d),m),a.enqueue(l),b=b.subarray(d)),l=new Uint8Array(512),m=0);l.set(b,m);m+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),u=Symbol.for("react.server.reference");function v(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===u){var b=ea.call(arguments,1);a.$$typeof=u;a.$$id=this.$$id;a.$$bound=this.$$bound?this.$$bound.concat(b):b}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=v(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=v({},a.$$id,!0),e=new Proxy(c,ka);a.status="fulfilled";a.value=e;return a.then=v(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}c=a[b];c||(c=v(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},sa={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=w?w:null;if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),x(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var d=w?w:null;if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?x(d,"C",[a,b]):x(d,"C",a))}}}
function na(a,b,d){if("string"===typeof a){var c=w?w:null;if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,h=d.imageSizes,k="";"string"===typeof g&&""!==g?(k+="["+g+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;f+="[image]"+k}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=y(d))?x(c,"L",[a,b,d]):x(c,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var d=w?w:null;if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"m",[a,b]):x(d,"m",a)}}}
function pa(a,b,d){if("string"===typeof a){var c=w?w:null;if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=y(d))?x(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?x(c,"S",[a,b]):x(c,"S",a)}}}function qa(a,b){if("string"===typeof a){var d=w?w:null;if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"X",[a,b]):x(d,"X",a)}}}
function ra(a,b){if("string"===typeof a){var d=w?w:null;if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"M",[a,b]):x(d,"M",a)}}}function y(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ta=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,z=Symbol.for("react.element"),ua=Symbol.for("react.fragment"),va=Symbol.for("react.provider"),wa=Symbol.for("react.server_context"),xa=Symbol.for("react.forward_ref"),ya=Symbol.for("react.suspense"),za=Symbol.for("react.suspense_list"),Aa=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Ba=Symbol.for("react.default_value"),Ca=Symbol.for("react.memo_cache_sentinel"),Da=Symbol.iterator,C=null;
function D(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");D(a,d);b.context._currentValue=b.value}}}function Ea(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ea(a)}
function Fa(a){var b=a.parent;null!==b&&Fa(b);a.context._currentValue=a.value}function Ga(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?D(a,b):Ga(a,b)}
function Ha(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?D(a,d):Ha(a,d);b.context._currentValue=b.value}function F(a){var b=C;b!==a&&(null===b?Fa(a):null===a?Ea(b):b.depth===a.depth?D(b,a):b.depth>a.depth?Ga(b,a):Ha(b,a),C=a)}function Ia(a,b){var d=a._currentValue;a._currentValue=b;var c=C;return C=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Ja=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ka(){}function La(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Ka,Ka),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}H=b;throw Ja;}}var H=null;
function Ma(){if(null===H)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=H;H=null;return a}var I=null,J=0,K=null;function Na(){var a=K;K=null;return a}function Oa(a){return a._currentValue}
var Sa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:L,useTransition:L,readContext:Oa,useContext:Oa,useReducer:L,useRef:L,useState:L,useInsertionEffect:L,useLayoutEffect:L,useImperativeHandle:L,useEffect:L,useId:Pa,useSyncExternalStore:L,useCacheRefresh:function(){return Qa},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ca;return b},use:Ra};
function L(){throw Error("This Hook is not supported in Server Components.");}function Qa(){throw Error("Refreshing the cache is not supported in Server Components.");}function Pa(){if(null===I)throw Error("useId can only be used while React is rendering");var a=I.identifierCount++;return":"+I.identifierPrefix+"S"+a.toString(32)+":"}
function Ra(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=J;J+=1;null===K&&(K=[]);return La(K,a,b)}if(a.$$typeof===wa)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Ta(){return(new AbortController).signal}function Ua(){var a=w?w:null;return a?a.cache:new Map}
var Va={getCacheSignal:function(){var a=Ua(),b=a.get(Ta);void 0===b&&(b=Ta(),a.set(Ta,b));return b},getCacheForType:function(a){var b=Ua(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},Wa=Array.isArray;function Xa(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function Ya(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Wa(a))return"[...]";a=Xa(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function M(a){if("string"===typeof a)return a;switch(a){case ya:return"Suspense";case za:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case xa:return M(a.render);case Aa:return M(a.type);case A:var b=a._payload;a=a._init;try{return M(a(b))}catch(d){}}return""}
function N(a,b){var d=Xa(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(Wa(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?N(g):Ya(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===z)e="<"+M(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var h=f[g],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k="object"===typeof k&&null!==k?N(k):
Ya(k);h===b?(d=e.length,c=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var Za=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$a=Za.ContextRegistry,O=JSON.stringify,ab=Za.ReactCurrentDispatcher,bb=Za.ReactCurrentCache;function cb(a){console.error(a)}function db(){}
function eb(a,b,d,c,e,f){if(null!==bb.current&&bb.current!==Va)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=sa;bb.current=Va;var g=new Set,h=[],k=new Set,p={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:g,pingedTasks:h,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,identifierPrefix:e||"",identifierCount:1,onError:void 0===d?cb:d,onPostpone:void 0===f?db:f,toJSON:function(t,E){return fb(p,this,t,E)}};p.pendingChunks++;b=gb(c);a=hb(p,a,b,g);h.push(a);return p}var w=null,ib={};
function jb(a,b){a.pendingChunks++;var d=hb(a,null,C,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,kb(a,d),d.id;case "rejected":var c=P(a,b.reason);Q(a,d.id,c);return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=e;kb(a,d)},function(e){d.status=4;e=P(a,e);Q(a,d.id,e);null!==a.destination&&
R(a,a.destination)});return d.id}function x(a,b,d){d=O(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(d=a.destination,a.flushScheduled=!0,R(a,d))}function lb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function mb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:lb}}
function S(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[z,b,d,e];J=0;K=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:mb(e):e}if("string"===typeof b)return[z,b,d,e];if("symbol"===typeof b)return b===ua?e.children:[z,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[z,b,d,e];switch(b.$$typeof){case A:var g=
b._init;b=g(b._payload);return S(a,b,d,c,e,f);case xa:return a=b.render,J=0,K=f,a(e,void 0);case Aa:return S(a,b.type,d,c,e,f);case va:return Ia(b._context,e.value),[z,b,d,{value:e.value,children:e.children,__pop:ib}]}}throw Error("Unsupported Server Component type: "+Ya(b));}function kb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,nb(a))}
function hb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return kb(a,e)},thenableState:null};c.add(e);return e}function T(a){return"$"+a.toString(16)}function ob(a,b,d){a=O(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function pb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===z&&"1"===d?"$L"+g.toString(16):T(g);try{var h=a.bundlerConfig,k=c.$$id;g="";var p=h[k];if(p)g=p.name;else{var t=k.lastIndexOf("#");-1!==t&&(g=k.slice(t+1),p=h[k.slice(0,t)]);if(!p)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var E={id:p.id,chunks:p.chunks,name:g,async:!!c.$$async};
a.pendingChunks++;var G=a.nextChunkId++,B=O(E),Hb=G.toString(16)+":I"+B+"\n",Ib=q.encode(Hb);a.completedImportChunks.push(Ib);f.set(e,G);return b[0]===z&&"1"===d?"$L"+G.toString(16):T(G)}catch(Jb){return a.pendingChunks++,b=a.nextChunkId++,d=P(a,Jb),Q(a,b,d),T(b)}}function qb(a,b){a.pendingChunks++;var d=a.nextChunkId++;rb(a,d,b);return d}
function fb(a,b,d,c){switch(c){case z:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===z||c.$$typeof===A);)try{switch(c.$$typeof){case z:var e=c;c=S(a,e.type,e.key,e.ref,e.props,null);break;case A:var f=c._init;c=f(c._payload)}}catch(g){d=g===Ja?Ma():g;if("object"===typeof d&&null!==d&&"function"===typeof d.then)return a.pendingChunks++,a=hb(a,c,C,a.abortableTasks),c=a.ping,d.then(c,c),a.thenableState=Na(),"$L"+a.id.toString(16);a.pendingChunks++;c=a.nextChunkId++;d=P(a,d);Q(a,c,d);return"$L"+
c.toString(16)}if(null===c)return null;if("object"===typeof c){if(c.$$typeof===r)return pb(a,b,d,c);if("function"===typeof c.then)return"$@"+jb(a,c).toString(16);if(c.$$typeof===va)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=ob(a,d,"$P"+c),a.completedRegularChunks.push(c)),T(d);if(c===ib){a=C;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=
c===Ba?a.context._defaultValue:c;C=a.parent;return}return c instanceof Map?"$Q"+qb(a,Array.from(c)).toString(16):c instanceof Set?"$W"+qb(a,Array.from(c)).toString(16):!Wa(c)&&(null===c||"object"!==typeof c?a=null:(a=Da&&c[Da]||c["@@iterator"],a="function"===typeof a?a:null),a)?Array.from(c):c}if("string"===typeof c){if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,c=q.encode(c),b=c.byteLength,b=d.toString(16)+":T"+b.toString(16)+
",",b=q.encode(b),a.completedRegularChunks.push(b,c),T(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){if(c.$$typeof===r)return pb(a,b,d,c);if(c.$$typeof===u)return d=a.writtenServerReferences,b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?
Promise.resolve(b):null},a=qb(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+N(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+N(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return T(f);f=c.description;
if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+N(b,d));a.pendingChunks++;d=a.nextChunkId++;b=ob(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return T(d)}if("bigint"===typeof c)return"$n"+c.toString(10);throw Error("Type "+typeof c+" is not supported in Client Component props."+N(b,d));}
function P(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}function sb(a,b){null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}
function Q(a,b,d){d={digest:d};b=b.toString(16)+":E"+O(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}function rb(a,b,d){d=O(d,a.toJSON);b=b.toString(16)+":"+d+"\n";b=q.encode(b);a.completedRegularChunks.push(b)}
function nb(a){var b=ab.current;ab.current=Sa;var d=w;I=w=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++){var f=c[e];var g=a;if(0===f.status){F(f.context);try{var h=f.model;if("object"===typeof h&&null!==h&&h.$$typeof===z){var k=h,p=f.thenableState;f.model=h;h=S(g,k.type,k.key,k.ref,k.props,p);for(f.thenableState=null;"object"===typeof h&&null!==h&&h.$$typeof===z;)k=h,f.model=h,h=S(g,k.type,k.key,k.ref,k.props,null)}rb(g,f.id,h);g.abortableTasks.delete(f);f.status=1}catch(B){var t=
B===Ja?Ma():B;if("object"===typeof t&&null!==t&&"function"===typeof t.then){var E=f.ping;t.then(E,E);f.thenableState=Na()}else{g.abortableTasks.delete(f);f.status=4;var G=P(g,t);Q(g,f.id,G)}}}}null!==a.destination&&R(a,a.destination)}catch(B){P(a,B),sb(a,B)}finally{ab.current=b,I=null,w=d}}
function R(a,b){l=new Uint8Array(512);m=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,n(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)n(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,n(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,n(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&(b.enqueue(new Uint8Array(l.buffer,0,m)),l=null,m=0)}0===a.pendingChunks&&
b.close()}function tb(a,b){try{var d=a.abortableTasks;if(0<d.size){var c=void 0===b?Error("The render was aborted by the server without a reason."):b,e=P(a,c);a.pendingChunks++;var f=a.nextChunkId++;Q(a,f,e,c);d.forEach(function(g){g.status=3;var h=T(f);g=ob(a,g.id,h);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&R(a,a.destination)}catch(g){P(a,g),sb(a,g)}}
function gb(a){if(a){var b=C;F(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];$a[e]||($a[e]=ba.createServerContext(e,Ba));Ia($a[e],c)}a=C;F(b);return a}return null}function ub(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return{id:c.id,chunks:c.chunks,name:d,async:!1}}var U=new Map;
function vb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function wb(){}
function xb(a){for(var b=a.chunks,d=[],c=0;c<b.length;c++){var e=b[c],f=U.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);d.push(f);var g=U.set.bind(U,e,null);f.then(g,wb);U.set(e,f)}else null!==f&&d.push(f)}return a.async?0===d.length?vb(a.id):Promise.all(d).then(function(){return vb(a.id)}):0<d.length?Promise.all(d):null}
function V(a){var b=globalThis.__next_require__(a.id);if(a.async&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a.name?b:""===a.name?b.__esModule?b.default:b:b[a.name]}function W(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}W.prototype=Object.create(Promise.prototype);
W.prototype.then=function(a,b){switch(this.status){case "resolved_model":yb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function zb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Ab(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&zb(d,b)}}function Bb(a,b,d,c,e,f){var g=ub(a._bundlerConfig,b);a=xb(g);if(d)d=Promise.all([d,a]).then(function(h){h=h[0];var k=V(g);return k.bind.apply(k,[null].concat(h))});else if(a)d=Promise.resolve(a).then(function(){return V(g)});else return V(g);d.then(Cb(c,e,f),Db(c));return null}var X=null,Y=null;
function yb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Eb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Ab(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new W("resolved_model",c,null,a):new W("pending",null,null,a),d.set(b,c));return c}function Cb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&zb(e,c.value))}}function Db(a){return function(b){return Ab(a,b)}}
function Fb(a,b){a=Z(a,b);"resolved_model"===a.status&&yb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Gb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Fb(a,c),Bb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Fb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Fb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,h){h.startsWith(e)&&f.append(h.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":yb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Cb(c,b,d),Db(c)),null;default:throw a.reason;}}return c}
function Kb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Gb(e,this,f,g):g}};return e}function Lb(a){Eb(a,Error("Connection closed."))}function Mb(a,b,d){var c=ub(a,b);a=xb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=V(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return V(c)}):Promise.resolve(V(c))}
function Nb(a,b,d){a=Kb(b,d,a);Lb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=v({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Nb(a,b,e),c=Mb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=Mb(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(g,h){h.startsWith("$ACTION_REF_")&&(g="$ACTION_"+h.slice(12)+":",e=Nb(b,d,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,c,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Kb(b,"",a);Lb(a);return Z(a,0)};
exports.registerClientReference=function(a,b,d){return v(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:u},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=eb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)tb(c,e.reason);else{var f=function(){tb(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){c.flushScheduled=null!==c.destination;nb(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===
c.destination){c.destination=g;try{R(c,g)}catch(h){P(c,h),sb(c,h)}}},cancel:function(){}},{highWaterMark:0})};
