{"version": 3, "sources": ["../../src/build/load-entrypoint.ts"], "names": ["fs", "path", "PACKAGE_ROOT", "normalize", "join", "__dirname", "TEMPLATE_FOLDER", "TEMPLATES_ESM_FOLDER", "loadEntrypoint", "entrypoint", "replacements", "injections", "filepath", "resolve", "file", "readFile", "count", "replaceAll", "_", "fromRequest", "importRequest", "relative", "replace", "startsWith", "Error", "JSON", "stringify", "replaced", "Set", "RegExp", "Object", "keys", "map", "k", "match", "key", "parse", "add", "matches", "size", "length", "difference", "filter", "has", "injected"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AAEvB,6DAA6D;AAC7D,MAAMC,eAAeD,KAAKE,SAAS,CAACF,KAAKG,IAAI,CAACC,WAAW;AACzD,MAAMC,kBAAkBL,KAAKG,IAAI,CAACC,WAAW;AAC7C,MAAME,uBAAuBN,KAAKE,SAAS,CACzCF,KAAKG,IAAI,CAACC,WAAW;AAGvB;;;;;;;;;;;;;CAaC,GAED,OAAO,eAAeG,eACpBC,UAA4D,EAC5DC,YAA6C,EAC7CC,UAAmC;IAEnC,MAAMC,WAAWX,KAAKY,OAAO,CAC3BZ,KAAKG,IAAI,CAACG,sBAAsB,CAAC,EAAEE,WAAW,GAAG,CAAC;IAGpD,IAAIK,OAAO,MAAMd,GAAGe,QAAQ,CAACH,UAAU;IAEvC,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAII,QAAQ;IACZF,OAAOA,KAAKG,UAAU,CACpB,sCACA,SAAUC,CAAC,EAAEC,WAAW,EAAEC,aAAa;QACrCJ;QAEA,MAAMK,WAAWpB,KACdoB,QAAQ,CACPnB,cACAD,KAAKY,OAAO,CAACP,iBAAiBa,eAAeC,eAE/C,2DAA2D;SAC1DE,OAAO,CAAC,OAAO;QAElB,0EAA0E;QAC1E,uEAAuE;QACvE,oCAAoC;QACpC,IAAI,CAACD,SAASE,UAAU,CAAC,UAAU;YACjC,MAAM,IAAIC,MACR,CAAC,kEAAkE,EAAEH,SAAS,CAAC,CAAC;QAEpF;QAEA,OAAOF,cACH,CAAC,KAAK,EAAEM,KAAKC,SAAS,CAACL,UAAU,CAAC,GAClC,CAAC,OAAO,EAAEI,KAAKC,SAAS,CAACL,UAAU,CAAC;IAC1C;IAGF,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,iBAAiB;IACjB,IAAIL,UAAU,GAAG;QACf,MAAM,IAAIQ,MAAM;IAClB;IAEA,MAAMG,WAAW,IAAIC;IAErB,2EAA2E;IAC3E,uCAAuC;IACvCd,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OACF,CAAC,EAAEC,OAAOC,IAAI,CAACrB,cACZsB,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnB7B,IAAI,CAAC,KAAK,CAAC,EACd,MAEF,CAAC8B;QACC,MAAMC,MAAMV,KAAKW,KAAK,CAACF;QAEvB,IAAI,CAAEC,CAAAA,OAAOzB,YAAW,GAAI;YAC1B,MAAM,IAAIc,MAAM,CAAC,wCAAwC,EAAEW,IAAI,CAAC;QAClE;QAEAR,SAASU,GAAG,CAACF;QAEb,OAAOV,KAAKC,SAAS,CAAChB,YAAY,CAACyB,IAAI;IACzC;IAGF,4DAA4D;IAC5D,IAAIG,UAAUxB,KAAKoB,KAAK,CAAC;IACzB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,6DAA6D,EAAEc,QAAQlC,IAAI,CAC1E,MACA,CAAC;IAEP;IAEA,mEAAmE;IACnE,IAAIuB,SAASY,IAAI,KAAKT,OAAOC,IAAI,CAACrB,cAAc8B,MAAM,EAAE;QACtD,yEAAyE;QACzE,uEAAuE;QACvE,kDAAkD;QAClD,MAAMC,aAAaX,OAAOC,IAAI,CAACrB,cAAcgC,MAAM,CACjD,CAACP,MAAQ,CAACR,SAASgB,GAAG,CAACR;QAGzB,MAAM,IAAIX,MACR,CAAC,+DAA+D,EAAEiB,WAAWrC,IAAI,CAC/E,MACA,YAAY,CAAC;IAEnB;IAEA,0BAA0B;IAC1B,MAAMwC,WAAW,IAAIhB;IACrB,IAAIjB,YAAY;QACd,iEAAiE;QACjEG,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OAAO,CAAC,WAAW,EAAEC,OAAOC,IAAI,CAACpB,YAAYP,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAC/D,CAACc,GAAGiB;YACF,IAAI,CAAEA,CAAAA,OAAOxB,UAAS,GAAI;gBACxB,MAAM,IAAIa,MAAM,CAAC,gCAAgC,EAAEW,IAAI,CAAC;YAC1D;YAEAS,SAASP,GAAG,CAACF;YAEb,OAAO,CAAC,MAAM,EAAEA,IAAI,GAAG,EAAExB,UAAU,CAACwB,IAAI,CAAC,CAAC;QAC5C;IAEJ;IAEA,oDAAoD;IACpDG,UAAUxB,KAAKoB,KAAK,CAAC;IACrB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,oDAAoD,EAAEc,QAAQlC,IAAI,CACjE,MACA,CAAC;IAEP;IAEA,2DAA2D;IAC3D,IAAIwC,SAASL,IAAI,KAAKT,OAAOC,IAAI,CAACpB,cAAc,CAAC,GAAG6B,MAAM,EAAE;QAC1D,uEAAuE;QACvE,2EAA2E;QAC3E,8BAA8B;QAC9B,MAAMC,aAAaX,OAAOC,IAAI,CAACpB,cAAc,CAAC,GAAG+B,MAAM,CACrD,CAACP,MAAQ,CAACS,SAASD,GAAG,CAACR;QAGzB,MAAM,IAAIX,MACR,CAAC,sDAAsD,EAAEiB,WAAWrC,IAAI,CACtE,MACA,YAAY,CAAC;IAEnB;IAEA,OAAOU;AACT"}