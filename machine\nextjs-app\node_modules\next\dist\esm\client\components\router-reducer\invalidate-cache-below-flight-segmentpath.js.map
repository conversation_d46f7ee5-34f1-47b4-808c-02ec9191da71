{"version": 3, "sources": ["../../../../src/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.ts"], "names": ["createRouterCache<PERSON>ey", "invalidateCacheBelowFlightSegmentPath", "newCache", "existingCache", "flightSegmentPath", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "delete", "existingChildCacheNode", "childCacheNode", "status", "data", "subTreeData", "slice"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,sCACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC;IAEpC,MAAMC,cAAcD,kBAAkBE,MAAM,IAAI;IAChD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGJ;IAEpC,MAAMK,WAAWT,qBAAqBQ;IAEtC,MAAME,0BACJP,cAAcQ,cAAc,CAACC,GAAG,CAACL;IAEnC,IAAI,CAACG,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBX,SAASS,cAAc,CAACC,GAAG,CAACL;IAClD,IAAI,CAACM,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BR,SAASS,cAAc,CAACI,GAAG,CAACR,kBAAkBM;IAChD;IAEA,iDAAiD;IACjD,IAAIR,aAAa;QACfQ,gBAAgBG,MAAM,CAACP;QACvB;IACF;IAEA,MAAMQ,yBAAyBP,wBAAwBE,GAAG,CAACH;IAC3D,IAAIS,iBAAiBL,gBAAgBD,GAAG,CAACH;IAEzC,IAAI,CAACS,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,QAAQD,eAAeC,MAAM;YAC7BC,MAAMF,eAAeE,IAAI;YACzBC,aAAaH,eAAeG,WAAW;YACvCV,gBAAgB,IAAIG,IAAII,eAAeP,cAAc;QACvD;QACAE,gBAAgBE,GAAG,CAACN,UAAUS;IAChC;IAEAjB,sCACEiB,gBACAD,wBACAb,kBAAkBkB,KAAK,CAAC;AAE5B"}