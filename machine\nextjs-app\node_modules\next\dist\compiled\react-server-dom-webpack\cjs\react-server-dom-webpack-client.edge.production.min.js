/**
 * @license React
 * react-server-dom-webpack-client.edge.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var r=require("react-dom"),t=require("react"),u={stream:!0};function v(a,b){if(a){var c=a[b.id];if(a=c[b.name])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b.id+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b.name}return{id:a.id,chunks:a.chunks,name:c,async:!!b.async}}return b}var w=new Map;
function x(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function y(){}
function z(a){for(var b=a.chunks,c=[],d=0;d<b.length;d++){var g=b[d],l=w.get(g);if(void 0===l){l=globalThis.__next_chunk_load__(g);c.push(l);var n=w.set.bind(w,g,null);l.then(n,y);w.set(g,l)}else null!==l&&c.push(l)}return a.async?0===c.length?x(a.id):Promise.all(c).then(function(){return x(a.id)}):0<c.length?Promise.all(c):null}var A=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),C=Symbol.for("react.lazy"),aa=Symbol.for("react.default_value"),E=Symbol.iterator;
function ba(a){if(null===a||"object"!==typeof a)return null;a=E&&a[E]||a["@@iterator"];return"function"===typeof a?a:null}var ca=Array.isArray,F=new WeakMap;function da(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ea(a,b,c,d){function g(m,e){if(null===e)return null;if("object"===typeof e){if("function"===typeof e.then){null===h&&(h=new FormData);n++;var k=l++;e.then(function(p){p=JSON.stringify(p,g);var q=h;q.append(b+k,p);n--;0===n&&c(q)},function(p){d(p)});return"$@"+k.toString(16)}if(e instanceof FormData){null===h&&(h=new FormData);var f=h;m=l++;var D=b+m+"_";e.forEach(function(p,q){f.append(D+q,p)});return"$K"+m.toString(16)}return e instanceof Map?(e=JSON.stringify(Array.from(e),g),null===h&&
(h=new FormData),m=l++,h.append(b+m,e),"$Q"+m.toString(16)):e instanceof Set?(e=JSON.stringify(Array.from(e),g),null===h&&(h=new FormData),m=l++,h.append(b+m,e),"$W"+m.toString(16)):!ca(e)&&ba(e)?Array.from(e):e}if("string"===typeof e){if("Z"===e[e.length-1]&&this[m]instanceof Date)return"$D"+e;e="$"===e[0]?"$"+e:e;return e}if("boolean"===typeof e)return e;if("number"===typeof e)return da(e);if("undefined"===typeof e)return"$undefined";if("function"===typeof e){e=F.get(e);if(void 0!==e)return e=JSON.stringify(e,
g),null===h&&(h=new FormData),m=l++,h.set(b+m,e),"$F"+m.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof e){m=e.description;if(Symbol.for(m)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+(e.description+") cannot be found among global symbols."));return"$S"+m}if("bigint"===typeof e)return"$n"+
e.toString(10);throw Error("Type "+typeof e+" is not supported as an argument to a Server Function.");}var l=1,n=0,h=null;a=JSON.stringify(a,g);null===h?c(a):(h.set(b+"0",a),0===n&&c(h))}var G=new WeakMap;function fa(a){var b,c,d=new Promise(function(g,l){b=g;c=l});ea(a,"",function(g){if("string"===typeof g){var l=new FormData;l.append("0",g);g=l}d.status="fulfilled";d.value=g;b(g)},function(g){d.status="rejected";d.reason=g;c(g)});return d}
function ha(a){var b=F.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=G.get(b);c||(c=fa(b),G.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(g,l){d.append("$ACTION_"+a+":"+l,g)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function ia(a,b){var c=F.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(g){d.status="fulfilled";d.value=g},function(g){d.status="rejected";d.reason=g})),d;}}
function H(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:ha},$$IS_SIGNATURE_EQUAL:{value:ia},bind:{value:ja}});F.set(a,b)}var ka=Function.prototype.bind,la=Array.prototype.slice;function ja(){var a=ka.apply(this,arguments),b=F.get(this);if(b){var c=la.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(g){return g.concat(c)}):Promise.resolve(c);H(a,{id:b.id,bound:d})}return a}
function ma(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}H(c,{id:a,bound:null});return c}var I=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function K(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}K.prototype=Object.create(Promise.prototype);
K.prototype.then=function(a,b){switch(this.status){case "resolved_model":L(this);break;case "resolved_module":M(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function na(a){switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function N(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function O(a,b,c){switch(a.status){case "fulfilled":N(b,a.value);break;case "pending":case "blocked":a.value=b;a.reason=c;break;case "rejected":c&&N(c,a.reason)}}
function P(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&N(c,b)}}function Q(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(M(a),O(a,c,d))}}var R=null,T=null;
function L(a){var b=R,c=T;R=a;T=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==T&&0<T.deps?(T.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(g){a.status="rejected",a.reason=g}finally{R=b,T=c}}
function M(a){try{var b=a.value,c=globalThis.__next_require__(b.id);if(b.async&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b.name?c:""===b.name?c.__esModule?c.default:c:c[b.name];a.status="fulfilled";a.value=d}catch(g){a.status="rejected",a.reason=g}}function U(a,b){a._chunks.forEach(function(c){"pending"===c.status&&P(c,b)})}function V(a,b){var c=a._chunks,d=c.get(b);d||(d=new K("pending",null,null,a),c.set(b,d));return d}
function oa(a,b,c){if(T){var d=T;d.deps++}else d=T={deps:1,value:null};return function(g){b[c]=g;d.deps--;0===d.deps&&"blocked"===a.status&&(g=a.value,a.status="fulfilled",a.value=d.value,null!==g&&N(g,d.value))}}function pa(a){return function(b){return P(a,b)}}
function qa(a,b){function c(){var g=Array.prototype.slice.call(arguments),l=b.bound;return l?"fulfilled"===l.status?d(b.id,l.value.concat(g)):Promise.resolve(l).then(function(n){return d(b.id,n.concat(g))}):d(b.id,g)}var d=a._callServer;H(c,b);return c}function W(a,b){a=V(a,b);switch(a.status){case "resolved_model":L(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ra(a,b,c,d){if("$"===d[0]){if("$"===d)return B;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=V(a,b),{$$typeof:C,_payload:a,_init:na};case "@":return b=parseInt(d.slice(2),16),V(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),I[a]||(I[a]=t.createServerContext(a,aa)),I[a].Provider;case "F":return b=parseInt(d.slice(2),16),b=W(a,b),qa(a,b);case "Q":return b=parseInt(d.slice(2),16),a=W(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),
16),a=W(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=V(a,d);switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=R,a.then(oa(d,b,c),pa(d)),null;default:throw a.reason;}}}return d}
function sa(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function X(a,b){var c=new Map;a={_bundlerConfig:a,_callServer:void 0!==b?b:sa,_chunks:c,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=ta(a);return a}
function ua(a,b,c){var d=a._chunks,g=d.get(b);c=JSON.parse(c,a._fromJSON);var l=v(a._bundlerConfig,c);if(c=z(l)){if(g){var n=g;n.status="blocked"}else n=new K("blocked",null,null,a),d.set(b,n);c.then(function(){return Q(n,l)},function(h){return P(n,h)})}else g?Q(g,l):d.set(b,new K("resolved_module",l,null,a))}
function ta(a){return function(b,c){return"string"===typeof c?ra(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===B?{$$typeof:B,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function Y(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
function Z(a,b){function c(l){var n=l.value;if(l.done)U(a,Error("Connection closed."));else{var h=0,m=a._rowState,e=a._rowID,k=a._rowTag,f=a._rowLength;l=a._buffer;for(var D=n.length;h<D;){var p=-1;switch(m){case 0:p=n[h++];58===p?m=1:e=e<<4|(96<p?p-87:p-48);continue;case 1:m=n[h];84===m?(k=m,m=2,h++):64<m&&91>m?(k=m,m=3,h++):(k=0,m=3);continue;case 2:p=n[h++];44===p?m=4:f=f<<4|(96<p?p-87:p-48);continue;case 3:p=n.indexOf(10,h);break;case 4:p=h+f,p>n.length&&(p=-1)}var q=n.byteOffset+h;if(-1<p){h=
new Uint8Array(n.buffer,q,p-h);f=a;q=k;var S=f._stringDecoder;k="";for(var J=0;J<l.length;J++)k+=S.decode(l[J],u);k+=S.decode(h);switch(q){case 73:ua(f,e,k);break;case 72:e=k[0];k=k.slice(1);f=JSON.parse(k,f._fromJSON);if(k=A.current)switch(e){case "D":k.prefetchDNS(f);break;case "C":"string"===typeof f?k.preconnect(f):k.preconnect(f[0],f[1]);break;case "L":e=f[0];h=f[1];3===f.length?k.preload(e,h,f[2]):k.preload(e,h);break;case "m":"string"===typeof f?k.preloadModule(f):k.preloadModule(f[0],f[1]);
break;case "S":"string"===typeof f?k.preinitStyle(f):k.preinitStyle(f[0],0===f[1]?void 0:f[1],3===f.length?f[2]:void 0);break;case "X":"string"===typeof f?k.preinitScript(f):k.preinitScript(f[0],f[1]);break;case "M":"string"===typeof f?k.preinitModuleScript(f):k.preinitModuleScript(f[0],f[1])}break;case 69:k=JSON.parse(k);h=k.digest;k=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
k.stack="Error: "+k.message;k.digest=h;h=f._chunks;(q=h.get(e))?P(q,k):h.set(e,new K("rejected",null,k,f));break;case 84:f._chunks.set(e,new K("fulfilled",k,null,f));break;default:h=f._chunks,(q=h.get(e))?(f=q,e=k,"pending"===f.status&&(k=f.value,h=f.reason,f.status="resolved_model",f.value=e,null!==k&&(L(f),O(f,k,h)))):h.set(e,new K("resolved_model",k,null,f))}h=p;3===m&&h++;f=e=k=m=0;l.length=0}else{n=new Uint8Array(n.buffer,q,n.byteLength-h);l.push(n);f-=n.byteLength;break}}a._rowState=m;a._rowID=
e;a._rowTag=k;a._rowLength=f;return g.read().then(c).catch(d)}}function d(l){U(a,l)}var g=b.getReader();g.read().then(c).catch(d)}exports.createFromFetch=function(a,b){var c=X(b&&b.moduleMap?b.moduleMap:null,Y);a.then(function(d){Z(c,d.body)},function(d){U(c,d)});return V(c,0)};exports.createFromReadableStream=function(a,b){b=X(b&&b.moduleMap?b.moduleMap:null,Y);Z(b,a);return V(b,0)};exports.createServerReference=function(a){return ma(a,Y)};
