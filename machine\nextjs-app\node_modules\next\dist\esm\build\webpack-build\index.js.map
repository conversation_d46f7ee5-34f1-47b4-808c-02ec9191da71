{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["Log", "NextBuildContext", "Worker", "origDebug", "path", "debug", "webpackBuildWithWorker", "config", "telemetryPlugin", "buildSpinner", "nextBuildSpan", "prunedBuildContext", "getWorker", "compilerName", "_worker", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "getStderr", "pipe", "stderr", "getStdout", "stdout", "worker", "_workerPool", "_workers", "_child", "on", "code", "signal", "console", "error", "combinedResult", "duration", "turbotraceContext", "ORDERED_COMPILER_NAMES", "curR<PERSON>ult", "worker<PERSON>ain", "buildContext", "end", "pluginState", "serializedPagesManifestEntries", "edgeServerAppPaths", "edgeServerPages", "nodeServerAppPaths", "nodeServerPages", "entriesTrace", "entryNameMap", "Map", "stopAndPersist", "event", "webpackBuild", "experimental", "webpackBuildWorker", "webpackBuildImpl", "require"], "mappings": "AACA,YAAYA,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,SAASC,MAAM,QAAQ,iCAAgC;AACvD,OAAOC,eAAe,2BAA0B;AAEhD,OAAOC,UAAU,OAAM;AAEvB,MAAMC,QAAQF,UAAU;AAExB,eAAeG;IACb,MAAM,EACJC,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,aAAa,EACb,GAAGC,oBACJ,GAAGV;IAEJ,MAAMW,YAAY,CAACC;YAeK;QAdtB,MAAMC,UAAU,IAAIZ,OAAOE,KAAKW,IAAI,CAACC,WAAW,YAAY;YAC1DC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QACAT,QAAQU,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACvCZ,QAAQa,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEvC,KAAK,MAAMC,UAAW,EAAA,sBAAA,AAACf,QAAgBgB,WAAW,qBAA5B,oBAA8BC,QAAQ,KAAI,EAAE,CAE7D;YACHF,OAAOG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC,MAAMC;gBAC9B,IAAID,QAAQC,QAAQ;oBAClBC,QAAQC,KAAK,CACX,CAAC,SAAS,EAAExB,aAAa,gCAAgC,EAAEqB,KAAK,aAAa,EAAEC,OAAO,CAAC;gBAE3F;YACF;QACF;QAEA,OAAOrB;IACT;IAEA,MAAMwB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IACA,qBAAqB;IACrB,MAAMC,yBAAyB;QAC7B;QACA;QACA;KACD;IAED,KAAK,MAAM5B,gBAAgB4B,uBAAwB;YAe7CC,2CAEAA,4CAEAA,4CAEAA,4CAKAA;QAzBJ,MAAMb,SAASjB,UAAUC;QAEzB,MAAM6B,YAAY,MAAMb,OAAOc,UAAU,CAAC;YACxCC,cAAcjC;YACdE;QACF;QACA,0DAA0D;QAC1D,MAAMgB,OAAOgB,GAAG;QAEhB,sBAAsB;QACtBlC,mBAAmBmC,WAAW,GAAGJ,UAAUI,WAAW;QAEtDnC,mBAAmBoC,8BAA8B,GAAG;YAClDC,kBAAkB,GAChBN,4CAAAA,UAAUK,8BAA8B,qBAAxCL,0CAA0CM,kBAAkB;YAC9DC,eAAe,GACbP,6CAAAA,UAAUK,8BAA8B,qBAAxCL,2CAA0CO,eAAe;YAC3DC,kBAAkB,GAChBR,6CAAAA,UAAUK,8BAA8B,qBAAxCL,2CAA0CQ,kBAAkB;YAC9DC,eAAe,GACbT,6CAAAA,UAAUK,8BAA8B,qBAAxCL,2CAA0CS,eAAe;QAC7D;QAEAb,eAAeC,QAAQ,IAAIG,UAAUH,QAAQ;QAE7C,KAAIG,+BAAAA,UAAUF,iBAAiB,qBAA3BE,6BAA6BU,YAAY,EAAE;YAC7Cd,eAAeE,iBAAiB,GAAGE,UAAUF,iBAAiB;YAE9D,MAAM,EAAEa,YAAY,EAAE,GAAGf,eAAeE,iBAAiB,CAACY,YAAY;YACtE,IAAIC,cAAc;gBAChBf,eAAeE,iBAAiB,CAACY,YAAY,CAAEC,YAAY,GAAG,IAAIC,IAChED;YAEJ;QACF;IACF;IACA5C,gCAAAA,aAAc8C,cAAc;IAC5BvD,IAAIwD,KAAK,CAAC;IAEV,OAAOlB;AACT;AAEA,OAAO,eAAemB;IACpB,MAAMlD,SAASN,iBAAiBM,MAAM;IAEtC,IAAIA,OAAOmD,YAAY,CAACC,kBAAkB,EAAE;QAC1CtD,MAAM;QACN,OAAO,MAAMC;IACf,OAAO;QACLD,MAAM;QACN,MAAMuD,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAO,MAAMA;IACf;AACF"}