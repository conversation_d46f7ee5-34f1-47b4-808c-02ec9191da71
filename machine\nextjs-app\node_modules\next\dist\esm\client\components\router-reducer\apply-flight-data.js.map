{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-flight-data.ts"], "names": ["CacheStates", "fillLazyItemsTillLeafWithHead", "fillCacheWithNewSubTreeData", "applyFlightData", "existingCache", "cache", "flightDataPath", "wasPrefetched", "treePatch", "subTreeData", "head", "slice", "length", "status", "READY", "parallelRoutes", "Map"], "mappings": "AAAA,SAEEA,WAAW,QACN,wDAAuD;AAE9D,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,2BAA2B,QAAQ,qCAAoC;AAEhF,OAAO,SAASC,gBACdC,aAAwB,EACxBC,KAAgB,EAChBC,cAA8B,EAC9BC,aAA8B;IAA9BA,IAAAA,0BAAAA,gBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,CAACC,WAAWC,aAAaC,KAAK,GAAGJ,eAAeK,KAAK,CAAC,CAAC;IAE7D,8FAA8F;IAC9F,IAAIF,gBAAgB,MAAM;QACxB,OAAO;IACT;IAEA,IAAIH,eAAeM,MAAM,KAAK,GAAG;QAC/BP,MAAMQ,MAAM,GAAGb,YAAYc,KAAK;QAChCT,MAAMI,WAAW,GAAGA;QACpBR,8BACEI,OACAD,eACAI,WACAE,MACAH;IAEJ,OAAO;QACL,mDAAmD;QACnDF,MAAMQ,MAAM,GAAGb,YAAYc,KAAK;QAChCT,MAAMI,WAAW,GAAGL,cAAcK,WAAW;QAC7CJ,MAAMU,cAAc,GAAG,IAAIC,IAAIZ,cAAcW,cAAc;QAC3D,oEAAoE;QACpEb,4BACEG,OACAD,eACAE,gBACAC;IAEJ;IAEA,OAAO;AACT"}