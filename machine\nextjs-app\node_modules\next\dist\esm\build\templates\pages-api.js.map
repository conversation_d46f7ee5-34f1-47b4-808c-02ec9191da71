{"version": 3, "sources": ["../../../src/build/templates/pages-api.ts"], "names": ["module", "RouteKind", "hoist", "PagesAPIRouteModule", "userland", "config", "routeModule", "definition", "kind", "PAGES_API", "page", "pathname", "bundlePath", "filename"], "mappings": "AAAA,oEAAoE;AACpE,YAAYA,YAAY,kEAAiE;AAEzF,SAASC,SAAS,QAAQ,iCAAgC;AAC1D,SAASC,KAAK,QAAQ,YAAW;AAEjC,MAAMC,sBACJH,OAAOG,mBAAmB;AAE5B,4BAA4B;AAC5B,0DAA0D;AAC1D,YAAYC,cAAc,eAAc;AAExC,wDAAwD;AACxD,eAAeF,MAAME,UAAU,WAAU;AAEzC,oBAAoB;AACpB,OAAO,MAAMC,SAASH,MAAME,UAAU,UAAS;AAE/C,4DAA4D;AAC5D,OAAO,MAAME,cAAc,IAAIH,oBAAoB;IACjDI,YAAY;QACVC,MAAMP,UAAUQ,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAT;AACF,GAAE"}