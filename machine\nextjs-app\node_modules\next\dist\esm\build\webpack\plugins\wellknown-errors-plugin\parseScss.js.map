{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseScss.ts"], "names": ["Chalk", "SimpleWebpackError", "chalk", "constructor", "enabled", "regexScssError", "getScssError", "fileName", "fileContent", "err", "name", "res", "exec", "message", "reason", "_lineNumer", "<PERSON><PERSON><PERSON><PERSON>", "columnString", "lineNumber", "Math", "max", "parseInt", "column", "length", "frame", "codeFrameColumns", "require", "start", "line", "forceColor", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": "AAAA,OAAOA,WAAW,2BAA0B;AAC5C,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,MAAMC,QAAQ,IAAIF,MAAMG,WAAW,CAAC;IAAEC,SAAS;AAAK;AACpD,MAAMC,iBACJ;AAEF,OAAO,SAASC,aACdC,QAAgB,EAChBC,WAA0B,EAC1BC,GAAU;IAEV,IAAIA,IAAIC,IAAI,KAAK,aAAa;QAC5B,OAAO;IACT;IAEA,MAAMC,MAAMN,eAAeO,IAAI,CAACH,IAAII,OAAO;IAC3C,IAAIF,KAAK;QACP,MAAM,GAAGG,QAAQC,YAAYC,aAAaC,aAAa,GAAGN;QAC1D,MAAMO,aAAaC,KAAKC,GAAG,CAAC,GAAGC,SAASN,YAAY;QACpD,MAAMO,SAASL,CAAAA,gCAAAA,aAAcM,MAAM,KAAI;QAEvC,IAAIC;QACJ,IAAIhB,aAAa;YACf,IAAI;gBACF,MAAM,EACJiB,gBAAgB,EACjB,GAAGC,QAAQ;gBACZF,QAAQC,iBACNjB,aACA;oBAAEmB,OAAO;wBAAEC,MAAMV;wBAAYI;oBAAO;gBAAE,GACtC;oBAAEO,YAAY;gBAAK;YAEvB,EAAE,OAAM,CAAC;QACX;QAEA,OAAO,IAAI5B,mBACT,CAAC,EAAEC,MAAM4B,IAAI,CAACvB,UAAU,CAAC,EAAEL,MAAM6B,MAAM,CACrCb,WAAWc,QAAQ,IACnB,CAAC,EAAE9B,MAAM6B,MAAM,CAACT,OAAOU,QAAQ,IAAI,CAAC,EACtC9B,MAAM+B,GAAG,CACNC,IAAI,CAAC,gBACLC,MAAM,CAAC,CAAC,EAAE,EAAErB,OAAO,IAAI,EAAEU,SAASR,YAAY,CAAC;IAEtD;IAEA,OAAO;AACT"}