{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseCss.ts"], "names": ["Chalk", "SimpleWebpackError", "chalk", "constructor", "enabled", "regexCssError", "getCssError", "fileName", "err", "name", "stack", "SyntaxError", "res", "exec", "message", "_lineNumber", "_column", "reason", "lineNumber", "Math", "max", "parseInt", "column", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": "AAAA,OAAOA,WAAW,2BAA0B;AAC5C,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,MAAMC,QAAQ,IAAIF,MAAMG,WAAW,CAAC;IAAEC,SAAS;AAAK;AACpD,MAAMC,gBACJ;AAEF,OAAO,SAASC,YACdC,QAAgB,EAChBC,GAAU;IAEV,IACE,CACE,CAAA,AAACA,CAAAA,IAAIC,IAAI,KAAK,oBAAoBD,IAAIC,IAAI,KAAK,aAAY,KAC3D,AAACD,IAAYE,KAAK,KAAK,SACvB,CAAEF,CAAAA,eAAeG,WAAU,CAAC,GAE9B;QACA,OAAO;IACT;IAEA,uGAAuG;IAEvG,MAAMC,MAAMP,cAAcQ,IAAI,CAACL,IAAIM,OAAO;IAC1C,IAAIF,KAAK;QACP,MAAM,GAAGG,aAAaC,SAASC,OAAO,GAAGL;QACzC,MAAMM,aAAaC,KAAKC,GAAG,CAAC,GAAGC,SAASN,aAAa;QACrD,MAAMO,SAASH,KAAKC,GAAG,CAAC,GAAGC,SAASL,SAAS;QAE7C,OAAO,IAAIf,mBACT,CAAC,EAAEC,MAAMqB,IAAI,CAAChB,UAAU,CAAC,EAAEL,MAAMsB,MAAM,CACrCN,WAAWO,QAAQ,IACnB,CAAC,EAAEvB,MAAMsB,MAAM,CAACF,OAAOG,QAAQ,IAAI,CAAC,EACtCvB,MAAMwB,GAAG,CAACC,IAAI,CAAC,gBAAgBC,MAAM,CAAC,CAAC,EAAE,EAAEX,OAAO,CAAC;IAEvD;IAEA,OAAO;AACT"}