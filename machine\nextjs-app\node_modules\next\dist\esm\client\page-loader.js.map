{"version": 3, "sources": ["../../src/client/page-loader.ts"], "names": ["addBasePath", "interpolateAs", "getAssetPathFromRoute", "addLocale", "isDynamicRoute", "parseRelativeUrl", "removeTrailingSlash", "createRouteLoader", "getClientBuildManifest", "DEV_CLIENT_PAGES_MANIFEST", "DEV_MIDDLEWARE_MANIFEST", "<PERSON><PERSON><PERSON><PERSON>", "getPageList", "process", "env", "NODE_ENV", "then", "manifest", "sortedPages", "window", "__DEV_PAGES_MANIFEST", "pages", "promisedDevPagesManifest", "fetch", "assetPrefix", "res", "json", "catch", "err", "console", "log", "Error", "getMiddleware", "middlewareMatchers", "__NEXT_MIDDLEWARE_MATCHERS", "__MIDDLEWARE_MATCHERS", "undefined", "__DEV_MIDDLEWARE_MATCHERS", "promisedMiddlewareMatchers", "buildId", "matchers", "getDataHref", "params", "<PERSON><PERSON><PERSON>", "href", "locale", "pathname", "hrefPathname", "query", "search", "asPathname", "route", "getHrefForSlug", "path", "dataRoute", "skipInterpolation", "result", "_isSsg", "promisedSsgManifest", "has", "loadPage", "routeLoader", "loadRoute", "page", "component", "mod", "exports", "styleSheets", "styles", "map", "o", "text", "content", "error", "prefetch", "constructor", "Promise", "resolve", "__SSG_MANIFEST", "__SSG_MANIFEST_CB"], "mappings": "AAGA,SAASA,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,aAAa,QAAQ,4CAA2C;AACzE,OAAOC,2BAA2B,uDAAsD;AACxF,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,iBAAgB;AAC1E,SACEC,yBAAyB,EACzBC,uBAAuB,QAClB,0BAAyB;AAkBjB,MAAMC;IA0BnBC,cAAc;QACZ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,OAAOP,yBAAyBQ,IAAI,CAAC,CAACC,WAAaA,SAASC,WAAW;QACzE,OAAO;YACL,IAAIC,OAAOC,oBAAoB,EAAE;gBAC/B,OAAOD,OAAOC,oBAAoB,CAACC,KAAK;YAC1C,OAAO;gBACL,IAAI,CAACC,6BAAL,IAAI,CAACA,2BAA6BC,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,+BAA4Bf,2BAE/CO,IAAI,CAAC,CAACS,MAAQA,IAAIC,IAAI,IACtBV,IAAI,CAAC,CAACC;oBACLE,OAAOC,oBAAoB,GAAGH;oBAC9B,OAAOA,SAASI,KAAK;gBACvB,GACCM,KAAK,CAAC,CAACC;oBACNC,QAAQC,GAAG,CAAE,qCAAoCF;oBACjD,MAAM,IAAIG,MACR,AAAC,0FACC;gBAEN;gBACF,OAAO,IAAI,CAACT,wBAAwB;YACtC;QACF;IACF;IAEAU,gBAAgB;QACd,IAAInB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAMkB,qBAAqBpB,QAAQC,GAAG,CAACoB,0BAA0B;YACjEf,OAAOgB,qBAAqB,GAAGF,qBAC1BA,qBACDG;YACJ,OAAOjB,OAAOgB,qBAAqB;QACrC,OAAO;YACL,IAAIhB,OAAOkB,yBAAyB,EAAE;gBACpC,OAAOlB,OAAOkB,yBAAyB;YACzC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACC,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGf,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACe,OAAO,GAAC,MAAG7B,yBAEnDM,IAAI,CAAC,CAACS,MAAQA,IAAIC,IAAI,IACtBV,IAAI,CAAC,CAACwB;wBACLrB,OAAOkB,yBAAyB,GAAGG;wBACnC,OAAOA;oBACT,GACCb,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACU,0BAA0B;YACxC;QACF;IACF;IAEAG,YAAYC,MAKX,EAAU;QACT,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE,GAAGH;QACjC,MAAM,EAAEI,UAAUC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAG5C,iBAAiBuC;QACnE,MAAM,EAAEE,UAAUI,UAAU,EAAE,GAAG7C,iBAAiBsC;QAClD,MAAMQ,QAAQ7C,oBAAoByC;QAClC,IAAII,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,MAAM,IAAIpB,MAAM,AAAC,8CAA2CoB,QAAM;QACpE;QAEA,MAAMC,iBAAiB,CAACC;YACtB,MAAMC,YAAYpD,sBAChBI,oBAAoBH,UAAUkD,MAAMR,UACpC;YAEF,OAAO7C,YACL,AAAC,iBAAc,IAAI,CAACuC,OAAO,GAAGe,YAAYL,QAC1C;QAEJ;QAEA,OAAOG,eACLV,OAAOa,iBAAiB,GACpBL,aACA9C,eAAe+C,SACflD,cAAc8C,cAAcG,YAAYF,OAAOQ,MAAM,GACrDL;IAER;IAEAM,OACE,iCAAiC,GACjCN,KAAa,EACK;QAClB,OAAO,IAAI,CAACO,mBAAmB,CAAC1C,IAAI,CAAC,CAACC,WAAaA,SAAS0C,GAAG,CAACR;IAClE;IAEAS,SAAST,KAAa,EAA0B;QAC9C,OAAO,IAAI,CAACU,WAAW,CAACC,SAAS,CAACX,OAAOnC,IAAI,CAAC,CAACS;YAC7C,IAAI,eAAeA,KAAK;gBACtB,OAAO;oBACLsC,MAAMtC,IAAIuC,SAAS;oBACnBC,KAAKxC,IAAIyC,OAAO;oBAChBC,aAAa1C,IAAI2C,MAAM,CAACC,GAAG,CAAC,CAACC,IAAO,CAAA;4BAClC1B,MAAM0B,EAAE1B,IAAI;4BACZ2B,MAAMD,EAAEE,OAAO;wBACjB,CAAA;gBACF;YACF;YACA,MAAM/C,IAAIgD,KAAK;QACjB;IACF;IAEAC,SAASvB,KAAa,EAAiB;QACrC,OAAO,IAAI,CAACU,WAAW,CAACa,QAAQ,CAACvB;IACnC;IAvIAwB,YAAYpC,OAAe,EAAEf,WAAmB,CAAE;QAChD,IAAI,CAACqC,WAAW,GAAGtD,kBAAkBiB;QAErC,IAAI,CAACe,OAAO,GAAGA;QACf,IAAI,CAACf,WAAW,GAAGA;QAEnB,IAAI,CAACkC,mBAAmB,GAAG,IAAIkB,QAAQ,CAACC;YACtC,IAAI1D,OAAO2D,cAAc,EAAE;gBACzBD,QAAQ1D,OAAO2D,cAAc;YAC/B,OAAO;gBACL3D,OAAO4D,iBAAiB,GAAG;oBACzBF,QAAQ1D,OAAO2D,cAAc;gBAC/B;YACF;QACF;IACF;AAyHF;AAjJA,SAAqBnE,wBAiJpB"}