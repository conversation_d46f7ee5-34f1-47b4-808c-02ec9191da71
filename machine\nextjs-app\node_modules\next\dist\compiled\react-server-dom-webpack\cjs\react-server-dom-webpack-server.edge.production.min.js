/**
 * @license React
 * react-server-dom-webpack-server.edge.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("react"),ba=require("react-dom"),l=null,m=0;function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<m&&(a.enqueue(new Uint8Array(l.buffer,0,m)),l=new Uint8Array(512),m=0),a.enqueue(b);else{var d=l.length-m;d<b.byteLength&&(0===d?a.enqueue(l):(l.set(b.subarray(0,d),m),a.enqueue(l),b=b.subarray(d)),l=new Uint8Array(512),m=0);l.set(b,m);m+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var t=Symbol.for("react.client.reference"),u=Symbol.for("react.server.reference");function v(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===u){var b=ea.call(arguments,1);a.$$typeof=u;a.$$id=this.$$id;a.$$bound=this.$$bound?this.$$bound.concat(b):b}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=v(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=v({},a.$$id,!0),e=new Proxy(c,ka);a.status="fulfilled";a.value=e;return a.then=v(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}c=a[b];c||(c=v(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},sa={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=w();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),x(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?x(d,"C",[a,b]):x(d,"C",a))}}}
function na(a,b,d){if("string"===typeof a){var c=w();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,h=d.imageSizes,k="";"string"===typeof g&&""!==g?(k+="["+g+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;f+="[image]"+k}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=y(d))?x(c,"L",[a,b,d]):x(c,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"m",[a,b]):x(d,"m",a)}}}
function pa(a,b,d){if("string"===typeof a){var c=w();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=y(d))?x(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?x(c,"S",[a,b]):x(c,"S",a)}}}function qa(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"X",[a,b]):x(d,"X",a)}}}function ra(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"M",[a,b]):x(d,"M",a)}}}
function y(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ta=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,ua="function"===typeof AsyncLocalStorage,va=ua?new AsyncLocalStorage:null,z=Symbol.for("react.element"),wa=Symbol.for("react.fragment"),xa=Symbol.for("react.provider"),ya=Symbol.for("react.server_context"),za=Symbol.for("react.forward_ref"),Aa=Symbol.for("react.suspense"),Ba=Symbol.for("react.suspense_list"),Ca=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Da=Symbol.for("react.default_value"),Ea=Symbol.for("react.memo_cache_sentinel"),
Fa=Symbol.iterator,C=null;function D(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");D(a,d);b.context._currentValue=b.value}}}function Ga(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ga(a)}
function Ha(a){var b=a.parent;null!==b&&Ha(b);a.context._currentValue=a.value}function Ia(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?D(a,b):Ia(a,b)}
function Ja(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?D(a,d):Ja(a,d);b.context._currentValue=b.value}function Ka(a){var b=C;b!==a&&(null===b?Ha(a):null===a?Ga(b):b.depth===a.depth?D(b,a):b.depth>a.depth?Ia(b,a):Ja(b,a),C=a)}function La(a,b){var d=a._currentValue;a._currentValue=b;var c=C;return C=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Ma=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Na(){}function Oa(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Na,Na),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}F=b;throw Ma;}}var F=null;
function Pa(){if(null===F)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=F;F=null;return a}var H=null,I=0,J=null;function Qa(){var a=J;J=null;return a}function Ra(a){return a._currentValue}
var Va={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:K,useTransition:K,readContext:Ra,useContext:Ra,useReducer:K,useRef:K,useState:K,useInsertionEffect:K,useLayoutEffect:K,useImperativeHandle:K,useEffect:K,useId:Sa,useSyncExternalStore:K,useCacheRefresh:function(){return Ta},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ea;return b},use:Ua};
function K(){throw Error("This Hook is not supported in Server Components.");}function Ta(){throw Error("Refreshing the cache is not supported in Server Components.");}function Sa(){if(null===H)throw Error("useId can only be used while React is rendering");var a=H.identifierCount++;return":"+H.identifierPrefix+"S"+a.toString(32)+":"}
function Ua(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=I;I+=1;null===J&&(J=[]);return Oa(J,a,b)}if(a.$$typeof===ya)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Wa(){return(new AbortController).signal}function Xa(){var a=w();return a?a.cache:new Map}
var Ya={getCacheSignal:function(){var a=Xa(),b=a.get(Wa);void 0===b&&(b=Wa(),a.set(Wa,b));return b},getCacheForType:function(a){var b=Xa(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},Za=Array.isArray;function $a(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function ab(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Za(a))return"[...]";a=$a(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function L(a){if("string"===typeof a)return a;switch(a){case Aa:return"Suspense";case Ba:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case za:return L(a.render);case Ca:return L(a.type);case A:var b=a._payload;a=a._init;try{return L(a(b))}catch(d){}}return""}
function M(a,b){var d=$a(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(Za(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?M(g):ab(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===z)e="<"+L(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var h=f[g],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k="object"===typeof k&&null!==k?M(k):
ab(k);h===b?(d=e.length,c=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var bb=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,cb=bb.ContextRegistry,N=JSON.stringify,db=bb.ReactCurrentDispatcher,eb=bb.ReactCurrentCache;function fb(a){console.error(a)}function gb(){}
function hb(a,b,d,c,e,f){if(null!==eb.current&&eb.current!==Ya)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=sa;eb.current=Ya;var g=new Set,h=[],k=new Set,n={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:g,pingedTasks:h,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,identifierPrefix:e||"",identifierCount:1,onError:void 0===d?fb:d,onPostpone:void 0===f?gb:f,toJSON:function(r,E){return ib(n,this,r,E)}};n.pendingChunks++;b=jb(c);a=kb(n,a,b,g);h.push(a);return n}var O=null;function w(){if(O)return O;if(ua){var a=va.getStore();if(a)return a}return null}var lb={};
function mb(a,b){a.pendingChunks++;var d=kb(a,null,C,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,nb(a,d),d.id;case "rejected":var c=P(a,b.reason);Q(a,d.id,c);return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=e;nb(a,d)},function(e){d.status=4;e=P(a,e);Q(a,d.id,e);null!==a.destination&&
R(a,a.destination)});return d.id}function x(a,b,d){d=N(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);ob(a)}function pb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function qb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:pb}}
function S(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===t)return[z,b,d,e];I=0;J=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:qb(e):e}if("string"===typeof b)return[z,b,d,e];if("symbol"===typeof b)return b===wa?e.children:[z,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===t)return[z,b,d,e];switch(b.$$typeof){case A:var g=
b._init;b=g(b._payload);return S(a,b,d,c,e,f);case za:return a=b.render,I=0,J=f,a(e,void 0);case Ca:return S(a,b.type,d,c,e,f);case xa:return La(b._context,e.value),[z,b,d,{value:e.value,children:e.children,__pop:lb}]}}throw Error("Unsupported Server Component type: "+ab(b));}function nb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return rb(a)},0))}
function kb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return nb(a,e)},thenableState:null};c.add(e);return e}function T(a){return"$"+a.toString(16)}function sb(a,b,d){a=N(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function tb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===z&&"1"===d?"$L"+g.toString(16):T(g);try{var h=a.bundlerConfig,k=c.$$id;g="";var n=h[k];if(n)g=n.name;else{var r=k.lastIndexOf("#");-1!==r&&(g=k.slice(r+1),n=h[k.slice(0,r)]);if(!n)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var E={id:n.id,chunks:n.chunks,name:g,async:!!c.$$async};
a.pendingChunks++;var G=a.nextChunkId++,B=N(E),Lb=G.toString(16)+":I"+B+"\n",Mb=q.encode(Lb);a.completedImportChunks.push(Mb);f.set(e,G);return b[0]===z&&"1"===d?"$L"+G.toString(16):T(G)}catch(Nb){return a.pendingChunks++,b=a.nextChunkId++,d=P(a,Nb),Q(a,b,d),T(b)}}function ub(a,b){a.pendingChunks++;var d=a.nextChunkId++;vb(a,d,b);return d}
function ib(a,b,d,c){switch(c){case z:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===z||c.$$typeof===A);)try{switch(c.$$typeof){case z:var e=c;c=S(a,e.type,e.key,e.ref,e.props,null);break;case A:var f=c._init;c=f(c._payload)}}catch(g){d=g===Ma?Pa():g;if("object"===typeof d&&null!==d&&"function"===typeof d.then)return a.pendingChunks++,a=kb(a,c,C,a.abortableTasks),c=a.ping,d.then(c,c),a.thenableState=Qa(),"$L"+a.id.toString(16);a.pendingChunks++;c=a.nextChunkId++;d=P(a,d);Q(a,c,d);return"$L"+
c.toString(16)}if(null===c)return null;if("object"===typeof c){if(c.$$typeof===t)return tb(a,b,d,c);if("function"===typeof c.then)return"$@"+mb(a,c).toString(16);if(c.$$typeof===xa)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=sb(a,d,"$P"+c),a.completedRegularChunks.push(c)),T(d);if(c===lb){a=C;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=
c===Da?a.context._defaultValue:c;C=a.parent;return}return c instanceof Map?"$Q"+ub(a,Array.from(c)).toString(16):c instanceof Set?"$W"+ub(a,Array.from(c)).toString(16):!Za(c)&&(null===c||"object"!==typeof c?a=null:(a=Fa&&c[Fa]||c["@@iterator"],a="function"===typeof a?a:null),a)?Array.from(c):c}if("string"===typeof c){if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,c=q.encode(c),b=c.byteLength,b=d.toString(16)+":T"+b.toString(16)+
",",b=q.encode(b),a.completedRegularChunks.push(b,c),T(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){if(c.$$typeof===t)return tb(a,b,d,c);if(c.$$typeof===u)return d=a.writtenServerReferences,b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?
Promise.resolve(b):null},a=ub(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+M(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+M(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return T(f);f=c.description;
if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+M(b,d));a.pendingChunks++;d=a.nextChunkId++;b=sb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return T(d)}if("bigint"===typeof c)return"$n"+c.toString(10);throw Error("Type "+typeof c+" is not supported in Client Component props."+M(b,d));}
function P(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}function wb(a,b){null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}
function Q(a,b,d){d={digest:d};b=b.toString(16)+":E"+N(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}function vb(a,b,d){d=N(d,a.toJSON);b=b.toString(16)+":"+d+"\n";b=q.encode(b);a.completedRegularChunks.push(b)}
function rb(a){var b=db.current;db.current=Va;var d=O;H=O=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++){var f=c[e];var g=a;if(0===f.status){Ka(f.context);try{var h=f.model;if("object"===typeof h&&null!==h&&h.$$typeof===z){var k=h,n=f.thenableState;f.model=h;h=S(g,k.type,k.key,k.ref,k.props,n);for(f.thenableState=null;"object"===typeof h&&null!==h&&h.$$typeof===z;)k=h,f.model=h,h=S(g,k.type,k.key,k.ref,k.props,null)}vb(g,f.id,h);g.abortableTasks.delete(f);f.status=1}catch(B){var r=
B===Ma?Pa():B;if("object"===typeof r&&null!==r&&"function"===typeof r.then){var E=f.ping;r.then(E,E);f.thenableState=Qa()}else{g.abortableTasks.delete(f);f.status=4;var G=P(g,r);Q(g,f.id,G)}}}}null!==a.destination&&R(a,a.destination)}catch(B){P(a,B),wb(a,B)}finally{db.current=b,H=null,O=d}}
function R(a,b){l=new Uint8Array(512);m=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,p(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)p(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,p(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,p(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&(b.enqueue(new Uint8Array(l.buffer,0,m)),l=null,m=0)}0===a.pendingChunks&&
b.close()}function xb(a){a.flushScheduled=null!==a.destination;ua?setTimeout(function(){return va.run(a,rb,a)},0):setTimeout(function(){return rb(a)},0)}function ob(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return R(a,b)},0)}}
function yb(a,b){try{var d=a.abortableTasks;if(0<d.size){var c=void 0===b?Error("The render was aborted by the server without a reason."):b,e=P(a,c);a.pendingChunks++;var f=a.nextChunkId++;Q(a,f,e,c);d.forEach(function(g){g.status=3;var h=T(f);g=sb(a,g.id,h);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&R(a,a.destination)}catch(g){P(a,g),wb(a,g)}}
function jb(a){if(a){var b=C;Ka(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];cb[e]||(cb[e]=aa.createServerContext(e,Da));La(cb[e],c)}a=C;Ka(b);return a}return null}function zb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return{id:c.id,chunks:c.chunks,name:d,async:!1}}
var U=new Map;function Ab(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Bb(){}
function Cb(a){for(var b=a.chunks,d=[],c=0;c<b.length;c++){var e=b[c],f=U.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);d.push(f);var g=U.set.bind(U,e,null);f.then(g,Bb);U.set(e,f)}else null!==f&&d.push(f)}return a.async?0===d.length?Ab(a.id):Promise.all(d).then(function(){return Ab(a.id)}):0<d.length?Promise.all(d):null}
function V(a){var b=globalThis.__next_require__(a.id);if(a.async&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a.name?b:""===a.name?b.__esModule?b.default:b:b[a.name]}function W(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}W.prototype=Object.create(Promise.prototype);
W.prototype.then=function(a,b){switch(this.status){case "resolved_model":Db(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Eb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Fb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Eb(d,b)}}function Gb(a,b,d,c,e,f){var g=zb(a._bundlerConfig,b);a=Cb(g);if(d)d=Promise.all([d,a]).then(function(h){h=h[0];var k=V(g);return k.bind.apply(k,[null].concat(h))});else if(a)d=Promise.resolve(a).then(function(){return V(g)});else return V(g);d.then(Hb(c,e,f),Ib(c));return null}var X=null,Y=null;
function Db(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Jb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Fb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new W("resolved_model",c,null,a):new W("pending",null,null,a),d.set(b,c));return c}function Hb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Eb(e,c.value))}}function Ib(a){return function(b){return Fb(a,b)}}
function Kb(a,b){a=Z(a,b);"resolved_model"===a.status&&Db(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Ob(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Kb(a,c),Gb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Kb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Kb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,h){h.startsWith(e)&&f.append(h.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Db(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Hb(c,b,d),Ib(c)),null;default:throw a.reason;}}return c}
function Pb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Ob(e,this,f,g):g}};return e}function Qb(a){Jb(a,Error("Connection closed."))}function Rb(a,b,d){var c=zb(a,b);a=Cb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=V(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return V(c)}):Promise.resolve(V(c))}
function Sb(a,b,d){a=Pb(b,d,a);Qb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=v({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Sb(a,b,e),c=Rb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=Rb(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(g,h){h.startsWith("$ACTION_REF_")&&(g="$ACTION_"+h.slice(12)+":",e=Sb(b,d,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,c,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Pb(b,"",a);Qb(a);return Z(a,0)};
exports.registerClientReference=function(a,b,d){return v(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:u},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=hb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)yb(c,e.reason);else{var f=function(){yb(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){xb(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===c.destination){c.destination=g;try{R(c,
g)}catch(h){P(c,h),wb(c,h)}}},cancel:function(){}},{highWaterMark:0})};
