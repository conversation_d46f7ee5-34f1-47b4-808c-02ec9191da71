{"version": 3, "sources": ["../../../src/build/output/index.ts"], "names": ["chalk", "stripAnsi", "textTable", "createStore", "formatWebpackMessages", "store", "consoleStore", "COMPILER_NAMES", "startedDevelopmentServer", "appUrl", "bindAddr", "setState", "formatAmpMessages", "amp", "output", "bold", "messages", "chalkError", "red", "ampError", "page", "error", "push", "message", "specUrl", "chalk<PERSON>arn", "yellow", "ampWarn", "warn", "errors", "warnings", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "code", "filter", "length", "index", "align", "stringLength", "str", "buildStore", "client", "server", "edgeServer", "buildWasDone", "clientWasLoading", "serverWasLoading", "edgeServerWasLoading", "subscribe", "state", "trigger", "getState", "loading", "bootstrap", "partialState", "typeChecking", "totalModulesCount", "hasEdgeServer", "concat", "ampValidation", "Object", "keys", "k", "sort", "reduce", "a", "c", "newAmp", "watchCompilers", "tapCompiler", "key", "compiler", "onEvent", "hooks", "invalid", "tap", "done", "stats", "to<PERSON><PERSON>", "preset", "moduleTrace", "hasErrors", "hasWarnings", "compilation", "modules", "size", "status", "undefined", "reportTrigger"], "mappings": "AAAA,OAAOA,WAAW,2BAA0B;AAC5C,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,iBAAiB,8BAA6B;AACrD,OAAOC,2BAA2B,yDAAwD;AAC1F,SAAsBC,SAASC,YAAY,QAAQ,UAAS;AAE5D,SAA6BC,cAAc,QAAQ,6BAA4B;AAE/E,OAAO,SAASC,yBAAyBC,MAAc,EAAEC,QAAgB;IACvEJ,aAAaK,QAAQ,CAAC;QAAEF;QAAQC;IAAS;AAC3C;AAgCA,OAAO,SAASE,kBAAkBC,GAAkB;IAClD,IAAIC,SAASd,MAAMe,IAAI,CAAC,oBAAoB;IAC5C,IAAIC,WAAuB,EAAE;IAE7B,MAAMC,aAAajB,MAAMkB,GAAG,CAAC;IAC7B,SAASC,SAASC,IAAY,EAAEC,KAAgB;QAC9CL,SAASM,IAAI,CAAC;YAACF;YAAMH;YAAYI,MAAME,OAAO;YAAEF,MAAMG,OAAO,IAAI;SAAG;IACtE;IAEA,MAAMC,YAAYzB,MAAM0B,MAAM,CAAC;IAC/B,SAASC,QAAQP,IAAY,EAAEQ,IAAe;QAC5CZ,SAASM,IAAI,CAAC;YAACF;YAAMK;YAAWG,KAAKL,OAAO;YAAEK,KAAKJ,OAAO,IAAI;SAAG;IACnE;IAEA,IAAK,MAAMJ,QAAQP,IAAK;QACtB,IAAI,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAGjB,GAAG,CAACO,KAAK;QAEpC,MAAMW,gBAAgB,CAACC,MAAmBA,IAAIC,IAAI,KAAK;QACvDJ,SAASA,OAAOK,MAAM,CAACH;QACvBD,WAAWA,SAASI,MAAM,CAACH;QAC3B,IAAI,CAAEF,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;YAEvC;QACF;QAEA,IAAIN,OAAOM,MAAM,EAAE;YACjBhB,SAASC,MAAMS,MAAM,CAAC,EAAE;YACxB,IAAK,IAAIO,QAAQ,GAAGA,QAAQP,OAAOM,MAAM,EAAE,EAAEC,MAAO;gBAClDjB,SAAS,IAAIU,MAAM,CAACO,MAAM;YAC5B;QACF;QACA,IAAIN,SAASK,MAAM,EAAE;YACnBR,QAAQE,OAAOM,MAAM,GAAG,KAAKf,MAAMU,QAAQ,CAAC,EAAE;YAC9C,IAAK,IAAIM,QAAQ,GAAGA,QAAQN,SAASK,MAAM,EAAE,EAAEC,MAAO;gBACpDT,QAAQ,IAAIG,QAAQ,CAACM,MAAM;YAC7B;QACF;QACApB,SAASM,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG;IAChC;IAEA,IAAI,CAACN,SAASmB,MAAM,EAAE;QACpB,OAAO;IACT;IAEArB,UAAUZ,UAAUc,UAAU;QAC5BqB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAI;QAC3BC,cAAaC,GAAW;YACtB,OAAOtC,UAAUsC,KAAKJ,MAAM;QAC9B;IACF;IAEA,OAAOrB;AACT;AAEA,MAAM0B,aAAarC,YAA8B;IAC/C,iCAAiC;IACjCsC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,YAAY,CAAC;AACf;AACA,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,uBAAuB;AAE3BP,WAAWQ,SAAS,CAAC,CAACC;IACpB,MAAM,EAAEpC,GAAG,EAAE4B,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEO,OAAO,EAAE,GAAGD;IAErD,MAAM,EAAExC,MAAM,EAAE,GAAGH,aAAa6C,QAAQ;IAExC,IAAIV,OAAOW,OAAO,IAAIV,OAAOU,OAAO,KAAIT,8BAAAA,WAAYS,OAAO,GAAE;QAC3D9C,aAAaK,QAAQ,CACnB;YACE0C,WAAW;YACX5C,QAAQA;YACR,wEAAwE;YACxE2C,SAAS;YACTF;QACF,GACA;QAEFL,mBAAmB,AAAC,CAACD,gBAAgBC,oBAAqBJ,OAAOW,OAAO;QACxEN,mBAAmB,AAAC,CAACF,gBAAgBE,oBAAqBJ,OAAOU,OAAO;QACxEL,uBACE,AAAC,CAACH,gBAAgBG,wBAAyBJ,WAAWS,OAAO;QAC/DR,eAAe;QACf;IACF;IAEAA,eAAe;IAEf,IAAIU,eAAqC;QACvCD,WAAW;QACX5C,QAAQA;QACR2C,SAAS;QACTG,cAAc;QACdC,mBACE,AAACX,CAAAA,mBAAmBJ,OAAOe,iBAAiB,GAAG,CAAA,IAC9CV,CAAAA,mBAAmBJ,OAAOc,iBAAiB,GAAG,CAAA,IAC9CT,CAAAA,uBAAuBJ,CAAAA,8BAAAA,WAAYa,iBAAiB,KAAI,IAAI,CAAA;QAC/DC,eAAe,CAAC,CAACd;IACnB;IACA,IAAIF,OAAOZ,MAAM,IAAIgB,kBAAkB;QACrC,0BAA0B;QAC1BvC,aAAaK,QAAQ,CACnB;YACE,GAAG2C,YAAY;YACfzB,QAAQY,OAAOZ,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIY,OAAOb,MAAM,IAAIiB,kBAAkB;QAC5CxC,aAAaK,QAAQ,CACnB;YACE,GAAG2C,YAAY;YACfzB,QAAQa,OAAOb,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIa,WAAWd,MAAM,IAAIkB,sBAAsB;QACpDzC,aAAaK,QAAQ,CACnB;YACE,GAAG2C,YAAY;YACfzB,QAAQc,WAAWd,MAAM;YACzBC,UAAU;QACZ,GACA;IAEJ,OAAO;QACL,iCAAiC;QACjC,MAAMA,WAAW;eACXW,OAAOX,QAAQ,IAAI,EAAE;eACrBY,OAAOZ,QAAQ,IAAI,EAAE;eACrBa,WAAWb,QAAQ,IAAI,EAAE;SAC9B,CAAC4B,MAAM,CAAC9C,kBAAkBC,QAAQ,EAAE;QAErCP,aAAaK,QAAQ,CACnB;YACE,GAAG2C,YAAY;YACfzB,QAAQ;YACRC,UAAUA,SAASK,MAAM,KAAK,IAAI,OAAOL;QAC3C,GACA;IAEJ;AACF;AAEA,OAAO,SAAS6B,cACdvC,IAAY,EACZS,MAAmB,EACnBC,QAAqB;IAErB,MAAM,EAAEjB,GAAG,EAAE,GAAG2B,WAAWW,QAAQ;IACnC,IAAI,CAAEtB,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;QACvCK,WAAW7B,QAAQ,CAAC;YAClBE,KAAK+C,OAAOC,IAAI,CAAChD,KACdqB,MAAM,CAAC,CAAC4B,IAAMA,MAAM1C,MACpB2C,IAAI,EACL,wCAAwC;aACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGrD,GAAG,CAACqD,EAAE,EAAGD,CAAAA,GAAI,CAAC;QAC7C;QACA;IACF;IAEA,MAAME,SAAwB;QAAE,GAAGtD,GAAG;QAAE,CAACO,KAAK,EAAE;YAAES;YAAQC;QAAS;IAAE;IACrEU,WAAW7B,QAAQ,CAAC;QAClBE,KAAK+C,OAAOC,IAAI,CAACM,QACdJ,IAAI,EACL,wCAAwC;SACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGC,MAAM,CAACD,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAChD;AACF;AAEA,OAAO,SAASG,eACd3B,MAAwB,EACxBC,MAAwB,EACxBC,UAA4B;IAE5BH,WAAW7B,QAAQ,CAAC;QAClB8B,QAAQ;YAAEW,SAAS;QAAK;QACxBV,QAAQ;YAAEU,SAAS;QAAK;QACxBT,YAAY;YAAES,SAAS;QAAK;QAC5BF,SAAS;IACX;IAEA,SAASmB,YACPC,GAAuB,EACvBC,QAA0B,EAC1BC,OAAwC;QAExCD,SAASE,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,cAAc,EAAEL,IAAI,CAAC,EAAE;YACjDE,QAAQ;gBAAEpB,SAAS;YAAK;QAC1B;QAEAmB,SAASE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,WAAW,EAAEL,IAAI,CAAC,EAAE,CAACO;YAC5CrC,WAAW7B,QAAQ,CAAC;gBAAEE,KAAK,CAAC;YAAE;YAE9B,MAAM,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAG1B,sBAC3ByE,MAAMC,MAAM,CAAC;gBACXC,QAAQ;gBACRC,aAAa;YACf;YAGF,MAAMC,YAAY,CAAC,EAACpD,0BAAAA,OAAQM,MAAM;YAClC,MAAM+C,cAAc,CAAC,EAACpD,4BAAAA,SAAUK,MAAM;YAEtCqC,QAAQ;gBACNpB,SAAS;gBACTI,mBAAmBqB,MAAMM,WAAW,CAACC,OAAO,CAACC,IAAI;gBACjDxD,QAAQoD,YAAYpD,SAAS;gBAC7BC,UAAUoD,cAAcpD,WAAW;YACrC;QACF;IACF;IAEAuC,YAAY9D,eAAekC,MAAM,EAAEA,QAAQ,CAAC6C;QAC1C,IACE,CAACA,OAAOlC,OAAO,IACf,CAACZ,WAAWW,QAAQ,GAAGT,MAAM,CAACU,OAAO,IACrC,CAACZ,WAAWW,QAAQ,GAAGR,UAAU,CAACS,OAAO,IACzCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAhB,WAAW7B,QAAQ,CAAC;gBAClB8B,QAAQ6C;gBACRpC,SAASqC;YACX;QACF,OAAO;YACL/C,WAAW7B,QAAQ,CAAC;gBAClB8B,QAAQ6C;YACV;QACF;IACF;IACAjB,YAAY9D,eAAemC,MAAM,EAAEA,QAAQ,CAAC4C;QAC1C,IACE,CAACA,OAAOlC,OAAO,IACf,CAACZ,WAAWW,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACZ,WAAWW,QAAQ,GAAGR,UAAU,CAACS,OAAO,IACzCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAhB,WAAW7B,QAAQ,CAAC;gBAClB+B,QAAQ4C;gBACRpC,SAASqC;YACX;QACF,OAAO;YACL/C,WAAW7B,QAAQ,CAAC;gBAClB+B,QAAQ4C;YACV;QACF;IACF;IACAjB,YAAY9D,eAAeoC,UAAU,EAAEA,YAAY,CAAC2C;QAClD,IACE,CAACA,OAAOlC,OAAO,IACf,CAACZ,WAAWW,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACZ,WAAWW,QAAQ,GAAGT,MAAM,CAACU,OAAO,IACrCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAhB,WAAW7B,QAAQ,CAAC;gBAClBgC,YAAY2C;gBACZpC,SAASqC;YACX;QACF,OAAO;YACL/C,WAAW7B,QAAQ,CAAC;gBAClBgC,YAAY2C;YACd;QACF;IACF;AACF;AAEA,OAAO,SAASE,cAActC,OAAe;IAC3CV,WAAW7B,QAAQ,CAAC;QAClBuC;IACF;AACF"}