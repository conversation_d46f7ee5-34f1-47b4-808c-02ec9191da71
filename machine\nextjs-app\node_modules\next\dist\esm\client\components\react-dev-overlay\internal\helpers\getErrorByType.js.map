{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/getErrorByType.ts"], "names": ["ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "getErrorSource", "getOriginalStackFrames", "getErrorByType", "ev", "id", "event", "type", "readyRuntimeError", "runtime", "error", "reason", "frames", "toString", "componentStackFrames", "_", "Error"], "mappings": "AAAA,SACEA,sBAAsB,EACtBC,0BAA0B,QACrB,2BAA0B;AAEjC,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,sBAAsB,QAA4B,gBAAe;AAW1E,OAAO,eAAeC,eACpBC,EAAuB;IAEvB,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAE,GAAGF;IACtB,OAAQE,MAAMC,IAAI;QAChB,KAAKR;QACL,KAAKC;YAA4B;gBAC/B,MAAMQ,oBAAuC;oBAC3CH;oBACAI,SAAS;oBACTC,OAAOJ,MAAMK,MAAM;oBACnBC,QAAQ,MAAMV,uBACZI,MAAMM,MAAM,EACZX,eAAeK,MAAMK,MAAM,GAC3BL,MAAMK,MAAM,CAACE,QAAQ;gBAEzB;gBACA,IAAIP,MAAMC,IAAI,KAAKR,wBAAwB;oBACzCS,kBAAkBM,oBAAoB,GAAGR,MAAMQ,oBAAoB;gBACrE;gBACA,OAAON;YACT;QACA;YAAS;gBACP;YACF;IACF;IACA,6DAA6D;IAC7D,MAAMO,IAAWT;IACjB,MAAM,IAAIU,MAAM;AAClB"}