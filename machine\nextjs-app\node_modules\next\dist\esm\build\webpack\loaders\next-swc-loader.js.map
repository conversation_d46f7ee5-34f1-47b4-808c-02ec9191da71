{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["isWasm", "transform", "getLoaderSWCOptions", "path", "isAbsolute", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "hasServerComponents", "isServerLayer", "optimizeBarrelExports", "bundleTarget", "isPageFile", "startsWith", "relativeFilePathFromRoot", "relative", "process", "env", "NEXT_TEST_MODE", "console", "log", "swcOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "isServerActionsEnabled", "serverActions", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "pitch", "callback", "async", "versions", "pnp", "test", "loaders", "length", "loaderIndex", "loaderSpan", "currentTraceSpan", "addDependency", "r", "sw<PERSON><PERSON><PERSON><PERSON>", "inputSource", "transformedSource", "outputSourceMap", "err", "raw"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAEA,SAASA,MAAM,EAAEC,SAAS,QAAQ,YAAW;AAC7C,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,OAAOC,QAAQC,UAAU,QAAQ,OAAM;AAEvC,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QAyCMC,0BACZA,2BAESA,2BAMGA;IAhD1B,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAgB,IAAI,CAACC,UAAU,MAAM,CAAC;IAE1C,MAAM,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfT,UAAU,EACVU,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,qBAAqB,EACrBC,YAAY,EACb,GAAGb;IACJ,MAAMc,aAAahB,SAASiB,UAAU,CAACX;IACvC,MAAMY,2BAA2BzB,KAAK0B,QAAQ,CAACd,SAASL;IAExD,uBAAuB;IACvB,IAAIoB,QAAQC,GAAG,CAACC,cAAc,EAAE;QAC9B,IAAIpB,cAAcY,qBAAqB,EAAE;YACvCS,QAAQC,GAAG,CAAC,0BAA0BxB;QACxC;IACF;IAEA,MAAMyB,aAAajC,oBAAoB;QACrCc;QACAC;QACAP;QACAI;QACAY;QACAU,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3BnB;QACAoB,iBAAiB,EAAE7B,8BAAAA,WAAY6B,iBAAiB;QAChDC,sBAAsB,EAAE9B,+BAAAA,2BAAAA,WAAY+B,YAAY,qBAAxB/B,yBAA0B8B,sBAAsB;QACxEE,UAAU,EAAEhC,+BAAAA,4BAAAA,WAAY+B,YAAY,qBAAxB/B,0BAA0BgC,UAAU;QAChDC,eAAe,EAAEjC,8BAAAA,WAAYkC,QAAQ;QACrCC,mBAAmB,EAAEnC,+BAAAA,4BAAAA,WAAY+B,YAAY,qBAAxB/B,0BAA0BmC,mBAAmB;QAClEzB;QACAC;QACAC;QACAO;QACAN;QACAuB,sBAAsB,EAAEpC,+BAAAA,4BAAAA,WAAY+B,YAAY,qBAAxB/B,0BAA0BqC,aAAa;QAC/DvB;QACAC;QACAC;IACF;IAEA,MAAMsB,sBAAsB;QAC1B,GAAGZ,UAAU;QACbzB;QACAF,gBAAgBA,iBAAiBwC,KAAKC,SAAS,CAACzC,kBAAkB0C;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgB5C;IAClB;IAEA,IAAI,CAACqC,oBAAoBvC,cAAc,EAAE;QACvC,OAAOuC,oBAAoBvC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAAC6B,IAAI,IACTU,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAACtD,SAAS,IACjC8C,oBAAoBQ,GAAG,CAACtD,SAAS,CAACuD,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCb,oBAAoBQ,GAAG,CAACtD,SAAS,CAACuD,KAAK,EACvC,gBAEF;QACAT,oBAAoBQ,GAAG,CAACtD,SAAS,CAACuD,KAAK,CAACpB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMwB,UAAUvD,YAAYwD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1B9D,UAAUM,QAAewC,qBAAqBiB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOnB,KAAKoB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGvB,KAAKoB,KAAK,CAACH,OAAOM,GAAG,IAAIrB;aAAU;QACvE;AAEJ;AAEA,MAAMsB,iBACJ;AAEF,OAAO,SAASC;IACd,MAAMC,WAAW,IAAI,CAACC,KAAK;IACzB,CAAA;QACA,IACE,kDAAkD;QAClD,CAAC7C,QAAQ8C,QAAQ,CAACC,GAAG,IACrB,CAACL,eAAeM,IAAI,CAAC,IAAI,CAACnE,YAAY,KACtC,IAAI,CAACoE,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5C7E,WAAW,IAAI,CAACO,YAAY,KAC5B,CAAE,MAAMX,UACR;YACA,MAAMkF,aAAa,IAAI,CAACC,gBAAgB,CAACrB,UAAU,CAAC;YACpD,IAAI,CAACsB,aAAa,CAAC,IAAI,CAACzE,YAAY;YACpC,OAAOuE,WAAWnB,YAAY,CAAC,IAC7B1D,gBAAgBuD,IAAI,CAAC,IAAI,EAAEsB;QAE/B;IACF,CAAA,IAAKlB,IAAI,CAAC,CAACqB;QACT,IAAIA,GAAG,OAAOX,SAAS,SAASW;QAChCX;IACF,GAAGA;AACL;AAEA,eAAe,SAASY,UAEtBC,WAAmB,EACnB/E,cAAmB;IAEnB,MAAM0E,aAAa,IAAI,CAACC,gBAAgB,CAACrB,UAAU,CAAC;IACpD,MAAMY,WAAW,IAAI,CAACC,KAAK;IAC3BO,WACGnB,YAAY,CAAC,IACZ1D,gBAAgBuD,IAAI,CAAC,IAAI,EAAEsB,YAAYK,aAAa/E,iBAErDwD,IAAI,CACH,CAAC,CAACwB,mBAAmBC,gBAAqB;QACxCf,SAAS,MAAMc,mBAAmBC,mBAAmBjF;IACvD,GACA,CAACkF;QACChB,SAASgB;IACX;AAEN;AAEA,oCAAoC;AACpC,OAAO,MAAMC,MAAM,KAAI"}