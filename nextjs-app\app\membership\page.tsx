import { Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function Membership() {
  const plans = [
    {
      name: "Basic",
      price: "$29",
      period: "/month",
      description: "Perfect for getting started with your fitness journey",
      features: [
        "Access to gym equipment",
        "Locker room access",
        "Basic fitness assessment",
        "Online workout library"
      ],
      notIncluded: [
        "Group classes",
        "Personal training",
        "Nutrition consultation",
        "24/7 access"
      ],
      popular: false
    },
    {
      name: "Premium",
      price: "$59",
      period: "/month",
      description: "Most popular choice for serious fitness enthusiasts",
      features: [
        "Everything in Basic",
        "Unlimited group classes",
        "24/7 gym access",
        "Guest passes (2/month)",
        "Nutrition consultation",
        "Progress tracking app"
      ],
      notIncluded: [
        "Personal training sessions",
        "Meal planning service"
      ],
      popular: true
    },
    {
      name: "Elite",
      price: "$99",
      period: "/month",
      description: "Complete fitness solution with premium perks",
      features: [
        "Everything in Premium",
        "4 personal training sessions/month",
        "Custom meal planning",
        "Recovery services access",
        "VIP locker",
        "Free supplements (monthly)",
        "Priority class booking",
        "Unlimited guest passes"
      ],
      notIncluded: [],
      popular: false
    }
  ];

  const faqs = [
    {
      question: "Can I cancel my membership anytime?",
      answer: "Yes, you can cancel your membership with 30 days notice. No cancellation fees apply."
    },
    {
      question: "Is there a joining fee?",
      answer: "Currently, we're waiving all joining fees for new members. This offer is valid for a limited time."
    },
    {
      question: "Can I freeze my membership?",
      answer: "Yes, you can freeze your membership for up to 3 months per year for medical reasons or travel."
    },
    {
      question: "Do you offer family discounts?",
      answer: "Yes, we offer 15% discount for families with 3+ members joining together."
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">Membership Plans</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Choose the perfect plan for your fitness goals. All memberships include access to our state-of-the-art facilities.
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <Card 
                key={index} 
                className={`relative overflow-hidden ${plan.popular ? 'ring-2 ring-black shadow-2xl scale-105' : 'shadow-lg'}`}
              >
                {plan.popular && (
                  <div className="absolute top-0 left-0 right-0 bg-black text-white text-center py-2 text-sm font-semibold">
                    MOST POPULAR
                  </div>
                )}
                
                <CardHeader className={`text-center ${plan.popular ? 'pt-12' : ''}`}>
                  <CardTitle className="text-2xl font-bold text-gray-900">
                    {plan.name}
                  </CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-600">{plan.period}</span>
                  </div>
                  <p className="text-gray-600 mt-2">{plan.description}</p>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                    {plan.notIncluded.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <X className="h-5 w-5 text-gray-400 flex-shrink-0" />
                        <span className="text-gray-400">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button 
                    className={`w-full ${plan.popular ? 'bg-black hover:bg-gray-800' : 'bg-gray-900 hover:bg-black'} text-white`}
                    size="lg"
                  >
                    Choose {plan.name}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Membership Benefits</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Every membership includes these amazing benefits at no extra cost.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              "State-of-the-art equipment",
              "Clean locker rooms & showers",
              "Free parking",
              "Wifi throughout facility",
              "Water stations",
              "Towel service",
              "Equipment orientation",
              "Fitness assessments"
            ].map((benefit, index) => (
              <div key={index} className="flex items-center space-x-3">
                <Check className="h-6 w-6 text-green-500 flex-shrink-0" />
                <span className="text-gray-700 font-medium">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
          </div>

          <div className="space-y-8">
            {faqs.map((faq, index) => (
              <div key={index} className="border-b border-gray-200 pb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Join Hardwork Gym today and take the first step towards a healthier, stronger you.
          </p>
          <Button size="lg" className="bg-white text-black hover:bg-gray-200 text-lg px-8 py-4">
            Sign Up Now
          </Button>
        </div>
      </section>
    </div>
  );
}