{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "names": ["devalue", "webpack", "sources", "BUILD_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "SYSTEM_ENTRYPOINTS", "getRouteFromEntrypoint", "ampFirstEntryNamesMap", "getSortedRoutes", "spans", "srcEmptySsgManifest", "generateClientManifest", "compiler", "compilation", "assetMap", "rewrites", "compilationSpan", "get", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "normalizeRewrite", "item", "has", "source", "destination", "traceFn", "clientManifest", "__rewrites", "afterFiles", "map", "beforeFiles", "fallback", "appDependencies", "Set", "pages", "sortedPageKeys", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "getEntrypointFiles", "entrypoint", "getFiles", "file", "test", "replace", "processRoute", "r", "rewrite", "startsWith", "BuildManifestPlugin", "constructor", "options", "buildId", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "exportRuntime", "createAssets", "assets", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "lowPriorityFiles", "rootMainFiles", "ampFirstPages", "ampFirstEntryNames", "entryName", "pagePath", "push", "mainFiles", "compilationAssets", "getAssets", "p", "name", "endsWith", "info", "v", "values", "filesForPage", "ssgManifestPath", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "JSON", "stringify", "clientManifestPath", "apply", "hooks", "make", "tap", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AACA,OAAOA,aAAa,6BAA4B;AAChD,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,cAAc,EACdC,yBAAyB,EACzBC,wBAAwB,EACxBC,gCAAgC,EAChCC,oCAAoC,EACpCC,4CAA4C,EAC5CC,yCAAyC,EACzCC,+BAA+B,EAC/BC,kBAAkB,QACb,gCAA+B;AAEtC,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,KAAK,QAAQ,qBAAoB;AAM1C,sEAAsE;AACtE,kEAAkE;AAClE,cAAc;AACd,OAAO,MAAMC,sBAAsB,CAAC,4EAA4E,CAAC,CAAA;AAEjH,mFAAmF;AACnF,yCAAyC;AACzC,SAASC,uBACPC,QAAa,EACbC,WAAgB,EAChBC,QAAuB,EACvBC,QAAkC;IAElC,MAAMC,kBAAkBP,MAAMQ,GAAG,CAACJ,gBAAgBJ,MAAMQ,GAAG,CAACL;IAC5D,MAAMM,wBAAwBF,mCAAAA,gBAAiBG,UAAU,CACvD;IAGF,MAAMC,mBAAmB,CAACC;QAKxB,OAAO;YACLC,KAAKD,KAAKC,GAAG;YACbC,QAAQF,KAAKE,MAAM;YACnBC,aAAaH,KAAKG,WAAW;QAC/B;IACF;IAEA,OAAON,yCAAAA,sBAAuBO,OAAO,CAAC;YAGpBV,sBACCA,uBAGHA;QANd,MAAMW,iBAAsC;YAC1CC,YAAY;gBACVC,UAAU,GAAEb,uBAAAA,SAASa,UAAU,qBAAnBb,qBAAqBc,GAAG,CAAC,CAACR,OAASD,iBAAiBC;gBAChES,WAAW,GAAEf,wBAAAA,SAASe,WAAW,qBAApBf,sBAAsBc,GAAG,CAAC,CAACR,OACtCD,iBAAiBC;gBAEnBU,QAAQ,GAAEhB,qBAAAA,SAASgB,QAAQ,qBAAjBhB,mBAAmBc,GAAG,CAAC,CAACR,OAASD,iBAAiBC;YAC9D;QACF;QACA,MAAMW,kBAAkB,IAAIC,IAAInB,SAASoB,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiB3B,gBAAgB4B,OAAOC,IAAI,CAACvB,SAASoB,KAAK;QAEjEC,eAAeG,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAe1B,SAASoB,KAAK,CAACK,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACX,gBAAgBV,GAAG,CAACqB;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBlB,cAAc,CAACa,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEf,eAAemB,WAAW,GAAGV;QAE7B,OAAOzC,QAAQgC;IACjB;AACF;AAEA,OAAO,SAASoB,mBAAmBC,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACCpB,GAAG,CAAC,CAACoB,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEA,MAAMC,eAAe,CAACC;IACpB,MAAMC,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,CAACC,QAAQ9B,WAAW,CAAC+B,UAAU,CAAC,MAAM;QACxC,OAAO,AAACD,QAAgB9B,WAAW;IACrC;IACA,OAAO8B;AACT;AAEA,iFAAiF;AACjF,+GAA+G;AAC/G,eAAe,MAAME;IAOnBC,YAAYC,OAMX,CAAE;QACD,IAAI,CAACC,OAAO,GAAGD,QAAQC,OAAO;QAC9B,IAAI,CAACC,aAAa,GAAG,CAAC,CAACF,QAAQE,aAAa;QAC5C,IAAI,CAAC7C,QAAQ,GAAG;YACde,aAAa,EAAE;YACfF,YAAY,EAAE;YACdG,UAAU,EAAE;QACd;QACA,IAAI,CAAC8B,aAAa,GAAGH,QAAQG,aAAa;QAC1C,IAAI,CAAC9C,QAAQ,CAACe,WAAW,GAAG4B,QAAQ3C,QAAQ,CAACe,WAAW,CAACD,GAAG,CAACuB;QAC7D,IAAI,CAACrC,QAAQ,CAACa,UAAU,GAAG8B,QAAQ3C,QAAQ,CAACa,UAAU,CAACC,GAAG,CAACuB;QAC3D,IAAI,CAACrC,QAAQ,CAACgB,QAAQ,GAAG2B,QAAQ3C,QAAQ,CAACgB,QAAQ,CAACF,GAAG,CAACuB;QACvD,IAAI,CAACU,aAAa,GAAG,CAAC,CAACJ,QAAQI,aAAa;IAC9C;IAEAC,aAAanD,QAAa,EAAEC,WAAgB,EAAEmD,MAAW,EAAE;QACzD,MAAMhD,kBAAkBP,MAAMQ,GAAG,CAACJ,gBAAgBJ,MAAMQ,GAAG,CAACL;QAC5D,MAAMqD,mBAAmBjD,mCAAAA,gBAAiBG,UAAU,CAClD;QAEF,OAAO8C,oCAAAA,iBAAkBxC,OAAO,CAAC;YAC/B,MAAMyC,cAAgCrD,YAAYqD,WAAW;YAC7D,MAAMpD,WAAuC;gBAC3CqD,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACfC,kBAAkB,EAAE;gBACpBC,eAAe,EAAE;gBACjBrC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBsC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqBlE,sBAAsBU,GAAG,CAACJ;YACrD,IAAI4D,oBAAoB;gBACtB,KAAK,MAAMC,aAAaD,mBAAoB;oBAC1C,MAAME,WAAWrE,uBAAuBoE;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEA7D,SAAS0D,aAAa,CAACI,IAAI,CAACD;gBAC9B;YACF;YAEA,MAAME,YAAY,IAAI5C,IACpBa,mBAAmBoB,YAAYjD,GAAG,CAACjB;YAGrC,IAAI,IAAI,CAAC6D,aAAa,EAAE;gBACtB/C,SAASyD,aAAa,GAAG;uBACpB,IAAItC,IACLa,mBACEoB,YAAYjD,GAAG,CAAChB;iBAGrB;YACH;YAEA,MAAM6E,oBAIAjE,YAAYkE,SAAS;YAE3BjE,SAASqD,aAAa,GAAGW,kBACtBpC,MAAM,CAAC,CAACsC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAEC,IAAI,CAACC,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACEF,EAAEG,IAAI,IAAIjF,gDAAgD8E,EAAEG,IAAI;YAEpE,GACCtD,GAAG,CAAC,CAACuD,IAAMA,EAAEH,IAAI;YAEpBnE,SAASsD,QAAQ,GAAGtB,mBAClBoB,YAAYjD,GAAG,CAACd,4CAChBuC,MAAM,CAAC,CAACO,OAAS,CAAC4B,UAAUvD,GAAG,CAAC2B;YAElCnC,SAASuD,WAAW,GAAGvB,mBACrBoB,YAAYjD,GAAG,CAACb;YAGlB,KAAK,MAAM2C,cAAclC,YAAYqD,WAAW,CAACmB,MAAM,GAAI;gBACzD,IAAIhF,mBAAmBiB,GAAG,CAACyB,WAAWkC,IAAI,GAAG;gBAC7C,MAAMN,WAAWrE,uBAAuByC,WAAWkC,IAAI;gBAEvD,IAAI,CAACN,UAAU;oBACb;gBACF;gBAEA,MAAMW,eAAexC,mBAAmBC;gBAExCjC,SAASoB,KAAK,CAACyC,SAAS,GAAG;uBAAI,IAAI1C,IAAI;2BAAI4C;2BAAcS;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAAC1B,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5B9C,SAASwD,gBAAgB,CAACM,IAAI,CAC5B,CAAC,EAAE7E,yBAAyB,CAAC,EAAE,IAAI,CAAC4D,OAAO,CAAC,kBAAkB,CAAC;gBAEjE,MAAM4B,kBAAkB,CAAC,EAAExF,yBAAyB,CAAC,EAAE,IAAI,CAAC4D,OAAO,CAAC,gBAAgB,CAAC;gBAErF7C,SAASwD,gBAAgB,CAACM,IAAI,CAACW;gBAC/BvB,MAAM,CAACuB,gBAAgB,GAAG,IAAI3F,QAAQ4F,SAAS,CAAC9E;YAClD;YAEAI,SAASoB,KAAK,GAAGE,OAAOC,IAAI,CAACvB,SAASoB,KAAK,EACxCuD,IAAI,EACL,2BAA2B;aAC1BC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAG9E,SAASoB,KAAK,CAAC0D,EAAE,EAAGD,CAAAA,GAAI,CAAC;YAEtD,IAAIE,oBAAoBhG;YAExB,IAAI,IAAI,CAAC+D,aAAa,EAAE;gBACtBiC,oBAAoB,CAAC,SAAS,EAAEhG,eAAe,CAAC;YAClD;YAEAmE,MAAM,CAAC6B,kBAAkB,GAAG,IAAIjG,QAAQ4F,SAAS,CAC/CM,KAAKC,SAAS,CAACjF,UAAU,MAAM;YAGjC,IAAI,IAAI,CAACgD,aAAa,EAAE;gBACtBE,MAAM,CAAC,CAAC,OAAO,EAAElE,0BAA0B,GAAG,CAAC,CAAC,GAC9C,IAAIF,QAAQ4F,SAAS,CACnB,CAAC,sBAAsB,EAAEM,KAAKC,SAAS,CAACjF,UAAU,CAAC;YAEzD;YAEA,IAAI,CAAC,IAAI,CAAC8C,aAAa,EAAE;gBACvB,MAAMoC,qBAAqB,CAAC,EAAEjG,yBAAyB,CAAC,EAAE,IAAI,CAAC4D,OAAO,CAAC,kBAAkB,CAAC;gBAE1FK,MAAM,CAACgC,mBAAmB,GAAG,IAAIpG,QAAQ4F,SAAS,CAChD,CAAC,wBAAwB,EAAE7E,uBACzBC,UACAC,aACAC,UACA,IAAI,CAACC,QAAQ,EACb,uDAAuD,CAAC;YAE9D;YAEA,OAAOiD;QACT;IACF;IAEAiC,MAAMrF,QAA0B,EAAE;QAChCA,SAASsF,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACvF;YAC9CA,YAAYqF,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEnB,MAAM;gBACNqB,OAAO3G,QAAQ4G,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACxC;gBACC,IAAI,CAACD,YAAY,CAACnD,UAAUC,aAAamD;YAC3C;QAEJ;QACA;IACF;AACF"}