{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "names": ["nodePath", "spans", "isError", "nodeFileTrace", "CLIENT_REFERENCE_MANIFEST", "TRACE_OUTPUT_VERSION", "webpack", "sources", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "resolveExternal", "loadBindings", "isMatch", "getModuleBuildInfo", "getPageFilePath", "PLUGIN_NAME", "TRACE_IGNORES", "NOT_TRACEABLE", "getModuleFromDependency", "compilation", "dep", "moduleGraph", "getModule", "getFilesMapFromReasons", "fileList", "reasons", "ignoreFn", "parentFilesMap", "Map", "propagateToParents", "parents", "file", "seen", "Set", "parent", "has", "add", "parentFiles", "get", "set", "parentReason", "reason", "isInitial", "type", "length", "includes", "size", "TraceEntryPointsPlugin", "constructor", "rootDir", "appDir", "pagesDir", "appDirEnabled", "traceIgnores", "esmExternals", "outputFileTracingRoot", "turbotrace", "turbotraceContext", "chunksToTrace", "entryTraces", "tracingRoot", "createTraceAssets", "assets", "span", "readlink", "stat", "outputPath", "outputOptions", "path", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "entryFilesMap", "isTraceable", "some", "suffix", "endsWith", "entrypoint", "entrypoints", "values", "entryFiles", "chunk", "getEntrypointChunk", "getAllReferencedChunks", "files", "filePath", "join", "auxiliaryFiles", "binding", "isWasm", "turbo", "startTrace", "ignores", "contains", "dot", "result", "base", "processCwd", "readFile", "source", "relative", "replace", "Promise", "resolve", "reject", "inputFileSystem", "err", "data", "e", "code", "ignore", "mixedModules", "esmFileList", "for<PERSON>ach", "traceOutputName", "name", "traceOutputPath", "dirname", "allEntryFiles", "child", "delete", "startsWith", "clientManifestsForPage", "finalFiles", "push", "RawSource", "JSON", "stringify", "version", "tapfinishModules", "traceEntrypointsPluginSpan", "doResolve", "hooks", "finishModules", "tapAsync", "_stats", "callback", "finishModulesSpan", "entryNameMap", "entryModMap", "additionalEntries", "depModMap", "traceFn", "entries", "entry", "normalizedName", "isPage", "isApp", "dependencies", "entryMod", "resource", "moduleBuildInfo", "route", "absolutePath", "absolutePagePath", "request", "curMap", "mod", "originalSource", "buffer", "entryPaths", "Array", "from", "keys", "collectDependencies", "depMod", "entriesToTrace", "entryName", "curExtraEntries", "contextDirectory", "chunks", "entriesTrace", "action", "input", "logLevel", "showAll", "logAll", "depModArray", "traceEntryCount", "id", "job", "isCjs", "undefined", "isAsset", "isArray", "loaders", "normalizedEntry", "finalDeps", "extraEntry", "normalizedExtraEntry", "then", "apply", "compiler", "tap", "link", "stats", "compilationSpan", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_SUMMARIZE", "catch", "resolver", "resolverFactory", "getPkgName", "segments", "split", "slice", "getResolve", "options", "curResolver", "withOptions", "context", "fileDependencies", "missingDependencies", "contextDependencies", "resContext", "Error", "requestPath", "isAbsolute", "descriptionFileRoot", "sep", "rootSeparatorIndex", "indexOf", "separatorIndex", "lastIndexOf", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFile", "emitFile", "realpath", "_err", "dependencyType", "CJS_RESOLVE_OPTIONS", "fullySpecified", "modules", "extensions", "BASE_CJS_RESOLVE_OPTIONS", "alias", "ESM_RESOLVE_OPTIONS", "BASE_ESM_RESOLVE_OPTIONS", "isEsmRequested", "res", "_", "resRequest", "afterEmit", "tapPromise", "filter", "chunksTrace"], "mappings": "AAAA,OAAOA,cAAc,OAAM;AAE3B,SAASC,KAAK,QAAQ,qBAAoB;AAC1C,OAAOC,aAAa,wBAAuB;AAC3C,SACEC,aAAa,QAER,iCAAgC;AACvC,SACEC,yBAAyB,EACzBC,oBAAoB,QACf,gCAA+B;AACtC,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,wBAAwB,EACxBC,oBAAoB,EACpBC,eAAe,QACV,uBAAsB;AAE7B,SAASC,YAAY,QAAQ,YAAW;AACxC,SAASC,OAAO,QAAQ,gCAA+B;AACvD,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,eAAe,QAAQ,gBAAe;AAE/C,MAAMC,cAAc;AACpB,MAAMC,gBAAgB;IACpB;IACA;CACD;AAED,MAAMC,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,wBACPC,WAAgB,EAChBC,GAAQ;IAER,OAAOD,YAAYE,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEA,SAASG,uBACPC,QAAqB,EACrBC,OAA6B,EAC7BC,QAAqD;IAErD,4DAA4D;IAC5D,8DAA8D;IAC9D,aAAa;IACb,MAAMC,iBAAiB,IAAIC;IAE3B,SAASC,mBACPC,OAAoB,EACpBC,IAAY,EACZC,OAAO,IAAIC,KAAa;QAExB,KAAK,MAAMC,UAAUJ,WAAW,EAAE,CAAE;YAClC,IAAI,CAACE,KAAKG,GAAG,CAACD,SAAS;gBACrBF,KAAKI,GAAG,CAACF;gBACT,IAAIG,cAAcV,eAAeW,GAAG,CAACJ;gBAErC,IAAI,CAACG,aAAa;oBAChBA,cAAc,IAAIJ;oBAClBN,eAAeY,GAAG,CAACL,QAAQG;gBAC7B;gBAEA,IAAI,EAACX,4BAAAA,SAAWK,MAAMG,UAAS;oBAC7BG,YAAYD,GAAG,CAACL;gBAClB;gBACA,MAAMS,eAAef,QAAQa,GAAG,CAACJ;gBAEjC,IAAIM,gCAAAA,aAAcV,OAAO,EAAE;oBACzBD,mBAAmBW,aAAaV,OAAO,EAAEC,MAAMC;gBACjD;YACF;QACF;IACF;IAEA,KAAK,MAAMD,QAAQP,SAAW;QAC5B,MAAMiB,SAAShB,QAASa,GAAG,CAACP;QAC5B,MAAMW,YACJD,CAAAA,0BAAAA,OAAQE,IAAI,CAACC,MAAM,MAAK,KAAKH,OAAOE,IAAI,CAACE,QAAQ,CAAC;QAEpD,IACE,CAACJ,UACD,CAACA,OAAOX,OAAO,IACdY,aAAaD,OAAOX,OAAO,CAACgB,IAAI,KAAK,GACtC;YACA;QACF;QACAjB,mBAAmBY,OAAOX,OAAO,EAAEC;IACrC;IACA,OAAOJ;AACT;AA4BA,OAAO,MAAMoB;IAcXC,YAAY,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,qBAAqB,EACrBC,UAAU,EAUX,CAAE;aA/BIC,oBAAuC,CAAC;aAWvCC,gBAA0B,EAAE;QAqBlC,IAAI,CAACT,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACQ,WAAW,GAAG,IAAI/B;QACvB,IAAI,CAAC0B,YAAY,GAAGA;QACpB,IAAI,CAACF,aAAa,GAAGA;QACrB,IAAI,CAACC,YAAY,GAAGA,gBAAgB,EAAE;QACtC,IAAI,CAACO,WAAW,GAAGL,yBAAyBN;QAC5C,IAAI,CAACO,UAAU,GAAGA;IACpB;IAEA,2DAA2D;IAC3D,2BAA2B;IAC3B,MAAMK,kBACJ1C,WAAgB,EAChB2C,MAAW,EACXC,IAAU,EACVC,QAAa,EACbC,IAAS,EACT;QACA,MAAMC,aAAa/C,YAAYgD,aAAa,CAACC,IAAI;QAEjD,MAAML,KAAKM,UAAU,CAAC,uBAAuBC,YAAY,CAAC;YACxD,MAAMC,gBAAgB,IAAI3C;YAC1B,MAAM8B,gBAAgB,IAAIzB;YAE1B,MAAMuC,cAAc,CAACzC,OACnB,CAACd,cAAcwD,IAAI,CAAC,CAACC;oBACnB,OAAO3C,KAAK4C,QAAQ,CAACD;gBACvB;YAEF,KAAK,MAAME,cAAczD,YAAY0D,WAAW,CAACC,MAAM,GAAI;gBACzD,MAAMC,aAAa,IAAI9C;gBAEvB,KAAK,MAAM+C,SAASJ,WACjBK,kBAAkB,GAClBC,sBAAsB,GAAI;oBAC3B,KAAK,MAAMnD,QAAQiD,MAAMG,KAAK,CAAE;wBAC9B,IAAIX,YAAYzC,OAAO;4BACrB,MAAMqD,WAAWpF,SAASqF,IAAI,CAACnB,YAAYnC;4BAC3C2B,cAActB,GAAG,CAACgD;4BAClBL,WAAW3C,GAAG,CAACgD;wBACjB;oBACF;oBACA,KAAK,MAAMrD,QAAQiD,MAAMM,cAAc,CAAE;wBACvC,IAAId,YAAYzC,OAAO;4BACrB,MAAMqD,WAAWpF,SAASqF,IAAI,CAACnB,YAAYnC;4BAC3C2B,cAActB,GAAG,CAACgD;4BAClBL,WAAW3C,GAAG,CAACgD;wBACjB;oBACF;gBACF;gBACAb,cAAchC,GAAG,CAACqC,YAAYG;YAChC;YAEA,kCAAkC;YAClC,IAAI,IAAI,CAACvB,UAAU,EAAE;gBACnB,IAAI+B,UAAU,MAAM5E;gBACpB,IACE,EAAC4E,2BAAAA,QAASC,MAAM,KAChB,OAAOD,QAAQE,KAAK,CAACC,UAAU,KAAK,YACpC;oBACA,IAAI,CAAChC,aAAa,GAAG;2BAAIA;qBAAc;oBACvC;gBACF;YACF;YACA,MAAMiC,UAAU;mBAAI3E;mBAAkB,IAAI,CAACqC,YAAY;aAAC;YAExD,MAAM3B,WAAW,CAAC0C;gBAChB,OAAOxD,QAAQwD,MAAMuB,SAAS;oBAAEC,UAAU;oBAAMC,KAAK;gBAAK;YAC5D;YACA,MAAMC,SAAS,MAAM3F,cAAc;mBAAIuD;aAAc,EAAE;gBACrDqC,MAAM,IAAI,CAACnC,WAAW;gBACtBoC,YAAY,IAAI,CAAC/C,OAAO;gBACxBgD,UAAU,OAAO7B;oBACf,IAAIV,cAAcvB,GAAG,CAACiC,OAAO;4BAEzBN,0CAAAA;wBADF,MAAMoC,UACJpC,oCAAAA,MAAM,CACJ9D,SAASmG,QAAQ,CAACjC,YAAYE,MAAMgC,OAAO,CAAC,OAAO,KACpD,sBAFDtC,2CAAAA,kCAEGoC,MAAM,qBAFTpC,8CAAAA;wBAGF,IAAIoC,QAAQ,OAAOA;oBACrB;oBACA,IAAI;wBACF,OAAO,MAAM,IAAIG,QAAQ,CAACC,SAASC;4BAE/BpF,YAAYqF,eAAe,CACxBP,QAAQ,CACX7B,MAAM,CAACqC,KAAKC;gCACZ,IAAID,KAAK,OAAOF,OAAOE;gCACvBH,QAAQI;4BACV;wBACF;oBACF,EAAE,OAAOC,GAAG;wBACV,IAAIzG,QAAQyG,MAAOA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,QAAO,GAAI;4BAC9D,OAAO;wBACT;wBACA,MAAMD;oBACR;gBACF;gBACA3C;gBACAC;gBACA4C,QAAQnF;gBACRoF,cAAc;YAChB;YACA,MAAMrF,UAAUqE,OAAOrE,OAAO;YAC9B,MAAMD,WAAWsE,OAAOtE,QAAQ;YAChCsE,OAAOiB,WAAW,CAACC,OAAO,CAAC,CAACjF,OAASP,SAASY,GAAG,CAACL;YAElD,MAAMJ,iBAAiBJ,uBAAuBC,UAAUC;YAExD,KAAK,MAAM,CAACmD,YAAYG,WAAW,IAAIR,cAAe;gBACpD,MAAM0C,kBAAkB,CAAC,GAAG,EAAErC,WAAWsC,IAAI,CAAC,YAAY,CAAC;gBAC3D,MAAMC,kBAAkBnH,SAASoH,OAAO,CACtCpH,SAASqF,IAAI,CAACnB,YAAY+C;gBAE5B,MAAMI,gBAAgB,IAAIpF;gBAE1B8C,WAAWiC,OAAO,CAAC,CAACjF;wBAClBJ;qBAAAA,sBAAAA,eACGW,GAAG,CAACtC,SAASmG,QAAQ,CAAC,IAAI,CAACvC,WAAW,EAAE7B,2BAD3CJ,oBAEIqF,OAAO,CAAC,CAACM;wBACTD,cAAcjF,GAAG,CAACpC,SAASqF,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAE0D;oBACpD;gBACJ;gBACA,8CAA8C;gBAC9CvC,WAAWwC,MAAM,CAACvH,SAASqF,IAAI,CAACnB,YAAY,CAAC,GAAG,EAAEU,WAAWsC,IAAI,CAAC,GAAG,CAAC;gBAEtE,IAAItC,WAAWsC,IAAI,CAACM,UAAU,CAAC,SAAS;oBACtC,wCAAwC;oBACxC,MAAMC,yBACJ7C,WAAWsC,IAAI,CAACvC,QAAQ,CAAC,YACzBC,WAAWsC,IAAI,KAAK,mBACpBtC,WAAWsC,IAAI,KAAK,mBAChBlH,SAASqF,IAAI,CACXnB,YACA,MACAU,WAAWsC,IAAI,CAACd,OAAO,CAAC,QAAQ,OAC9B,MACAhG,4BACA,SAEJ;oBAEN,IAAIqH,2BAA2B,MAAM;wBACnC1C,WAAW3C,GAAG,CAACqF;oBACjB;gBACF;gBAEA,MAAMC,aAAuB,EAAE;gBAE/B,KAAK,MAAM3F,QAAQ,IAAIE,IAAI;uBACtB8C;uBACAsC;uBACC,IAAI,CAAC1D,WAAW,CAACrB,GAAG,CAACsC,WAAWsC,IAAI,KAAK,EAAE;iBAChD,EAAG;oBACF,IAAInF,MAAM;wBACR2F,WAAWC,IAAI,CACb3H,SAASmG,QAAQ,CAACgB,iBAAiBpF,MAAMqE,OAAO,CAAC,OAAO;oBAE5D;gBACF;gBAEAtC,MAAM,CAACmD,gBAAgB,GAAG,IAAI1G,QAAQqH,SAAS,CAC7CC,KAAKC,SAAS,CAAC;oBACbC,SAAS1H;oBACT8E,OAAOuC;gBACT;YAEJ;QACF;IACF;IAEAM,iBACE7G,WAAgC,EAChC8G,0BAAgC,EAChCC,SAKoB,EACpBlE,QAAa,EACbC,IAAS,EACT;QACA9C,YAAYgH,KAAK,CAACC,aAAa,CAACC,QAAQ,CACtCtH,aACA,OAAOuH,QAAaC;YAClB,MAAMC,oBACJP,2BAA2B5D,UAAU,CAAC;YACxC,MAAMmE,kBACHlE,YAAY,CAAC;gBACZ,gDAAgD;gBAChD,mDAAmD;gBACnD,oCAAoC;gBACpC,MAAMmE,eAAe,IAAI7G;gBACzB,MAAM8G,cAAc,IAAI9G;gBACxB,MAAM+G,oBAAoB,IAAI/G;gBAE9B,MAAMgH,YAAY,IAAIhH;gBAEtB4G,kBAAkBnE,UAAU,CAAC,eAAewE,OAAO,CAAC;oBAClD1H,YAAY2H,OAAO,CAAC9B,OAAO,CAAC,CAAC+B,OAAO7B;wBAClC,MAAM8B,iBAAiB9B,wBAAAA,KAAMd,OAAO,CAAC,OAAO;wBAE5C,MAAM6C,SAASD,eAAexB,UAAU,CAAC;wBACzC,MAAM0B,QACJ,IAAI,CAAC9F,aAAa,IAAI4F,eAAexB,UAAU,CAAC;wBAElD,IAAI0B,SAASD,QAAQ;4BACnB,KAAK,MAAM7H,OAAO2H,MAAMI,YAAY,CAAE;gCACpC,IAAI,CAAC/H,KAAK;gCACV,MAAMgI,WAAWlI,wBAAwBC,aAAaC;gCAEtD,2DAA2D;gCAC3D,yCAAyC;gCACzC,IAAIgI,YAAYA,SAASC,QAAQ,KAAK,IAAI;oCACxC,MAAMC,kBAAkBzI,mBAAmBuI;oCAC3C,wFAAwF;oCACxF,IAAIE,gBAAgBC,KAAK,EAAE;wCACzB,MAAMC,eAAe1I,gBAAgB;4CACnC2I,kBACEH,gBAAgBC,KAAK,CAACE,gBAAgB;4CACxCxG,SAAS,IAAI,CAACA,OAAO;4CACrBC,QAAQ,IAAI,CAACA,MAAM;4CACnBC,UAAU,IAAI,CAACA,QAAQ;wCACzB;wCAEA,qCAAqC;wCACrC,IACE,AAAC,IAAI,CAACA,QAAQ,IACZqG,aAAahC,UAAU,CAAC,IAAI,CAACrE,QAAQ,KACtC,IAAI,CAACD,MAAM,IAAIsG,aAAahC,UAAU,CAAC,IAAI,CAACtE,MAAM,GACnD;4CACAwF,YAAYnG,GAAG,CAACiH,cAAcJ;4CAC9BX,aAAalG,GAAG,CAACiH,cAActC;wCACjC;oCACF;oCAEA,wFAAwF;oCACxF,oEAAoE;oCACpE,IAAIkC,SAASM,OAAO,EAAE;wCACpB,IAAIC,SAAShB,kBAAkBrG,GAAG,CAAC4E;wCAEnC,IAAI,CAACyC,QAAQ;4CACXA,SAAS,IAAI/H;4CACb+G,kBAAkBpG,GAAG,CAAC2E,MAAMyC;wCAC9B;wCACAf,UAAUrG,GAAG,CAAC6G,SAASM,OAAO,EAAEN;wCAChCO,OAAOpH,GAAG,CAAC6G,SAASC,QAAQ,EAAED;oCAChC;gCACF;gCAEA,IAAIA,YAAYA,SAASC,QAAQ,EAAE;oCACjCZ,aAAalG,GAAG,CAAC6G,SAASC,QAAQ,EAAEnC;oCACpCwB,YAAYnG,GAAG,CAAC6G,SAASC,QAAQ,EAAED;oCAEnC,IAAIO,SAAShB,kBAAkBrG,GAAG,CAAC4E;oCAEnC,IAAI,CAACyC,QAAQ;wCACXA,SAAS,IAAI/H;wCACb+G,kBAAkBpG,GAAG,CAAC2E,MAAMyC;oCAC9B;oCACAf,UAAUrG,GAAG,CAAC6G,SAASC,QAAQ,EAAED;oCACjCO,OAAOpH,GAAG,CAAC6G,SAASC,QAAQ,EAAED;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEA,MAAMnD,WAAW,OACf7B;wBAMewF;oBAJf,MAAMA,MAAMhB,UAAUtG,GAAG,CAAC8B,SAASsE,YAAYpG,GAAG,CAAC8B;oBAEnD,oDAAoD;oBACpD,kCAAkC;oBAClC,MAAM8B,SAAS0D,wBAAAA,sBAAAA,IAAKC,cAAc,qBAAnBD,yBAAAA;oBAEf,IAAI1D,QAAQ;wBACV,OAAOA,OAAO4D,MAAM;oBACtB;oBACA,0CAA0C;oBAC1C,kDAAkD;oBAClD,OAAO;gBACT;gBAEA,MAAMC,aAAaC,MAAMC,IAAI,CAACvB,YAAYwB,IAAI;gBAE9C,MAAMC,sBAAsB,CAACP;oBAC3B,IAAI,CAACA,OAAO,CAACA,IAAIT,YAAY,EAAE;oBAE/B,KAAK,MAAM/H,OAAOwI,IAAIT,YAAY,CAAE;wBAClC,MAAMiB,SAASlJ,wBAAwBC,aAAaC;wBAEpD,IAAIgJ,CAAAA,0BAAAA,OAAQf,QAAQ,KAAI,CAACT,UAAUtG,GAAG,CAAC8H,OAAOf,QAAQ,GAAG;4BACvDT,UAAUrG,GAAG,CAAC6H,OAAOf,QAAQ,EAAEe;4BAC/BD,oBAAoBC;wBACtB;oBACF;gBACF;gBACA,MAAMC,iBAAiB;uBAAIN;iBAAW;gBAEtCA,WAAW/C,OAAO,CAAC,CAAC+B;oBAClBoB,oBAAoBzB,YAAYpG,GAAG,CAACyG;oBACpC,MAAMuB,YAAY7B,aAAanG,GAAG,CAACyG;oBACnC,MAAMwB,kBAAkB5B,kBAAkBrG,GAAG,CAACgI;oBAE9C,IAAIC,iBAAiB;wBACnBF,eAAe1C,IAAI,IAAI4C,gBAAgBL,IAAI;oBAC7C;gBACF;gBACA,kCAAkC;gBAClC,IAAI,IAAI,CAAC1G,UAAU,EAAE;oBACnB,IAAI+B,UAAU,MAAM5E;oBACpB,IACE,EAAC4E,2BAAAA,QAASC,MAAM,KAChB,OAAOD,QAAQE,KAAK,CAACC,UAAU,KAAK,YACpC;4BAEE,kBAQc,mBACF,mBACD;wBAXb,MAAM8E,mBACJ,EAAA,mBAAA,IAAI,CAAChH,UAAU,qBAAf,iBAAiBgH,gBAAgB,KAAI,IAAI,CAAC5G,WAAW;wBACvD,MAAM6G,SAAS;+BAAIJ;yBAAe;wBAElC,IAAI,CAAC5G,iBAAiB,CAACiH,YAAY,GAAG;4BACpCC,QAAQ;gCACNA,QAAQ;gCACRC,OAAOH;gCACPD;gCACAxE,YAAY,EAAA,oBAAA,IAAI,CAACxC,UAAU,qBAAf,kBAAiBwC,UAAU,KAAI,IAAI,CAAC/C,OAAO;gCACvD4H,QAAQ,GAAE,oBAAA,IAAI,CAACrH,UAAU,qBAAf,kBAAiBqH,QAAQ;gCACnCC,OAAO,GAAE,oBAAA,IAAI,CAACtH,UAAU,qBAAf,kBAAiBuH,MAAM;4BAClC;4BACA7H,QAAQ,IAAI,CAACD,OAAO;4BACpB+H,aAAahB,MAAMC,IAAI,CAACrB,UAAUsB,IAAI;4BACtCzB;4BACAvE,YAAY/C,YAAYgD,aAAa,CAACC,IAAI;wBAC5C;wBACA;oBACF;gBACF;gBACA,IAAI5C;gBACJ,IAAIC;gBACJ,MAAMkE,UAAU;uBACX3E;uBACA,IAAI,CAACqC,YAAY;oBACpB;iBACD;gBACD,MAAM3B,WAAW,CAAC0C;oBAChB,OAAOxD,QAAQwD,MAAMuB,SAAS;wBAAEC,UAAU;wBAAMC,KAAK;oBAAK;gBAC5D;gBAEA,MAAM2C,kBACHnE,UAAU,CAAC,mBAAmB;oBAC7B4G,iBAAiBZ,eAAezH,MAAM,GAAG;gBAC3C,GACC0B,YAAY,CAAC;oBACZ,MAAMwB,SAAS,MAAM3F,cAAckK,gBAAgB;wBACjDtE,MAAM,IAAI,CAACnC,WAAW;wBACtBoC,YAAY,IAAI,CAAC/C,OAAO;wBACxBgD;wBACAjC;wBACAC;wBACAqC,SAAS4B,YACL,OAAOgD,IAAIhJ,QAAQiJ,KAAKC;4BACtB,OAAOlD,UAAUgD,IAAIhJ,QAAQiJ,KAAK,CAACC;wBACrC,IACAC;wBACJxE,QAAQnF;wBACRoF,cAAc;oBAChB;oBACA,aAAa;oBACbtF,WAAWsE,OAAOtE,QAAQ;oBAC1BsE,OAAOiB,WAAW,CAACC,OAAO,CAAC,CAACjF,OAASP,SAASY,GAAG,CAACL;oBAClDN,UAAUqE,OAAOrE,OAAO;gBAC1B;gBAEF,MAAM+G,kBACHnE,UAAU,CAAC,wBACXC,YAAY,CAAC;oBACZ,MAAM3C,iBAAiBJ,uBACrBC,UACAC,SACA,CAACM;4BAMiBN;wBALhB,iDAAiD;wBACjD,wCAAwC;wBACxC,oCAAoC;wBACpCM,OAAO/B,SAASqF,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAE7B;wBACvC,MAAMqI,SAASxB,UAAUtG,GAAG,CAACP;wBAC7B,MAAMuJ,WAAU7J,eAAAA,QACba,GAAG,CAACtC,SAASmG,QAAQ,CAAC,IAAI,CAACvC,WAAW,EAAE7B,2BAD3BN,aAEZkB,IAAI,CAACE,QAAQ,CAAC;wBAElB,OACE,CAACyI,WACDtB,MAAMuB,OAAO,CAACnB,0BAAAA,OAAQoB,OAAO,KAC7BpB,OAAOoB,OAAO,CAAC5I,MAAM,GAAG;oBAE5B;oBAEFmH,WAAW/C,OAAO,CAAC,CAAC+B;4BAUlBpH;wBATA,MAAM2I,YAAY7B,aAAanG,GAAG,CAACyG;wBACnC,MAAM0C,kBAAkBzL,SAASmG,QAAQ,CACvC,IAAI,CAACvC,WAAW,EAChBmF;wBAGF,MAAMwB,kBAAkB5B,kBAAkBrG,GAAG,CAACgI;wBAC9C,MAAMoB,YAAY,IAAIzJ;yBAEtBN,sBAAAA,eAAeW,GAAG,CAACmJ,qCAAnB9J,oBAAqCqF,OAAO,CAAC,CAAC5F;4BAC5CsK,UAAUtJ,GAAG,CAACpC,SAASqF,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAExC;wBAChD;wBAEA,IAAImJ,iBAAiB;4BACnB,KAAK,MAAMoB,cAAcpB,gBAAgBL,IAAI,GAAI;oCAM/CvI;gCALA,MAAMiK,uBAAuB5L,SAASmG,QAAQ,CAC5C,IAAI,CAACvC,WAAW,EAChB+H;gCAEFD,UAAUtJ,GAAG,CAACuJ;iCACdhK,uBAAAA,eACGW,GAAG,CAACsJ,0CADPjK,qBAEIqF,OAAO,CAAC,CAAC5F;oCACTsK,UAAUtJ,GAAG,CAACpC,SAASqF,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAExC;gCAChD;4BACJ;wBACF;wBACA,IAAI,CAACuC,WAAW,CAACpB,GAAG,CAAC+H,WAAWoB;oBAClC;gBACF;YACJ,GACCG,IAAI,CACH,IAAMtD,YACN,CAAC9B,MAAQ8B,SAAS9B;QAExB;IAEJ;IAEAqF,MAAMC,QAA0B,EAAE;QAChCA,SAAS5D,KAAK,CAAChH,WAAW,CAAC6K,GAAG,CAACjL,aAAa,CAACI;YAC3C,MAAM6C,WAAW,OAAOI;gBACtB,IAAI;oBACF,OAAO,MAAM,IAAIiC,QAAQ,CAACC,SAASC;wBAE/BpF,YAAYqF,eAAe,CACxBxC,QAAQ,CACXI,MAAM,CAACqC,KAAKwF;4BACZ,IAAIxF,KAAK,OAAOF,OAAOE;4BACvBH,QAAQ2F;wBACV;oBACF;gBACF,EAAE,OAAOtF,GAAG;oBACV,IACEzG,QAAQyG,MACPA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,SAAQ,GAClE;wBACA,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YACA,MAAM1C,OAAO,OAAOG;gBAClB,IAAI;oBACF,OAAO,MAAM,IAAIiC,QAAQ,CAACC,SAASC;wBAC/BpF,YAAYqF,eAAe,CAACvC,IAAI,CAChCG,MACA,CAACqC,KAAKyF;4BACJ,IAAIzF,KAAK,OAAOF,OAAOE;4BACvBH,QAAQ4F;wBACV;oBAEJ;gBACF,EAAE,OAAOvF,GAAG;oBACV,IAAIzG,QAAQyG,MAAOA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,SAAQ,GAAI;wBAC/D,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YAEA,MAAMwF,kBAAkBlM,MAAMqC,GAAG,CAACnB,gBAAgBlB,MAAMqC,GAAG,CAACyJ;YAC5D,MAAM9D,6BAA6BkE,gBAAgB9H,UAAU,CAC3D;YAEF4D,2BAA2BY,OAAO,CAAC;gBACjC1H,YAAYgH,KAAK,CAACiE,aAAa,CAAC/D,QAAQ,CACtC;oBACEnB,MAAMnG;oBACNsL,OAAO/L,QAAQgM,WAAW,CAACC,8BAA8B;gBAC3D,GACA,CAACzI,QAAayE;oBACZ,IAAI,CAAC1E,iBAAiB,CACpB1C,aACA2C,QACAmE,4BACAjE,UACAC,MAEC4H,IAAI,CAAC,IAAMtD,YACXiE,KAAK,CAAC,CAAC/F,MAAQ8B,SAAS9B;gBAC7B;gBAGF,IAAIgG,WAAWtL,YAAYuL,eAAe,CAACpK,GAAG,CAAC;gBAE/C,SAASqK,WAAWzF,IAAY;oBAC9B,MAAM0F,WAAW1F,KAAK2F,KAAK,CAAC;oBAC5B,IAAI3F,IAAI,CAAC,EAAE,KAAK,OAAO0F,SAAShK,MAAM,GAAG,GACvC,OAAOgK,SAAShK,MAAM,GAAG,IAAIgK,SAASE,KAAK,CAAC,GAAG,GAAGzH,IAAI,CAAC,OAAO;oBAChE,OAAOuH,SAAShK,MAAM,GAAGgK,QAAQ,CAAC,EAAE,GAAG;gBACzC;gBAEA,MAAMG,aAAa,CAACC;oBAClB,MAAMC,cAAcR,SAASS,WAAW,CAACF;oBAEzC,OAAO,CACL9K,QACAwH,SACAyB,MAEA,IAAI9E,QAA2B,CAACC,SAASC;4BACvC,MAAM4G,UAAUnN,SAASoH,OAAO,CAAClF;4BAEjC+K,YAAY3G,OAAO,CACjB,CAAC,GACD6G,SACAzD,SACA;gCACE0D,kBAAkBjM,YAAYiM,gBAAgB;gCAC9CC,qBAAqBlM,YAAYkM,mBAAmB;gCACpDC,qBAAqBnM,YAAYmM,mBAAmB;4BACtD,GACA,OAAO7G,KAAUX,QAASyH;gCACxB,IAAI9G,KAAK,OAAOF,OAAOE;gCAEvB,IAAI,CAACX,QAAQ;oCACX,OAAOS,OAAO,IAAIiH,MAAM;gCAC1B;gCAEA,mDAAmD;gCACnD,sCAAsC;gCACtC,IAAI1H,OAAOjD,QAAQ,CAAC,QAAQiD,OAAOjD,QAAQ,CAAC,MAAM;oCAChDiD,SAASyH,CAAAA,8BAAAA,WAAYnJ,IAAI,KAAI0B;gCAC/B;gCAEA,IAAI;oCACF,oDAAoD;oCACpD,sDAAsD;oCACtD,yDAAyD;oCACzD,sDAAsD;oCACtD,IAAIA,OAAOjD,QAAQ,CAAC,iBAAiB;wCACnC,IAAI4K,cAAc3H,OACfM,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCAElB,IACE,CAACpG,SAAS0N,UAAU,CAAChE,YACrBA,QAAQ7G,QAAQ,CAAC,SACjB0K,8BAAAA,WAAYI,mBAAmB,GAC/B;gDAGgBhB;4CAFhBc,cAAc,AACZF,CAAAA,WAAWI,mBAAmB,GAC9BjE,QAAQoD,KAAK,CAACH,EAAAA,cAAAA,WAAWjD,6BAAXiD,YAAqB/J,MAAM,KAAI,KAC7C5C,SAAS4N,GAAG,GACZ,cAAa,EAEZxH,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCACpB;wCAEA,MAAMyH,qBAAqBJ,YAAYK,OAAO,CAAC;wCAC/C,IAAIC;wCACJ,MACE,AAACA,CAAAA,iBAAiBN,YAAYO,WAAW,CAAC,IAAG,IAC7CH,mBACA;4CACAJ,cAAcA,YAAYX,KAAK,CAAC,GAAGiB;4CACnC,MAAME,qBAAqB,CAAC,EAAER,YAAY,aAAa,CAAC;4CACxD,IAAI,MAAMtC,IAAI+C,MAAM,CAACD,qBAAqB;gDACxC,MAAM9C,IAAIgD,QAAQ,CAChB,MAAMhD,IAAIiD,QAAQ,CAACH,qBACnB,WACA/L;4CAEJ;wCACF;oCACF;gCACF,EAAE,OAAOmM,MAAM;gCACb,kDAAkD;gCAClD,sDAAsD;gCACxD;gCACA/H,QAAQ;oCAACR;oCAAQkH,QAAQsB,cAAc,KAAK;iCAAM;4BACpD;wBAEJ;gBACJ;gBAEA,MAAMC,sBAAsB;oBAC1B,GAAG9N,oBAAoB;oBACvB+N,gBAAgBnD;oBAChBoD,SAASpD;oBACTqD,YAAYrD;gBACd;gBACA,MAAMsD,2BAA2B;oBAC/B,GAAGJ,mBAAmB;oBACtBK,OAAO;gBACT;gBACA,MAAMC,sBAAsB;oBAC1B,GAAGrO,wBAAwB;oBAC3BgO,gBAAgBnD;oBAChBoD,SAASpD;oBACTqD,YAAYrD;gBACd;gBACA,MAAMyD,2BAA2B;oBAC/B,GAAGD,mBAAmB;oBACtBD,OAAO;gBACT;gBAEA,MAAM1G,YAAY,OAChBwB,SACAxH,QACAiJ,KACA4D;oBAEA,MAAM5B,UAAUnN,SAASoH,OAAO,CAAClF;oBACjC,gEAAgE;oBAChE,yBAAyB;oBACzB,MAAM,EAAE8M,GAAG,EAAE,GAAG,MAAMtO,gBACpB,IAAI,CAACuC,OAAO,EACZ,IAAI,CAACK,YAAY,EACjB6J,SACAzD,SACAqF,gBACA,CAAC,CAAC,IAAI,CAAC3L,aAAa,EACpB,CAAC4J,UAAY,CAACiC,GAAWC;4BACvB,OAAOnC,WAAWC,SAAS9K,QAAQgN,YAAY/D;wBACjD,GACAE,WACAA,WACAwD,qBACAN,qBACAO,0BACAH;oBAGF,IAAI,CAACK,KAAK;wBACR,MAAM,IAAIxB,MAAM,CAAC,kBAAkB,EAAE9D,QAAQ,MAAM,EAAExH,OAAO,CAAC;oBAC/D;oBACA,OAAO8M,IAAI5I,OAAO,CAAC,OAAO;gBAC5B;gBAEA,IAAI,CAAC4B,gBAAgB,CACnB7G,aACA8G,4BACAC,WACAlE,UACAC;YAEJ;QACF;QAEA,IAAI,IAAI,CAACT,UAAU,EAAE;YACnBuI,SAAS5D,KAAK,CAACgH,SAAS,CAACC,UAAU,CAACrO,aAAa;gBAC/C,IAAIwE,UAAU,MAAM5E;gBACpB,IACE,EAAC4E,2BAAAA,QAASC,MAAM,KAChB,OAAOD,QAAQE,KAAK,CAACC,UAAU,KAAK,YACpC;wBAaM,kBACU,mBACH,mBACC;oBAfd,MAAMC,UAAU;2BAAI3E;2BAAkB,IAAI,CAACqC,YAAY;qBAAC;oBAExD,MAAM3B,WAAW,CAAC0C;wBAChB,OAAOxD,QAAQwD,MAAMuB,SAAS;4BAAEC,UAAU;4BAAMC,KAAK;wBAAK;oBAC5D;oBACA,MAAM4E,SAAS,IAAI,CAAC/G,aAAa,CAAC2L,MAAM,CAAC,CAACrK,QAAU,CAACtD,SAASsD;oBAE9D,IAAI,CAACvB,iBAAiB,CAAC6L,WAAW,GAAG;wBACnC3E,QAAQ;4BACNA,QAAQ;4BACRC,OAAOH;4BACPD,kBACE,EAAA,mBAAA,IAAI,CAAChH,UAAU,qBAAf,iBAAiBgH,gBAAgB,KAAI,IAAI,CAAC5G,WAAW;4BACvDoC,YAAY,EAAA,oBAAA,IAAI,CAACxC,UAAU,qBAAf,kBAAiBwC,UAAU,KAAI,IAAI,CAAC/C,OAAO;4BACvD6H,OAAO,GAAE,oBAAA,IAAI,CAACtH,UAAU,qBAAf,kBAAiBuH,MAAM;4BAChCF,QAAQ,GAAE,oBAAA,IAAI,CAACrH,UAAU,qBAAf,kBAAiBqH,QAAQ;wBACrC;wBACA3G,YAAY6H,SAAS7H,UAAU;oBACjC;gBACF;YACF;QACF;IACF;AACF"}