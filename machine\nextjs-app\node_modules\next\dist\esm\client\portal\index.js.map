{"version": 3, "sources": ["../../../src/client/portal/index.tsx"], "names": ["useEffect", "useState", "createPortal", "Portal", "children", "type", "portalNode", "setPortalNode", "element", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,QAAO;AAC3C,SAASC,YAAY,QAAQ,YAAW;AAOxC,OAAO,MAAMC,SAAS;QAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAe;IACpD,MAAM,CAACC,YAAYC,cAAc,GAAGN,SAA6B;IAEjED,UAAU;QACR,MAAMQ,UAAUC,SAASC,aAAa,CAACL;QACvCI,SAASE,IAAI,CAACC,WAAW,CAACJ;QAC1BD,cAAcC;QACd,OAAO;YACLC,SAASE,IAAI,CAACE,WAAW,CAACL;QAC5B;IACF,GAAG;QAACH;KAAK;IAET,OAAOC,2BAAaJ,aAAaE,UAAUE,cAAc;AAC3D,EAAC"}