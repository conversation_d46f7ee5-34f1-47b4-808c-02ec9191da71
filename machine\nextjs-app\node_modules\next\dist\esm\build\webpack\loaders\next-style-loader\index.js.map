{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-style-loader/index.ts"], "names": ["path", "stringifyRequest", "loaderApi", "pitch", "loader", "request", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "options", "getOptions", "insert", "JSON", "stringify", "toString", "injectType", "esModule", "hmrCode", "hot", "join", "__dirname", "isSingleton", "require", "module", "exports"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,gBAAgB,QAAQ,0BAAyB;AAE1D,MAAMC,YAAY,KAAO;AAEzBA,UAAUC,KAAK,GAAG,SAASC,OAAkBC,OAAY;IACvD,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAEpD,OAAOF,WAAWG,OAAO,CAAC;QACxB,MAAMC,UAAU,IAAI,CAACC,UAAU;QAE/B,MAAMC,SACJ,OAAOF,QAAQE,MAAM,KAAK,cACtB,WACA,OAAOF,QAAQE,MAAM,KAAK,WAC1BC,KAAKC,SAAS,CAACJ,QAAQE,MAAM,IAC7BF,QAAQE,MAAM,CAACG,QAAQ;QAC7B,MAAMC,aAAaN,QAAQM,UAAU,IAAI;QACzC,MAAMC,WACJ,OAAOP,QAAQO,QAAQ,KAAK,cAAcP,QAAQO,QAAQ,GAAG;QAE/D,OAAOP,QAAQO,QAAQ;QAEvB,OAAQD;YACN,KAAK;gBAAW;oBACd,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;IAGT,EAAElB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;KAExC,EACEY,WACI,qBACA,CAAC,kBAAkB,EAAEhB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;;;2BAI5C,CAAC,CACtB;;;;;;;CAOL,CAAC,GACU;oBAEJ,OAAO,CAAC,EACNY,WACI,CAAC,gBAAgB,EAAEhB,iBACjB,IAAI,EACJ,CAAC,CAAC,EAAED,KAAKoB,IAAI,CAACC,WAAW,sCAAsC,CAAC,EAChE;gCACgB,EAAEpB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEJ,iBACnB,IAAI,EACJ,CAAC,CAAC,EAAED,KAAKoB,IAAI,CAACC,WAAW,sCAAsC,CAAC,EAChE;kCACkB,EAAEpB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;qEAEN,CAAC,CAC7D;;cAEK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;;;;AAI1B,EAAEM,QAAQ;;AAEV,EAAED,WAAW,sBAAsB,GAAG,CAAC;gBACjC;YAEA,KAAK;YACL,KAAK;gBAAyB;oBAC5B,MAAMK,cAAcN,eAAe;oBAEnC,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;wBAGW,EAAEI,QAAQ,2BAA2BR,QAAQ,GAAG;;;;;MAKlE,EAAEd,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;QAEvC,EACEY,WACI,CAAC;;;;;;;;;;eAUA,CAAC,GACF,CAAC,kBAAkB,EAAEhB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;eAc3D,CAAC,CACP;;;;;;;;;;CAUR,CAAC,GACU;oBAEJ,OAAO,CAAC,EACNY,WACI,CAAC,gBAAgB,EAAEhB,iBACjB,IAAI,EACJ,CAAC,CAAC,EAAED,KAAKoB,IAAI,CACXC,WACA,uCACA,CAAC,EACH;gCACgB,EAAEpB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEJ,iBACnB,IAAI,EACJ,CAAC,CAAC,EAAED,KAAKoB,IAAI,CACXC,WACA,uCACA,CAAC,EACH;kCACkB,EAAEpB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;;;;;aAM9D,CAAC,CACL;;;;cAIK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;oBACN,EAAEU,YAAY;;;;;;;;;;;;;;;;;;;AAmBlC,EAAEJ,QAAQ;;AAEV,EAAED,WAAW,mBAAmB,mBAAmB,UAAU,CAAC;gBACxD;YAEA,KAAK;YACL,KAAK;YACL;gBAAS;oBACP,MAAMK,cAAcN,eAAe;oBAEnC,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;wBAGW,EAAEI,QAAQ,2BAA2BR,QAAQ,GAAG;;;;MAIlE,EAAEd,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;QAEvC,EACEY,WACI,CAAC;;;;;;;;8BAQe,CAAC,GACjB,CAAC,kBAAkB,EAAEhB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;8BAgB5C,CAAC,CACtB;;;;;;;;CAQR,CAAC,GACU;oBAEJ,OAAO,CAAC,EACNY,WACI,CAAC,gBAAgB,EAAEhB,iBACjB,IAAI,EACJ,CAAC,CAAC,EAAED,KAAKoB,IAAI,CACXC,WACA,uCACA,CAAC,EACH;gCACgB,EAAEpB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEJ,iBACnB,IAAI,EACJ,CAAC,CAAC,EAAED,KAAKoB,IAAI,CACXC,WACA,uCACA,CAAC,EACH;kCACkB,EAAEpB,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAEI,QAAQ,CAAC,EAAE;;;;;;aAM9D,CAAC,CACL;;cAEK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;oBACN,EAAEU,YAAY;;;;AAIlC,EAAEJ,QAAQ;;AAEV,EAAED,WAAW,mBAAmB,mBAAmB,sBAAsB,CAAC;gBACpE;QACF;IACF;AACF;AAEAO,OAAOC,OAAO,GAAGvB"}