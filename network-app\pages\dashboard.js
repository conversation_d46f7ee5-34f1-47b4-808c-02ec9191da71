import { useState, useEffect } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart,
  Line,
  RadialBarChart,
  RadialBar,
} from "recharts";
import Layout from "../components/layout";
import {
  Activity,
  Users,
  Cpu,
  ShieldAlert,
  TrendingUp,
  TrendingDown,
  Wifi,
  HardDrive,
  Globe,
  Lock,
  AlertTriangle,
  Eye,
  Zap,
  Server,
  Shield,
  Database,
  Network,
} from "lucide-react";

const StatCard = ({ icon, label, value, trend, trendDirection, subtitle }) => (
  <div className="bg-black border border-gray-800 rounded-lg p-5 hover:border-gray-600 transition-all duration-300">
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <div className="p-3 bg-gray-800 rounded-lg mr-4">{icon}</div>
        <div>
          <p className="text-sm text-gray-400">{label}</p>
          <p className="text-2xl font-bold text-white">{value}</p>
          {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
        </div>
      </div>
      {trend && (
        <div className="text-right">
          <div
            className={`flex items-center text-sm ${
              trendDirection === "up" ? "text-green-400" : "text-red-400"
            }`}
          >
            {trendDirection === "up" ? (
              <TrendingUp size={16} />
            ) : (
              <TrendingDown size={16} />
            )}
            <span className="ml-1">{trend}</span>
          </div>
          <span className="text-xs text-gray-500">vs. last hour</span>
        </div>
      )}
    </div>
  </div>
);

const MiniChart = ({ title, value, data, type = "line" }) => (
  <div className="bg-black border border-gray-800 rounded-lg p-4">
    <div className="flex justify-between items-start mb-3">
      <h3 className="text-sm font-medium text-gray-300">{title}</h3>
      <span className="text-lg font-bold text-white">{value}</span>
    </div>
    <ResponsiveContainer width="100%" height={60}>
      {type === "line" ? (
        <LineChart data={data}>
          <Line
            type="monotone"
            dataKey="value"
            stroke="#ffffff"
            strokeWidth={2}
            dot={false}
          />
        </LineChart>
      ) : (
        <BarChart data={data}>
          <Bar dataKey="value" fill="#ffffff" radius={2} />
        </BarChart>
      )}
    </ResponsiveContainer>
  </div>
);

const AlertItem = ({ severity, message, time, type }) => (
  <div className="flex items-center p-3 border-l-4 border-gray-700 bg-gray-900/30 rounded-r-lg mb-2">
    <div
      className={`p-2 rounded-full mr-3 ${
        severity === "high"
          ? "bg-red-900/50"
          : severity === "medium"
          ? "bg-yellow-900/50"
          : "bg-blue-900/50"
      }`}
    >
      <AlertTriangle
        size={14}
        className={
          severity === "high"
            ? "text-red-400"
            : severity === "medium"
            ? "text-yellow-400"
            : "text-blue-400"
        }
      />
    </div>
    <div className="flex-1">
      <p className="text-sm text-white">{message}</p>
      <p className="text-xs text-gray-500">{time}</p>
    </div>
    <span
      className={`px-2 py-1 rounded text-xs ${
        severity === "high"
          ? "bg-red-900/50 text-red-300"
          : severity === "medium"
          ? "bg-yellow-900/50 text-yellow-300"
          : "bg-blue-900/50 text-blue-300"
      }`}
    >
      {type}
    </span>
  </div>
);

// Static data generators to ensure consistency
const generateStaticTrafficData = () => {
  const data = [];
  const baseTime = new Date();
  baseTime.setHours(0, 0, 0, 0); // Start from midnight
  
  for (let i = 0; i < 25; i++) {
    const hour = i;
    data.push({
      time: `${String(hour).padStart(2, "0")}:00`,
      internal: 150 + Math.sin(i * 0.5) * 50 + (i % 3) * 10,
      external: 80 + Math.cos(i * 0.3) * 30 + (i % 2) * 5,
      blocked: 20 + Math.sin(i * 0.8) * 15 + (i % 4) * 3,
    });
  }
  return data.map(item => ({
    ...item,
    internal: Math.floor(item.internal),
    external: Math.floor(item.external),
    blocked: Math.floor(item.blocked)
  }));
};

const generateStaticThreatData = () => {
  return [
    { day: "Mon", malware: 35, phishing: 18, ddos: 8 },
    { day: "Tue", malware: 28, phishing: 22, ddos: 12 },
    { day: "Wed", malware: 45, phishing: 15, ddos: 6 },
    { day: "Thu", malware: 32, phishing: 25, ddos: 15 },
    { day: "Fri", malware: 52, phishing: 28, ddos: 18 },
    { day: "Sat", malware: 38, phishing: 12, ddos: 4 },
    { day: "Sun", malware: 25, phishing: 8, ddos: 2 },
  ];
};

const generateStaticGeoData = () => [
  { country: "United States", requests: 1240, threats: 45 },
  { country: "China", requests: 890, threats: 123 },
  { country: "Russia", requests: 567, threats: 89 },
  { country: "Germany", requests: 445, threats: 12 },
  { country: "United Kingdom", requests: 334, threats: 8 },
];

const generateStaticMiniChartData = (seed) => {
  const data = [];
  for (let i = 0; i < 12; i++) {
    // Use seed to generate consistent data
    const value = Math.floor((Math.sin(i + seed) + 1) * 50);
    data.push({ value });
  }
  return data;
};

const protocolData = [
  { name: "HTTPS", value: 680, color: "#ffffff" },
  { name: "SSH", value: 180, color: "#e5e5e5" },
  { name: "RDP", value: 120, color: "#cccccc" },
  { name: "ICMP", value: 240, color: "#999999" },
  { name: "DNS", value: 380, color: "#666666" },
  { name: "FTP", value: 90, color: "#333333" },
];

const systemHealthData = [
  { name: "CPU", value: 65, color: "#ffffff" },
  { name: "Memory", value: 78, color: "#cccccc" },
  { name: "Disk", value: 45, color: "#999999" },
  { name: "Network", value: 89, color: "#666666" },
];

const initialAlerts = [
  {
    id: 1,
    severity: "high",
    message: "Multiple failed login attempts detected",
    time: "2 minutes ago",
    type: "Authentication",
  },
  {
    id: 2,
    severity: "medium",
    message: "Unusual traffic pattern from *************",
    time: "5 minutes ago",
    type: "Network",
  },
  {
    id: 3,
    severity: "low",
    message: "System backup completed successfully",
    time: "15 minutes ago",
    type: "System",
  },
  {
    id: 4,
    severity: "high",
    message: "Potential DDoS attack detected",
    time: "23 minutes ago",
    type: "Security",
  },
];

const initialStats = {
  cpuLoad: 34,
  memoryUsage: 67,
  diskUsage: 45,
  networkLatency: 12,
  activeConnections: 247,
  blockedRequests: 1834,
  uptime: 99.98,
  totalUsers: 14,
  deniedAccess: 17,
  dataTransfer: 2.4,
};

export default function AdvancedSecurityDashboard() {
  // State management with proper initialization
  const [trafficData, setTrafficData] = useState(generateStaticTrafficData());
  const [threatData] = useState(generateStaticThreatData());
  const [geoData] = useState(generateStaticGeoData());
  const [alerts, setAlerts] = useState(initialAlerts);
  const [currentTime, setCurrentTime] = useState("");
  const [isMounted, setIsMounted] = useState(false);
  const [stats, setStats] = useState(initialStats);

  // Handle client-side mounting
  useEffect(() => {
    setIsMounted(true);
    setCurrentTime(new Date().toLocaleTimeString());
  }, []);

  // Update time and data only on client side
  useEffect(() => {
    if (!isMounted) return;

    const interval = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());

      // Update traffic data
      setTrafficData((prev) => {
        const newData = [...prev.slice(1)];
        const now = new Date();
        newData.push({
          time: now.toLocaleTimeString("en-GB", {
            hour: "2-digit",
            minute: "2-digit",
          }),
          internal: Math.floor(Math.random() * (200 - 80 + 1)) + 80,
          external: Math.floor(Math.random() * (120 - 30 + 1)) + 30,
          blocked: Math.floor(Math.random() * (40 - 5 + 1)) + 5,
        });
        return newData;
      });

      // Update stats
      setStats((prev) => ({
        ...prev,
        cpuLoad: Math.floor(Math.random() * (60 - 20 + 1)) + 20,
        memoryUsage: Math.floor(Math.random() * (80 - 50 + 1)) + 50,
        networkLatency: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
        activeConnections: Math.floor(Math.random() * (300 - 200 + 1)) + 200,
        dataTransfer: (Math.random() * (5 - 1) + 1).toFixed(1),
      }));

      // Occasionally add new alerts
      if (Math.random() > 0.7) {
        const newAlert = {
          id: Date.now(),
          severity: ["high", "medium", "low"][Math.floor(Math.random() * 3)],
          message: [
            "Suspicious file download detected",
            "Firewall rule triggered",
            "System update available",
            "Unusual bandwidth usage",
            "New device connected",
          ][Math.floor(Math.random() * 5)],
          time: "Just now",
          type: ["Security", "Network", "System"][
            Math.floor(Math.random() * 3)
          ],
        };
        setAlerts((prev) => [newAlert, ...prev.slice(0, 7)]);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [isMounted]);

  // Don't render dynamic content until mounted
  if (!isMounted) {
    return (
      <Layout>
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Security Operations Center
          </h1>
          <p className="text-gray-400">
            Real-time monitoring and threat detection dashboard
          </p>
          <div className="flex items-center mt-4 space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-gray-300">System Operational</span>
            </div>
            <div className="text-sm text-gray-400">
              Loading...
            </div>
          </div>
        </div>
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 mb-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-gray-800 h-24 rounded-lg"></div>
            ))}
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-white mb-2">
          Security Operations Center
        </h1>
        <p className="text-gray-400">
          Real-time monitoring and threat detection dashboard
        </p>
        <div className="flex items-center mt-4 space-x-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span className="text-sm text-gray-300">System Operational</span>
          </div>
          <div className="text-sm text-gray-400">
            Last updated: {currentTime}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 mb-8">
        <StatCard
          icon={<Activity size={22} className="text-white" />}
          label="CPU Load"
          value={`${stats.cpuLoad}%`}
          trend="+2.1%"
          trendDirection="up"
        />
        <StatCard
          icon={<HardDrive size={22} className="text-white" />}
          label="Memory Usage"
          value={`${stats.memoryUsage}%`}
          trend="-1.2%"
          trendDirection="down"
        />
        <StatCard
          icon={<Wifi size={22} className="text-white" />}
          label="Network Latency"
          value={`${stats.networkLatency}ms`}
          trend="+0.5ms"
          trendDirection="up"
        />
        <StatCard
          icon={<Globe size={22} className="text-white" />}
          label="Active Connections"
          value={stats.activeConnections}
          trend="+12"
          trendDirection="up"
        />
        <StatCard
          icon={<ShieldAlert size={22} className="text-white" />}
          label="Blocked Requests"
          value={stats.blockedRequests}
          trend="+47"
          trendDirection="up"
        />
        <StatCard
          icon={<Database size={22} className="text-white" />}
          label="Data Transfer"
          value={`${stats.dataTransfer}GB`}
          trend="1.2GB"
          trendDirection="up"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Network Traffic Chart */}
        <div className="lg:col-span-2 bg-black p-6 rounded-lg border border-gray-800">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">
              Network Traffic Analysis
            </h2>
            <div className="flex space-x-4 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-white rounded-full mr-2"></div>
                Internal
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                External
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gray-600 rounded-full mr-2"></div>
                Blocked
              </div>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={trafficData}>
              <defs>
                <linearGradient id="colorInternal" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#ffffff" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#ffffff" stopOpacity={0} />
                </linearGradient>
                <linearGradient id="colorExternal" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#9ca3af" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#9ca3af" stopOpacity={0} />
                </linearGradient>
                <linearGradient id="colorBlocked" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#6b7280" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#6b7280" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="time" stroke="#6b7280" />
              <YAxis stroke="#6b7280" />
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#1f2937",
                  border: "1px solid #4b5563",
                  borderRadius: "8px",
                }}
              />
              <Area
                type="monotone"
                dataKey="internal"
                stackId="1"
                stroke="#ffffff"
                fill="url(#colorInternal)"
              />
              <Area
                type="monotone"
                dataKey="external"
                stackId="1"
                stroke="#9ca3af"
                fill="url(#colorExternal)"
              />
              <Area
                type="monotone"
                dataKey="blocked"
                stackId="1"
                stroke="#6b7280"
                fill="url(#colorBlocked)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* System Health Radial Chart */}
        <div className="bg-black p-6 rounded-lg border border-gray-800">
          <h2 className="text-xl font-semibold text-white mb-4">
            System Health
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <RadialBarChart
              cx="50%"
              cy="50%"
              innerRadius="20%"
              outerRadius="90%"
              data={systemHealthData}
            >
              <RadialBar dataKey="value" cornerRadius={10} fill="#ffffff" />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#1f2937",
                  border: "1px solid #4b5563",
                  borderRadius: "8px",
                }}
              />
            </RadialBarChart>
          </ResponsiveContainer>
          <div className="grid grid-cols-2 gap-2 mt-4">
            {systemHealthData.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-2 bg-gray-900 rounded"
              >
                <span className="text-sm text-gray-300">{item.name}</span>
                <span className="text-sm font-bold text-white">
                  {item.value}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6 mb-8">
        <MiniChart
          title="Disk I/O"
          value="1.2K/s"
          data={generateStaticMiniChartData(1)}
        />
        <MiniChart
          title="Bandwidth"
          value="45.2MB/s"
          data={generateStaticMiniChartData(2)}
        />
        <MiniChart
          title="Threats Blocked"
          value="127"
          data={generateStaticMiniChartData(3)}
          type="bar"
        />
        <MiniChart
          title="Response Time"
          value="180ms"
          data={generateStaticMiniChartData(4)}
        />
        <MiniChart
          title="Error Rate"
          value="0.02%"
          data={generateStaticMiniChartData(5)}
        />
        <MiniChart
          title="Uptime"
          value="99.98%"
          data={generateStaticMiniChartData(6)}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Threat Detection Chart */}
        <div className="bg-black p-6 rounded-lg border border-gray-800">
          <h2 className="text-xl font-semibold text-white mb-4">
            Weekly Threat Detection
          </h2>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart
              data={threatData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <XAxis dataKey="day" stroke="#6b7280" />
              <YAxis stroke="#6b7280" />
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#1f2937",
                  border: "1px solid #4b5563",
                  borderRadius: "8px",
                }}
              />
              <Bar
                dataKey="malware"
                stackId="a"
                fill="#ffffff"
                radius={[0, 0, 0, 0]}
              />
              <Bar
                dataKey="phishing"
                stackId="a"
                fill="#cccccc"
                radius={[0, 0, 0, 0]}
              />
              <Bar
                dataKey="ddos"
                stackId="a"
                fill="#999999"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Protocol Distribution */}
        <div className="bg-black p-6 rounded-lg border border-gray-800">
          <h2 className="text-xl font-semibold text-white mb-4">
            Protocol Distribution
          </h2>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={protocolData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="value"
              >
                {protocolData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: "#1f2937",
                  border: "1px solid #4b5563",
                  borderRadius: "8px",
                }}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="grid grid-cols-2 gap-2 mt-4">
            {protocolData.map((item, index) => (
              <div key={index} className="flex items-center">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-gray-300">
                  {item.name}: {item.value}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Geographic Requests Table */}
        <div className="bg-black p-6 rounded-lg border border-gray-800">
          <h2 className="text-xl font-semibold text-white mb-4">
            Geographic Request Analysis
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left text-gray-300">
              <thead className="text-xs text-gray-400 uppercase border-b border-gray-800">
                <tr>
                  <th className="px-4 py-3">Country</th>
                  <th className="px-4 py-3 text-right">Requests</th>
                  <th className="px-4 py-3 text-right">Threats</th>
                  <th className="px-4 py-3 text-right">Risk</th>
                </tr>
              </thead>
              <tbody>
                {geoData.map((country, index) => (
                  <tr
                    key={index}
                    className="border-b border-gray-800 hover:bg-gray-900 transition-colors"
                  >
                    <td className="px-4 py-3 font-medium text-white">
                      {country.country}
                    </td>
                    <td className="px-4 py-3 text-right">
                      {country.requests.toLocaleString()}
                    </td>
                    <td className="px-4 py-3 text-right">{country.threats}</td>
                    <td className="px-4 py-3 text-right">
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          country.threats / country.requests > 0.1
                            ? "bg-red-900/50 text-red-300"
                            : country.threats / country.requests > 0.05
                            ? "bg-yellow-900/50 text-yellow-300"
                            : "bg-green-900/50 text-green-300"
                        }`}
                      >
                        {((country.threats / country.requests) * 100).toFixed(
                          1
                        )}
                        %
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Security Alerts */}
        <div className="bg-black p-6 rounded-lg border border-gray-800">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">
              Security Alerts
            </h2>
            <span className="text-sm text-gray-400">
              {alerts.length} active
            </span>
          </div>
          <div className="space-y-2 max-h-80 overflow-y-auto">
            {alerts.map((alert) => (
              <AlertItem key={alert.id} {...alert} />
            ))}
          </div>
        </div>
      </div>
    </Layout>
  );
}