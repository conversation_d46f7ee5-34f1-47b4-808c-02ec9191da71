{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts"], "names": ["CacheStates", "createRouterCache<PERSON>ey", "fillLazyItemsTillLeafWithHead", "newCache", "existingCache", "routerState", "head", "wasPrefetched", "isLastSegment", "Object", "keys", "length", "key", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "parallelRouteCacheNode", "Map", "existingCacheNode", "newCacheNode", "status", "data", "subTreeData", "LAZY_INITIALIZED", "set", "existingParallelRoutes", "undefined"], "mappings": "AAAA,SAEEA,WAAW,QACN,wDAAuD;AAE9D,SAASC,oBAAoB,QAAQ,4BAA2B;AAEhE,OAAO,SAASC,8BACdC,QAAmB,EACnBC,aAAoC,EACpCC,WAA8B,EAC9BC,IAAqB,EACrBC,aAAuB;IAEvB,MAAMC,gBAAgBC,OAAOC,IAAI,CAACL,WAAW,CAAC,EAAE,EAAEM,MAAM,KAAK;IAC7D,IAAIH,eAAe;QACjBL,SAASG,IAAI,GAAGA;QAChB;IACF;IACA,+FAA+F;IAC/F,IAAK,MAAMM,OAAOP,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMQ,qBAAqBR,WAAW,CAAC,EAAE,CAACO,IAAI;QAC9C,MAAME,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAWd,qBAAqBa;QAEtC,IAAIV,eAAe;YACjB,MAAMY,kCACJZ,cAAca,cAAc,CAACC,GAAG,CAACN;YACnC,IAAII,iCAAiC;gBACnC,IAAIG,yBAAyB,IAAIC,IAAIJ;gBACrC,MAAMK,oBAAoBF,uBAAuBD,GAAG,CAACH;gBACrD,MAAMO,eACJf,iBAAiBc,oBACZ;oBACCE,QAAQF,kBAAkBE,MAAM;oBAChCC,MAAMH,kBAAkBG,IAAI;oBAC5BC,aAAaJ,kBAAkBI,WAAW;oBAC1CR,gBAAgB,IAAIG,IAAIC,kBAAkBJ,cAAc;gBAC1D,IACA;oBACEM,QAAQvB,YAAY0B,gBAAgB;oBACpCF,MAAM;oBACNC,aAAa;oBACbR,gBAAgB,IAAIG,IAAIC,qCAAAA,kBAAmBJ,cAAc;gBAC3D;gBACN,mDAAmD;gBACnDE,uBAAuBQ,GAAG,CAACZ,UAAUO;gBACrC,qEAAqE;gBACrEpB,8BACEoB,cACAD,mBACAR,oBACAP,MACAC;gBAGFJ,SAASc,cAAc,CAACU,GAAG,CAACf,KAAKO;gBACjC;YACF;QACF;QAEA,MAAMG,eAA0B;YAC9BC,QAAQvB,YAAY0B,gBAAgB;YACpCF,MAAM;YACNC,aAAa;YACbR,gBAAgB,IAAIG;QACtB;QAEA,MAAMQ,yBAAyBzB,SAASc,cAAc,CAACC,GAAG,CAACN;QAC3D,IAAIgB,wBAAwB;YAC1BA,uBAAuBD,GAAG,CAACZ,UAAUO;QACvC,OAAO;YACLnB,SAASc,cAAc,CAACU,GAAG,CAACf,KAAK,IAAIQ,IAAI;gBAAC;oBAACL;oBAAUO;iBAAa;aAAC;QACrE;QAEApB,8BACEoB,cACAO,WACAhB,oBACAP,MACAC;IAEJ;AACF"}