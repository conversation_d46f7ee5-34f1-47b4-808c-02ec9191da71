{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/RootLayoutError.tsx"], "names": ["React", "Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "Overlay", "Terminal", "noop", "css", "RootLayoutError", "BuildError", "missingTags", "message", "length", "join", "useCallback", "fixed", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "className", "h4", "id", "content", "footer", "p", "small", "styles"], "mappings": ";;;;;;;;;;AAAA,OAAOA,WAAW,QAAO;AACzB,SACEC,MAAM,EACNC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,uBAAsB;AAC7B,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,QAAQ,QAAQ,yBAAwB;AACjD,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AAItD,OAAO,MAAMC,kBACX,SAASC,WAAW,KAAe;IAAf,IAAA,EAAEC,WAAW,EAAE,GAAf;IAClB,MAAMC,UACJ,4FACA,CAAA,AAAC,qCACCD,CAAAA,YAAYE,MAAM,KAAK,IAAI,KAAK,GAAE,IACnC,IAAE,IACHF,YAAYG,IAAI,CAAC;IAEnB,MAAMP,OAAOP,MAAMe,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,oBAACV;QAAQW,OAAAA;qBACP,oBAACf;QACCgB,MAAK;QACLC,mBAAgB;QAChBC,oBAAiB;QACjBC,SAASb;qBAET,oBAACJ,mCACC,oBAACC;QAAaiB,WAAU;qBACtB,oBAACC;QAAGC,IAAG;OAA4C,yCAIrD,oBAACrB;QAAWmB,WAAU;qBACpB,oBAACf;QAASkB,SAASZ;sBACnB,oBAACa,8BACC,oBAACC;QAAEH,IAAG;qBACJ,oBAACI,eAAM;AAWvB,EAAC;AAEH,OAAO,MAAMC,SAASpB,uBAiBrB"}