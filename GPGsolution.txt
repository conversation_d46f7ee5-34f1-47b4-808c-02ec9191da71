solution stage 2 gpg key :

git clone https://github.com/hardowork/hardwork.git
cd hardwork

~/hardwork $ git log --all --pretty=format:"%H %s"
175eea07ceffb1faac79881201428fabba466e45 Remove private key from current working directory
af62df24b05eb1d7ce8e16e47bb1a19a12768cad Add private key with passphrase
5c4bb3affb9c5c70992e7c6a45eb84a120e6717d Remove dontreadme.md
7c23c00fc07da0eba692a03bc74ef84004c40f94 developer pass
634fd0eb70adb1e16f645160ec01d85853c102fa Remove private key for security
fb7473e0aff66a7159daf993b5c33fa19b0e8d63 Add private key for 5 min
391e0aecc579d393c49a193dbc0d33a2ae4972d0 Create dontreadme.md 


]~/hardwork $ git show af62df24b05eb1d7ce8e16e47bb1a19a12768cad -- private_key.asc
commit af62df24b05eb1d7ce8e16e47bb1a19a12768cad (HEAD)
Author: hardwork <<EMAIL>>
Date:   Tue Jun 3 17:23:29 2025 +0100

    Add private key with passphrase

diff --git a/private_key.asc b/private_key.asc
new file mode 100644
index 0000000..c116f4a
--- /dev/null
+++ b/private_key.asc
@@ -0,0 +1,17 @@
+-----BEGIN PGP PRIVATE KEY BLOCK-----
+
+lIYEaD8TvhYJKwYBBAHaRw8BAQdAbb6ZYL0m5S0E5QsS4fsjLEZNYIZrhoLF1CgC
+dw8F6P3+BwMCp7xJt5KtHjX5vUlPHrzhhVqE5kPonqmKCmILNkT294eczKYvRpOl
+9J/V+CMiergtkHiekq5gFEtsXeryNcKmO8oXMUKCwIUvmTO8s/B6BrQgaGFyZHdv
+cmsgPGJsYWNrMV8xaGF0QHByb3Rvbi5tZT6ImQQTFgoAQRYhBNPdxlXcA80zbDgu
+vUEfikJ5ueTuBQJoPxO+AhsDBQkFo5qABQsJCAcCAiICBhUKCQgLAgQWAgMBAh4H
+AheAAAoJEEEfikJ5ueTuxNoBAL2MRqYWgFUvQvMK5IIRk2sj2mdvtW724tGuW8kz
+E4mAAQDlpFhNNaS428tj6jB6BjPiYbcJ7ydNMK4cZmcSHss2D5yLBGg/E74SCisG
+AQQBl1UBBQEBB0CNDVXGTWrXI/E8B/rxhMI/bQOY5a677/H6TK8ntmiWMwMBCAf+
+BwMCxfWm5Ujfo0r5ReZWTuCfQoZJHVPavMBd6B0zQbyh1hMZg0OfE23R0wrW6ATb
+7rSXXyaM/O58l+uKxxm4eCYqRjQn6Zy/oPwtq9h5SpkxIYh+BBgWCgAmFiEE093G
+VdwDzTNsOC69QR+KQnm55O4FAmg/E74CGwwFCQWjmoAACgkQQR+KQnm55O7qVAEA
+yX1v0sBPF3AE4ra4Sp0l/HueEbCVNQhEhEE9qIfWE8ABAMNa+M0nke+HXLeq6SRM
+mWS/Za2+IHQOOKNbyK9gXpkL

~/hardwork $ git checkout af62df24b05eb1d7ce8e16e47bb1a19a12768cad -- private_key.asc


~/hardwork $ cat private_key.asc
-----BEGIN PGP PRIVATE KEY BLOCK-----

lIYEaD8TvhYJKwYBBAHaRw8BAQdAbb6ZYL0m5S0E5QsS4fsjLEZNYIZrhoLF1CgC
dw8F6P3+BwMCp7xJt5KtHjX5vUlPHrzhhVqE5kPonqmKCmILNkT294eczKYvRpOl
9J/V+CMiergtkHiekq5gFEtsXeryNcKmO8oXMUKCwIUvmTO8s/B6BrQgaGFyZHdv
cmsgPGJsYWNrMV8xaGF0QHByb3Rvbi5tZT6ImQQTFgoAQRYhBNPdxlXcA80zbDgu
vUEfikJ5ueTuBQJoPxO+AhsDBQkFo5qABQsJCAcCAiICBhUKCQgLAgQWAgMBAh4H
AheAAAoJEEEfikJ5ueTuxNoBAL2MRqYWgFUvQvMK5IIRk2sj2mdvtW724tGuW8kz
E4mAAQDlpFhNNaS428tj6jB6BjPiYbcJ7ydNMK4cZmcSHss2D5yLBGg/E74SCisG
AQQBl1UBBQEBB0CNDVXGTWrXI/E8B/rxhMI/bQOY5a677/H6TK8ntmiWMwMBCAf+
BwMCxfWm5Ujfo0r5ReZWTuCfQoZJHVPavMBd6B0zQbyh1hMZg0OfE23R0wrW6ATb
7rSXXyaM/O58l+uKxxm4eCYqRjQn6Zy/oPwtq9h5SpkxIYh+BBgWCgAmFiEE093G
VdwDzTNsOC69QR+KQnm55O4FAmg/E74CGwwFCQWjmoAACgkQQR+KQnm55O7qVAEA
yX1v0sBPF3AE4ra4Sp0l/HueEbCVNQhEhEE9qIfWE8ABAMNa+M0nke+HXLeq6SRM
mWS/Za2+IHQOOKNbyK9gXpkL
=Y9xG
-----END PGP PRIVATE KEY BLOCK-----



┌──[HQ🚀🌐172.26.215.99|172.18.0.1|172.17.0.1|172.19.0.1🔥bz7]
└──╼[👾]~/hardwork $ cat passlist.txt
bbbbbb
f
d
dq
sd
q
sd
hardwork123
┌──[HQ🚀🌐172.26.215.99|172.18.0.1|172.17.0.1|172.19.0.1🔥bz7]
└──╼[👾]~/hardwork $ cat dec.sh
#!/bin/bash

ENCRYPTED_FILE="cloud.passwd.gpg"
OUTPUT_FILE="decrypted_password.txt"
WORDLIST="passlist.txt"

if [ ! -f "$ENCRYPTED_FILE" ]; then
  echo "Encrypted file $ENCRYPTED_FILE not found!"
  exit 1
fi

if [ ! -f "$WORDLIST" ]; then
  echo "Wordlist file $WORDLIST not found!"
  exit 1
fi

while IFS= read -r pass; do
  echo "Trying passphrase: $pass"
  echo "$pass" | gpg --batch --yes --passphrase-fd 0 --pinentry-mode loopback --output "$OUTPUT_FILE" --decrypt "$ENCRYPTED_FILE" 2>/dev/null
  if [ $? -eq 0 ]; then
    echo "Success! Passphrase is: $pass"
    echo "Decrypted content:"
    cat "$OUTPUT_FILE"
    exit 0
  fi
done < "$WORDLIST"

echo "Passphrase not found in the wordlist."
exit 1
┌──[HQ🚀🌐172.26.215.99|172.18.0.1|172.17.0.1|172.19.0.1🔥bz7]
└──╼[👾]~/hardwork $ ./dec.sh
Trying passphrase: bbbbbb
Trying passphrase: f
Trying passphrase: d
Trying passphrase: dq
Trying passphrase: sd
Trying passphrase: q
Trying passphrase: sd
Trying passphrase: hardwork123
Success! Passphrase is: hardwork123
Decrypted content:
hardwork112
