{"version": 3, "sources": ["../../../src/client/dev/amp-dev.ts"], "names": ["displayContent", "initOnDemandEntries", "addMessageListener", "connectHMR", "HMR_ACTIONS_SENT_TO_BROWSER", "data", "JSON", "parse", "document", "getElementById", "textContent", "window", "__NEXT_DATA__", "assetPrefix", "page", "mostRecentHash", "curHash", "__webpack_hash__", "hotUpdatePath", "endsWith", "isUpdateAvailable", "canApplyUpdates", "module", "hot", "status", "tryApplyUpdates", "res", "fetch", "__webpack_runtime_id__", "jsonData", "json", "curPage", "pageUpdated", "Array", "isArray", "c", "Object", "keys", "some", "mod", "indexOf", "startsWith", "replace", "location", "reload", "err", "console", "error", "message", "action", "SERVER_ERROR", "DEV_PAGES_MANIFEST_UPDATE", "SYNC", "BUILT", "hash", "RELOAD_PAGE", "warn", "stack", "path"], "mappings": "AAAA,4BAA4B,GAC5B,SAASA,cAAc,QAAQ,SAAQ;AACvC,OAAOC,yBAAyB,6BAA4B;AAC5D,SAASC,kBAAkB,EAAEC,UAAU,QAAQ,4BAA2B;AAC1E,SAASC,2BAA2B,QAAQ,sCAAqC;AAMjF,MAAMC,OAAOC,KAAKC,KAAK,CACrB,AAACC,SAASC,cAAc,CAAC,iBAAyBC,WAAW;AAE/DC,OAAOC,aAAa,GAAGP;AAEvB,IAAI,EAAEQ,WAAW,EAAEC,IAAI,EAAE,GAAGT;AAC5BQ,cAAcA,eAAe;AAC7B,IAAIE,iBAAgC;AACpC,4BAA4B,GAC5B,IAAIC,UAAUC;AACd,MAAMC,gBACJL,cAAeA,CAAAA,YAAYM,QAAQ,CAAC,OAAO,KAAK,GAAE,IAAK;AAEzD,mDAAmD;AACnD,SAASC;IACP,2DAA2D;IAC3D,8CAA8C;IAC9C,4BAA4B,GAC5B,OAAOL,mBAAmBE;AAC5B;AAEA,6CAA6C;AAC7C,SAASI;IACP,yIAAyI;IACzI,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AAEA,uDAAuD;AACvD,wCAAwC;AACxC,eAAeC;IACb,IAAI,CAACL,uBAAuB,CAACC,mBAAmB;QAC9C;IACF;IACA,IAAI;QACF,MAAMK,MAAM,MAAMC,MAChB,OAAOC,2BAA2B,cAE9B,AAAC,KAAEV,gBAAgBF,UAAQ,MAAGY,yBAAuB,qBACrD,AAAC,KAAEV,gBAAgBF,UAAQ;QAEjC,MAAMa,WAAW,MAAMH,IAAII,IAAI;QAC/B,MAAMC,UAAUjB,SAAS,MAAM,UAAUA;QACzC,kCAAkC;QAClC,MAAMkB,cAAc,AAClBC,CAAAA,MAAMC,OAAO,CAACL,SAASM,CAAC,IAAIN,SAASM,CAAC,GAAGC,OAAOC,IAAI,CAACR,SAASM,CAAC,CAAA,EAC/DG,IAAI,CAAC,CAACC;YACN,OACEA,IAAIC,OAAO,CACT,AAAC,UAAOT,CAAAA,QAAQU,UAAU,CAAC,OAAOV,UAAU,AAAC,MAAGA,OAAQ,OACpD,CAAC,KACPQ,IAAIC,OAAO,CACT,CAAA,AAAC,UAAOT,CAAAA,QAAQU,UAAU,CAAC,OAAOV,UAAU,AAAC,MAAGA,OAAQ,CAAE,EAAEW,OAAO,CACjE,OACA,WAEE,CAAC;QAEX;QAEA,IAAIV,aAAa;YACfrB,OAAOgC,QAAQ,CAACC,MAAM;QACxB,OAAO;YACL5B,UAAUD;QACZ;IACF,EAAE,OAAO8B,KAAK;QACZC,QAAQC,KAAK,CAAC,sCAAsCF;QACpDlC,OAAOgC,QAAQ,CAACC,MAAM;IACxB;AACF;AAEA1C,mBAAmB,CAAC8C;IAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;QAC1B;IACF;IAEA,IAAI;QACF,2CAA2C;QAC3C,IACEA,QAAQC,MAAM,KAAK7C,4BAA4B8C,YAAY,IAC3DF,QAAQC,MAAM,KAAK7C,4BAA4B+C,yBAAyB,EACxE;YACA;QACF;QACA,IACEH,QAAQC,MAAM,KAAK7C,4BAA4BgD,IAAI,IACnDJ,QAAQC,MAAM,KAAK7C,4BAA4BiD,KAAK,EACpD;YACA,IAAI,CAACL,QAAQM,IAAI,EAAE;gBACjB;YACF;YACAvC,iBAAiBiC,QAAQM,IAAI;YAC7B7B;QACF,OAAO,IAAIuB,QAAQC,MAAM,KAAK7C,4BAA4BmD,WAAW,EAAE;YACrE5C,OAAOgC,QAAQ,CAACC,MAAM;QACxB;IACF,EAAE,OAAOC,KAAU;YAE+BA;QADhDC,QAAQU,IAAI,CACV,4BAA4BR,UAAU,OAAQH,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKY,KAAK,YAAVZ,aAAc,EAAC;IAEjE;AACF;AAEA1C,WAAW;IACTU;IACA6C,MAAM;AACR;AACA1D;AAEAC,oBAAoBI,KAAKS,IAAI"}