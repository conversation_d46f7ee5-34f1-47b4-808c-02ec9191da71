/**
 * @license React
 * react-server-dom-webpack-client.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var n=require("util"),p=require("react-dom"),q=require("react"),t={stream:!0};function u(a,b){if(a){var c=a[b.id];if(a=c[b.name])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b.id+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b.name}return{id:a.id,chunks:a.chunks,name:c,async:!!b.async}}return b}var v=new Map;
function w(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function x(){}
function B(a){for(var b=a.chunks,c=[],d=0;d<b.length;d++){var e=b[d],f=v.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);c.push(f);var g=v.set.bind(v,e,null);f.then(g,x);v.set(e,f)}else null!==f&&c.push(f)}return a.async?0===c.length?w(a.id):Promise.all(c).then(function(){return w(a.id)}):0<c.length?Promise.all(c):null}
var C=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,D=Symbol.for("react.element"),E=Symbol.for("react.lazy"),aa=Symbol.for("react.default_value"),ba=Symbol.for("react.postpone"),F=Symbol.iterator;function ca(a){if(null===a||"object"!==typeof a)return null;a=F&&a[F]||a["@@iterator"];return"function"===typeof a?a:null}var da=Array.isArray,G=new WeakMap;
function ea(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function fa(a,b,c,d){function e(l,h){if(null===h)return null;if("object"===typeof h){if("function"===typeof h.then){null===k&&(k=new FormData);g++;var y=f++;h.then(function(r){r=JSON.stringify(r,e);var z=k;z.append(b+y,r);g--;0===g&&c(z)},function(r){d(r)});return"$@"+y.toString(16)}if(h instanceof FormData){null===k&&(k=new FormData);var m=k;l=f++;var A=b+l+"_";h.forEach(function(r,z){m.append(A+z,r)});return"$K"+l.toString(16)}return h instanceof Map?(h=JSON.stringify(Array.from(h),e),null===k&&
(k=new FormData),l=f++,k.append(b+l,h),"$Q"+l.toString(16)):h instanceof Set?(h=JSON.stringify(Array.from(h),e),null===k&&(k=new FormData),l=f++,k.append(b+l,h),"$W"+l.toString(16)):!da(h)&&ca(h)?Array.from(h):h}if("string"===typeof h){if("Z"===h[h.length-1]&&this[l]instanceof Date)return"$D"+h;h="$"===h[0]?"$"+h:h;return h}if("boolean"===typeof h)return h;if("number"===typeof h)return ea(h);if("undefined"===typeof h)return"$undefined";if("function"===typeof h){h=G.get(h);if(void 0!==h)return h=JSON.stringify(h,
e),null===k&&(k=new FormData),l=f++,k.set(b+l,h),"$F"+l.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof h){l=h.description;if(Symbol.for(l)!==h)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+(h.description+") cannot be found among global symbols."));return"$S"+l}if("bigint"===typeof h)return"$n"+
h.toString(10);throw Error("Type "+typeof h+" is not supported as an argument to a Server Function.");}var f=1,g=0,k=null;a=JSON.stringify(a,e);null===k?c(a):(k.set(b+"0",a),0===g&&c(k))}var H=new WeakMap;function ha(a){var b,c,d=new Promise(function(e,f){b=e;c=f});fa(a,"",function(e){if("string"===typeof e){var f=new FormData;f.append("0",e);e=f}d.status="fulfilled";d.value=e;b(e)},function(e){d.status="rejected";d.reason=e;c(e)});return d}
function ia(a){var b=G.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=H.get(b);c||(c=ha(b),H.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(e,f){d.append("$ACTION_"+a+":"+f,e)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function ja(a,b){var c=G.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(e){d.status="fulfilled";d.value=e},function(e){d.status="rejected";d.reason=e})),d;}}
function I(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:ia},$$IS_SIGNATURE_EQUAL:{value:ja},bind:{value:ka}});G.set(a,b)}var la=Function.prototype.bind,ma=Array.prototype.slice;function ka(){var a=la.apply(this,arguments),b=G.get(this);if(b){var c=ma.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(e){return e.concat(c)}):Promise.resolve(c);I(a,{id:b.id,bound:d})}return a}
function na(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}I(c,{id:a,bound:null});return c}var J=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function K(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}K.prototype=Object.create(Promise.prototype);
K.prototype.then=function(a,b){switch(this.status){case "resolved_model":L(this);break;case "resolved_module":M(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function oa(a){switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function N(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function O(a,b,c){switch(a.status){case "fulfilled":N(b,a.value);break;case "pending":case "blocked":a.value=b;a.reason=c;break;case "rejected":c&&N(c,a.reason)}}
function P(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&N(c,b)}}function Q(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(M(a),O(a,c,d))}}var R=null,S=null;
function L(a){var b=R,c=S;R=a;S=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==S&&0<S.deps?(S.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(e){a.status="rejected",a.reason=e}finally{R=b,S=c}}
function M(a){try{var b=a.value,c=globalThis.__next_require__(b.id);if(b.async&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b.name?c:""===b.name?c.__esModule?c.default:c:c[b.name];a.status="fulfilled";a.value=d}catch(e){a.status="rejected",a.reason=e}}function T(a,b){a._chunks.forEach(function(c){"pending"===c.status&&P(c,b)})}function U(a,b){var c=a._chunks,d=c.get(b);d||(d=new K("pending",null,null,a),c.set(b,d));return d}
function pa(a,b,c){if(S){var d=S;d.deps++}else d=S={deps:1,value:null};return function(e){b[c]=e;d.deps--;0===d.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=d.value,null!==e&&N(e,d.value))}}function qa(a){return function(b){return P(a,b)}}
function ra(a,b){function c(){var e=Array.prototype.slice.call(arguments),f=b.bound;return f?"fulfilled"===f.status?d(b.id,f.value.concat(e)):Promise.resolve(f).then(function(g){return d(b.id,g.concat(e))}):d(b.id,e)}var d=a._callServer;I(c,b);return c}function V(a,b){a=U(a,b);switch(a.status){case "resolved_model":L(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function sa(a,b,c,d){if("$"===d[0]){if("$"===d)return D;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=U(a,b),{$$typeof:E,_payload:a,_init:oa};case "@":return b=parseInt(d.slice(2),16),U(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),J[a]||(J[a]=q.createServerContext(a,aa)),J[a].Provider;case "F":return b=parseInt(d.slice(2),16),b=V(a,b),ra(a,b);case "Q":return b=parseInt(d.slice(2),16),a=V(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),
16),a=V(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=U(a,d);switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=R,a.then(pa(d,b,c),qa(d)),null;default:throw a.reason;}}}return d}
function ta(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function ua(a,b){var c=new Map;a={_bundlerConfig:a,_callServer:void 0!==b?b:ta,_chunks:c,_stringDecoder:new n.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=va(a);return a}function W(a,b,c){a._chunks.set(b,new K("fulfilled",c,null,a))}
function wa(a,b,c){var d=a._chunks,e=d.get(b);c=JSON.parse(c,a._fromJSON);var f=u(a._bundlerConfig,c);if(c=B(f)){if(e){var g=e;g.status="blocked"}else g=new K("blocked",null,null,a),d.set(b,g);c.then(function(){return Q(g,f)},function(k){return P(g,k)})}else e?Q(e,f):d.set(b,new K("resolved_module",f,null,a))}function X(a,b){for(var c=a.length,d=b.length,e=0;e<c;e++)d+=a[e].byteLength;d=new Uint8Array(d);for(var f=e=0;f<c;f++){var g=a[f];d.set(g,e);e+=g.byteLength}d.set(b,e);return d}
function Y(a,b,c,d,e,f){c=0===c.length&&0===d.byteOffset%f?d:X(c,d);e=new e(c.buffer,c.byteOffset,c.byteLength/f);W(a,b,e)}
function xa(a,b,c,d,e){switch(c){case 65:W(a,b,X(d,e).buffer);return;case 67:Y(a,b,d,e,Int8Array,1);return;case 99:W(a,b,0===d.length?e:X(d,e));return;case 85:Y(a,b,d,e,Uint8ClampedArray,1);return;case 83:Y(a,b,d,e,Int16Array,2);return;case 115:Y(a,b,d,e,Uint16Array,2);return;case 76:Y(a,b,d,e,Int32Array,4);return;case 108:Y(a,b,d,e,Uint32Array,4);return;case 70:Y(a,b,d,e,Float32Array,4);return;case 68:Y(a,b,d,e,Float64Array,8);return;case 78:Y(a,b,d,e,BigInt64Array,8);return;case 109:Y(a,b,d,e,BigUint64Array,
8);return;case 86:Y(a,b,d,e,DataView,1);return}for(var f=a._stringDecoder,g="",k=0;k<d.length;k++)g+=f.decode(d[k],t);g+=f.decode(e);switch(c){case 73:wa(a,b,g);break;case 72:b=g[0];g=g.slice(1);a=JSON.parse(g,a._fromJSON);if(g=C.current)switch(b){case "D":g.prefetchDNS(a);break;case "C":"string"===typeof a?g.preconnect(a):g.preconnect(a[0],a[1]);break;case "L":b=a[0];c=a[1];3===a.length?g.preload(b,c,a[2]):g.preload(b,c);break;case "m":"string"===typeof a?g.preloadModule(a):g.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?g.preinitStyle(a):g.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?g.preinitScript(a):g.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?g.preinitModuleScript(a):g.preinitModuleScript(a[0],a[1])}break;case 69:c=JSON.parse(g).digest;g=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
g.stack="Error: "+g.message;g.digest=c;c=a._chunks;(d=c.get(b))?P(d,g):c.set(b,new K("rejected",null,g,a));break;case 84:a._chunks.set(b,new K("fulfilled",g,null,a));break;case 80:g=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");g.$$typeof=ba;g.stack="Error: "+g.message;c=a._chunks;(d=c.get(b))?P(d,g):c.set(b,new K("rejected",null,g,a));break;default:d=a._chunks,(c=d.get(b))?"pending"===c.status&&(a=c.value,b=c.reason,c.status=
"resolved_model",c.value=g,null!==a&&(L(c),O(c,a,b))):d.set(b,new K("resolved_model",g,null,a))}}function va(a){return function(b,c){return"string"===typeof c?sa(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===D?{$$typeof:D,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,b){var c=ua(b,Z);a.on("data",function(d){for(var e=0,f=c._rowState,g=c._rowID,k=c._rowTag,l=c._rowLength,h=c._buffer,y=d.length;e<y;){var m=-1;switch(f){case 0:m=d[e++];58===m?f=1:g=g<<4|(96<m?m-87:m-48);continue;case 1:f=d[e];84===f||65===f||67===f||99===f||85===f||83===f||115===f||76===f||108===f||70===f||68===f||78===f||109===f||86===f?(k=f,f=2,e++):64<f&&91>f?(k=f,f=3,e++):(k=0,f=3);continue;case 2:m=d[e++];44===m?f=4:l=l<<4|(96<m?m-87:m-48);continue;case 3:m=
d.indexOf(10,e);break;case 4:m=e+l,m>d.length&&(m=-1)}var A=d.byteOffset+e;if(-1<m)l=new Uint8Array(d.buffer,A,m-e),xa(c,g,k,h,l),e=m,3===f&&e++,l=g=k=f=0,h.length=0;else{d=new Uint8Array(d.buffer,A,d.byteLength-e);h.push(d);l-=d.byteLength;break}}c._rowState=f;c._rowID=g;c._rowTag=k;c._rowLength=l});a.on("error",function(d){T(c,d)});a.on("end",function(){T(c,Error("Connection closed."))});return U(c,0)};exports.createServerReference=function(a){return na(a,Z)};
