(()=>{var e={151:function(e,t){(function(e,n){true?n(t):0})(this,(function(e){"use strict";const t=",".charCodeAt(0);const n=";".charCodeAt(0);const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";const r=new Uint8Array(64);const o=new Uint8Array(128);for(let e=0;e<i.length;e++){const t=i.charCodeAt(e);o[t]=e;r[e]=t}const a=typeof TextDecoder!=="undefined"?new TextDecoder:typeof Buffer!=="undefined"?{decode(e){const t=Buffer.from(e.buffer,e.byteOffset,e.byteLength);return t.toString()}}:{decode(e){let t="";for(let n=0;n<e.length;n++){t+=String.fromCharCode(e[n])}return t}};function decode(e){const i=new Int32Array(5);const r=[];let o=[];let a=true;let s=0;for(let u=0;u<e.length;){const l=e.charCodeAt(u);if(l===t){u++}else if(l===n){i[0]=s=0;if(!a)sort(o);a=true;r.push(o);o=[];u++}else{u=decodeInteger(e,u,i,0);const t=i[0];if(t<s)a=false;s=t;if(!hasMoreSegments(e,u)){o.push([t]);continue}u=decodeInteger(e,u,i,1);u=decodeInteger(e,u,i,2);u=decodeInteger(e,u,i,3);if(!hasMoreSegments(e,u)){o.push([t,i[1],i[2],i[3]]);continue}u=decodeInteger(e,u,i,4);o.push([t,i[1],i[2],i[3],i[4]])}}if(!a)sort(o);r.push(o);return r}function decodeInteger(e,t,n,i){let r=0;let a=0;let s=0;do{const n=e.charCodeAt(t++);s=o[n];r|=(s&31)<<a;a+=5}while(s&32);const u=r&1;r>>>=1;if(u){r=-2147483648|-r}n[i]+=r;return t}function hasMoreSegments(e,i){if(i>=e.length)return false;const r=e.charCodeAt(i);if(r===t||r===n)return false;return true}function sort(e){e.sort(sortComparator$1)}function sortComparator$1(e,t){return e[0]-t[0]}function encode(e){const i=new Int32Array(5);let r=new Uint8Array(1024);let o=0;for(let a=0;a<e.length;a++){const s=e[a];if(a>0){r=reserve(r,o,1);r[o++]=n}if(s.length===0)continue;i[0]=0;for(let e=0;e<s.length;e++){const n=s[e];r=reserve(r,o,36);if(e>0)r[o++]=t;o=encodeInteger(r,o,i,n,0);if(n.length===1)continue;o=encodeInteger(r,o,i,n,1);o=encodeInteger(r,o,i,n,2);o=encodeInteger(r,o,i,n,3);if(n.length===4)continue;o=encodeInteger(r,o,i,n,4)}}return a.decode(r.subarray(0,o))}function reserve(e,t,n){if(e.length>t+n)return e;const i=new Uint8Array(e.length*2);i.set(e);return i}function encodeInteger(e,t,n,i,o){const a=i[o];let s=a-n[o];n[o]=a;s=s<0?-s<<1|1:s<<1;do{let n=s&31;s>>>=5;if(s>0)n|=32;e[t++]=r[n]}while(s>0);return t}const s=/^[\w+.-]+:\/\//;const u=/^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?/;const l=/^file:(?:\/\/((?![a-z]:)[^/]*)?)?(\/?.*)/i;function isAbsoluteUrl(e){return s.test(e)}function isSchemeRelativeUrl(e){return e.startsWith("//")}function isAbsolutePath(e){return e.startsWith("/")}function isFileUrl(e){return e.startsWith("file:")}function parseAbsoluteUrl(e){const t=u.exec(e);return makeUrl(t[1],t[2]||"",t[3],t[4]||"",t[5]||"/")}function parseFileUrl(e){const t=l.exec(e);const n=t[2];return makeUrl("file:","",t[1]||"","",isAbsolutePath(n)?n:"/"+n)}function makeUrl(e,t,n,i,r){return{scheme:e,user:t,host:n,port:i,path:r,relativePath:false}}function parseUrl(e){if(isSchemeRelativeUrl(e)){const t=parseAbsoluteUrl("http:"+e);t.scheme="";return t}if(isAbsolutePath(e)){const t=parseAbsoluteUrl("http://foo.com"+e);t.scheme="";t.host="";return t}if(isFileUrl(e))return parseFileUrl(e);if(isAbsoluteUrl(e))return parseAbsoluteUrl(e);const t=parseAbsoluteUrl("http://foo.com/"+e);t.scheme="";t.host="";t.relativePath=true;return t}function stripPathFilename(e){if(e.endsWith("/.."))return e;const t=e.lastIndexOf("/");return e.slice(0,t+1)}function mergePaths(e,t){if(!e.relativePath)return;normalizePath(t);if(e.path==="/"){e.path=t.path}else{e.path=stripPathFilename(t.path)+e.path}e.relativePath=t.relativePath}function normalizePath(e){const{relativePath:t}=e;const n=e.path.split("/");let i=1;let r=0;let o=false;for(let e=1;e<n.length;e++){const a=n[e];if(!a){o=true;continue}o=false;if(a===".")continue;if(a===".."){if(r){o=true;r--;i--}else if(t){n[i++]=a}continue}n[i++]=a;r++}let a="";for(let e=1;e<i;e++){a+="/"+n[e]}if(!a||o&&!a.endsWith("/..")){a+="/"}e.path=a}function resolve$1(e,t){if(!e&&!t)return"";const n=parseUrl(e);if(t&&!n.scheme){const e=parseUrl(t);n.scheme=e.scheme;if(!n.host){n.user=e.user;n.host=e.host;n.port=e.port}mergePaths(n,e)}normalizePath(n);if(n.relativePath){const i=n.path.slice(1);if(!i)return".";const r=(t||e).startsWith(".");return!r||i.startsWith(".")?i:"./"+i}if(!n.scheme&&!n.host)return n.path;return`${n.scheme}//${n.user}${n.host}${n.port}${n.path}`}function resolve(e,t){if(t&&!t.endsWith("/"))t+="/";return resolve$1(e,t)}function stripFilename(e){if(!e)return"";const t=e.lastIndexOf("/");return e.slice(0,t+1)}const c=0;const f=1;const _=2;const p=3;const d=4;function maybeSort(e,t){const n=nextUnsortedSegmentLine(e,0);if(n===e.length)return e;if(!t)e=e.slice();for(let i=n;i<e.length;i=nextUnsortedSegmentLine(e,i+1)){e[i]=sortSegments(e[i],t)}return e}function nextUnsortedSegmentLine(e,t){for(let n=t;n<e.length;n++){if(!isSorted(e[n]))return n}return e.length}function isSorted(e){for(let t=1;t<e.length;t++){if(e[t][c]<e[t-1][c]){return false}}return true}function sortSegments(e,t){if(!t)e=e.slice();return e.sort(sortComparator)}function sortComparator(e,t){return e[c]-t[c]}let m=false;function binarySearch(e,t,n,i){while(n<=i){const r=n+(i-n>>1);const o=e[r][c]-t;if(o===0){m=true;return r}if(o<0){n=r+1}else{i=r-1}}m=false;return n-1}function upperBound(e,t,n){for(let i=n+1;i<e.length;i++,n++){if(e[i][c]!==t)break}return n}function lowerBound(e,t,n){for(let i=n-1;i>=0;i--,n--){if(e[i][c]!==t)break}return n}function memoizedState(){return{lastKey:-1,lastNeedle:-1,lastIndex:-1}}function memoizedBinarySearch(e,t,n,i){const{lastKey:r,lastNeedle:o,lastIndex:a}=n;let s=0;let u=e.length-1;if(i===r){if(t===o){m=a!==-1&&e[a][c]===t;return a}if(t>=o){s=a===-1?0:a}else{u=a}}n.lastKey=i;n.lastNeedle=t;return n.lastIndex=binarySearch(e,t,s,u)}const AnyMap=function(e,t){const n=typeof e==="string"?JSON.parse(e):e;if(!("sections"in n))return new TraceMap(n,t);const i=[];const r=[];const o=[];const a=[];const{sections:s}=n;let u=0;for(;u<s.length-1;u++){const e=s[u+1].offset;addSection(s[u],t,i,r,o,a,e.line,e.column)}if(s.length>0){addSection(s[u],t,i,r,o,a,Infinity,Infinity)}const l={version:3,file:n.file,names:a,sources:r,sourcesContent:o,mappings:i};return S(l)};function addSection(e,t,n,i,r,o,a,s){const u=AnyMap(e.map,t);const{line:l,column:m}=e.offset;const h=i.length;const E=o.length;const g=b(u);const{resolvedSources:v}=u;append(i,v);append(r,u.sourcesContent||fillSourcesContent(v.length));append(o,u.names);for(let e=n.length;e<=l;e++)n.push([]);const D=a-l;const y=Math.min(g.length,D+1);for(let e=0;e<y;e++){const t=g[e];const i=e===0?n[l]:n[l+e]=[];const r=e===0?m:0;for(let n=0;n<t.length;n++){const o=t[n];const a=r+o[c];if(e===D&&a>=s)break;if(o.length===1){i.push([a]);continue}const u=h+o[f];const l=o[_];const m=o[p];if(o.length===4){i.push([a,u,l,m]);continue}i.push([a,u,l,m,E+o[d]])}}}function append(e,t){for(let n=0;n<t.length;n++)e.push(t[n])}function fillSourcesContent(e){const t=[];for(let n=0;n<e;n++)t[n]=null;return t}const h=Object.freeze({source:null,line:null,column:null,name:null});Object.freeze({line:null,column:null});const E="`line` must be greater than 0 (lines start at line 1)";const g="`column` must be greater than or equal to 0 (columns start at column 0)";const v=-1;const D=1;let b;let y;let S;class TraceMap{constructor(e,t){this._decodedMemo=memoizedState();this._bySources=undefined;this._bySourceMemos=undefined;const n=typeof e==="string";if(!n&&e.constructor===TraceMap)return e;const i=n?JSON.parse(e):e;const{version:r,file:o,names:a,sourceRoot:s,sources:u,sourcesContent:l}=i;this.version=r;this.file=o;this.names=a;this.sourceRoot=s;this.sources=u;this.sourcesContent=l;if(s||t){const e=resolve(s||"",stripFilename(t));this.resolvedSources=u.map((t=>resolve(t||"",e)))}else{this.resolvedSources=u.map((e=>e||""))}const{mappings:c}=i;if(typeof c==="string"){this._encoded=c;this._decoded=undefined}else{this._encoded=undefined;this._decoded=maybeSort(c,n)}}}(()=>{b=e=>e._decoded||(e._decoded=decode(e._encoded));y=(e,{line:t,column:n,bias:i})=>{t--;if(t<0)throw new Error(E);if(n<0)throw new Error(g);const r=b(e);if(t>=r.length)return h;const o=traceSegmentInternal(r[t],e._decodedMemo,t,n,i||D);if(o==null)return h;if(o.length==1)return h;const{names:a,resolvedSources:s}=e;return{source:s[o[f]],line:o[_]+1,column:o[p],name:o.length===5?a[o[d]]:null}};S=(e,t)=>{const n=Object.assign({},e);n.mappings=[];const i=new TraceMap(n,t);i._decoded=e.mappings;return i}})();function traceSegmentInternal(e,t,n,i,r){let o=memoizedBinarySearch(e,i,t,n);if(m){o=(r===v?upperBound:lowerBound)(e,i,o)}else if(r===v)o++;if(o===-1||o===e.length)return null;return e[o]}let A;let T;class SetArray{constructor(){this._indexes={__proto__:null};this.array=[]}}(()=>{A=(e,t)=>e._indexes[t];T=(e,t)=>{const n=A(e,t);if(n!==undefined)return n;const{array:i,_indexes:r}=e;return r[t]=i.push(t)-1}})();const k=0;const C=1;const R=2;const F=3;const O=4;const x=-1;let M;let w;let N;let I;let P;class GenMapping{constructor({file:e,sourceRoot:t}={}){this._names=new SetArray;this._sources=new SetArray;this._sourcesContent=[];this._mappings=[];this.file=e;this.sourceRoot=t}}(()=>{M=(e,t)=>addMappingInternal(true,e,t);w=(e,t,n)=>{const{_sources:i,_sourcesContent:r}=e;r[T(i,t)]=n};N=e=>{const{file:t,sourceRoot:n,_mappings:i,_sources:r,_sourcesContent:o,_names:a}=e;removeEmptyFinalLines(i);return{version:3,file:t||undefined,names:a.array,sourceRoot:n||undefined,sources:r.array,sourcesContent:o,mappings:i}};I=e=>{const t=N(e);return Object.assign(Object.assign({},t),{mappings:encode(t.mappings)})};P=(e,t,n,i,r,o,a,s)=>{const{_mappings:u,_sources:l,_sourcesContent:c,_names:f}=t;const _=getLine(u,n);const p=getColumnIndex(_,i);if(!r){if(e&&skipSourceless(_,p))return;return insert(_,p,[i])}const d=T(l,r);const m=s?T(f,s):x;if(d===c.length)c[d]=null;if(e&&skipSource(_,p,d,o,a,m)){return}return insert(_,p,s?[i,d,o,a,m]:[i,d,o,a])}})();function getLine(e,t){for(let n=e.length;n<=t;n++){e[n]=[]}return e[t]}function getColumnIndex(e,t){let n=e.length;for(let i=n-1;i>=0;n=i--){const n=e[i];if(t>=n[k])break}return n}function insert(e,t,n){for(let n=e.length;n>t;n--){e[n]=e[n-1]}e[t]=n}function removeEmptyFinalLines(e){const{length:t}=e;let n=t;for(let t=n-1;t>=0;n=t,t--){if(e[t].length>0)break}if(n<t)e.length=n}function skipSourceless(e,t){if(t===0)return true;const n=e[t-1];return n.length===1}function skipSource(e,t,n,i,r,o){if(t===0)return false;const a=e[t-1];if(a.length===1)return false;return n===a[C]&&i===a[R]&&r===a[F]&&o===(a.length===5?a[O]:x)}function addMappingInternal(e,t,n){const{generated:i,source:r,original:o,name:a}=n;if(!r){return P(e,t,i.line-1,i.column,null,null,null,null)}const s=r;return P(e,t,i.line-1,i.column,s,o.line-1,o.column,a)}class SourceMapConsumer{constructor(e,t){const n=this._map=new AnyMap(e,t);this.file=n.file;this.names=n.names;this.sourceRoot=n.sourceRoot;this.sources=n.resolvedSources;this.sourcesContent=n.sourcesContent}originalPositionFor(e){return y(this._map,e)}destroy(){}}class SourceMapGenerator{constructor(e){this._map=new GenMapping(e)}addMapping(e){M(this._map,e)}setSourceContent(e,t){w(this._map,e,t)}toJSON(){return I(this._map)}toDecodedMap(){return N(this._map)}}e.SourceMapConsumer=SourceMapConsumer;e.SourceMapGenerator=SourceMapGenerator;Object.defineProperty(e,"__esModule",{value:true})}))},988:e=>{"use strict";e.exports=require("next/dist/compiled/acorn")},221:function(e,t,n){(function(e,i){true?i(t,n(151)):0})(this,(function(e,t){"use strict";function characters(e){return e.split("")}function member(e,t){return t.includes(e)}class DefaultsError extends Error{constructor(e,t){super();this.name="DefaultsError";this.message=e;this.defs=t}}function defaults(e,t,n){if(e===true){e={}}else if(e!=null&&typeof e==="object"){e={...e}}const i=e||{};if(n)for(const e in i)if(HOP(i,e)&&!HOP(t,e)){throw new DefaultsError("`"+e+"` is not a supported option",t)}for(const n in t)if(HOP(t,n)){if(!e||!HOP(e,n)){i[n]=t[n]}else if(n==="ecma"){let t=e[n]|0;if(t>5&&t<2015)t+=2009;i[n]=t}else{i[n]=e&&HOP(e,n)?e[n]:t[n]}}return i}function noop(){}function return_false(){return false}function return_true(){return true}function return_this(){return this}function return_null(){return null}var i=function(){function MAP(t,n,i){var r=[],o=[],a;function doit(){var s=n(t[a],a);var u=s instanceof Last;if(u)s=s.v;if(s instanceof AtTop){s=s.v;if(s instanceof Splice){o.push.apply(o,i?s.v.slice().reverse():s.v)}else{o.push(s)}}else if(s!==e){if(s instanceof Splice){r.push.apply(r,i?s.v.slice().reverse():s.v)}else{r.push(s)}}return u}if(Array.isArray(t)){if(i){for(a=t.length;--a>=0;)if(doit())break;r.reverse();o.reverse()}else{for(a=0;a<t.length;++a)if(doit())break}}else{for(a in t)if(HOP(t,a))if(doit())break}return o.concat(r)}MAP.at_top=function(e){return new AtTop(e)};MAP.splice=function(e){return new Splice(e)};MAP.last=function(e){return new Last(e)};var e=MAP.skip={};function AtTop(e){this.v=e}function Splice(e){this.v=e}function Last(e){this.v=e}return MAP}();function make_node(e,t,n){if(!n)n={};if(t){if(!n.start)n.start=t.start;if(!n.end)n.end=t.end}return new e(n)}function push_uniq(e,t){if(!e.includes(t))e.push(t)}function string_template(e,t){return e.replace(/{(.+?)}/g,(function(e,n){return t&&t[n]}))}function remove(e,t){for(var n=e.length;--n>=0;){if(e[n]===t)e.splice(n,1)}}function mergeSort(e,t){if(e.length<2)return e.slice();function merge(e,n){var i=[],r=0,o=0,a=0;while(r<e.length&&o<n.length){t(e[r],n[o])<=0?i[a++]=e[r++]:i[a++]=n[o++]}if(r<e.length)i.push.apply(i,e.slice(r));if(o<n.length)i.push.apply(i,n.slice(o));return i}function _ms(e){if(e.length<=1)return e;var t=Math.floor(e.length/2),n=e.slice(0,t),i=e.slice(t);n=_ms(n);i=_ms(i);return merge(n,i)}return _ms(e)}function makePredicate(e){if(!Array.isArray(e))e=e.split(" ");return new Set(e.sort())}function map_add(e,t,n){if(e.has(t)){e.get(t).push(n)}else{e.set(t,[n])}}function map_from_object(e){var t=new Map;for(var n in e){if(HOP(e,n)&&n.charAt(0)==="$"){t.set(n.substr(1),e[n])}}return t}function map_to_object(e){var t=Object.create(null);e.forEach((function(e,n){t["$"+n]=e}));return t}function HOP(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function keep_name(e,t){return e===true||e instanceof RegExp&&e.test(t)}var r={"\0":"0","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"};function regexp_source_fix(e){return e.replace(/[\0\n\r\u2028\u2029]/g,(function(t,n){var i=e[n-1]=="\\"&&(e[n-2]!="\\"||/(?:^|[^\\])(?:\\{2})*$/.test(e.slice(0,n-1)));return(i?"":"\\")+r[t]}))}const o="gimuy";function sort_regexp_flags(e){const t=new Set(e.split(""));let n="";for(const e of o){if(t.has(e)){n+=e;t.delete(e)}}if(t.size){t.forEach((e=>{n+=e}))}return n}function has_annotation(e,t){return e._annotations&t}function set_annotation(e,t){e._annotations|=t}var a="";var s=new Map;var u="break case catch class const continue debugger default delete do else export extends finally for function if in instanceof let new return switch throw try typeof var void while with";var l="false null true";var c="enum import super this "+l+" "+u;var f="implements interface package private protected public static "+c;var _="return new delete throw else case yield await";u=makePredicate(u);c=makePredicate(c);_=makePredicate(_);l=makePredicate(l);f=makePredicate(f);var p=makePredicate(characters("+-*&%=<>!?|~^"));var d=/[0-9a-f]/i;var m=/^0x[0-9a-f]+$/i;var h=/^0[0-7]+$/;var E=/^0o[0-7]+$/i;var g=/^0b[01]+$/i;var v=/^\d*\.?\d*(?:e[+-]?\d*(?:\d\.?|\.?\d)\d*)?$/i;var D=/^(0[xob])?[0-9a-f]+n$/i;var b=makePredicate(["in","instanceof","typeof","new","void","delete","++","--","+","-","!","~","&","|","^","*","**","/","%",">>","<<",">>>","<",">","<=",">=","==","===","!=","!==","?","=","+=","-=","||=","&&=","??=","/=","*=","**=","%=",">>=","<<=",">>>=","|=","^=","&=","&&","??","||"]);var y=makePredicate(characters("  \n\r\t\f\v​           \u2028\u2029  　\ufeff"));var S=makePredicate(characters("\n\r\u2028\u2029"));var A=makePredicate(characters(";]),:"));var T=makePredicate(characters("[{(,;:"));var k=makePredicate(characters("[]{}(),;:"));var C={ID_Start:/[$A-Z_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,ID_Continue:/(?:[$0-9A-Z_a-z\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFC-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C4\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF])+/};function get_full_char(e,t){if(is_surrogate_pair_head(e.charCodeAt(t))){if(is_surrogate_pair_tail(e.charCodeAt(t+1))){return e.charAt(t)+e.charAt(t+1)}}else if(is_surrogate_pair_tail(e.charCodeAt(t))){if(is_surrogate_pair_head(e.charCodeAt(t-1))){return e.charAt(t-1)+e.charAt(t)}}return e.charAt(t)}function get_full_char_code(e,t){if(is_surrogate_pair_head(e.charCodeAt(t))){return 65536+(e.charCodeAt(t)-55296<<10)+e.charCodeAt(t+1)-56320}return e.charCodeAt(t)}function get_full_char_length(e){var t=0;for(var n=0;n<e.length;n++){if(is_surrogate_pair_head(e.charCodeAt(n))&&is_surrogate_pair_tail(e.charCodeAt(n+1))){t++;n++}}return e.length-t}function from_char_code(e){if(e>65535){e-=65536;return String.fromCharCode((e>>10)+55296)+String.fromCharCode(e%1024+56320)}return String.fromCharCode(e)}function is_surrogate_pair_head(e){return e>=55296&&e<=56319}function is_surrogate_pair_tail(e){return e>=56320&&e<=57343}function is_digit(e){return e>=48&&e<=57}function is_identifier_start(e){return C.ID_Start.test(e)}function is_identifier_char(e){return C.ID_Continue.test(e)}const R=/^[a-z_$][a-z0-9_$]*$/i;function is_basic_identifier_string(e){return R.test(e)}function is_identifier_string(e,t){if(R.test(e)){return true}if(!t&&/[\ud800-\udfff]/.test(e)){return false}var n=C.ID_Start.exec(e);if(!n||n.index!==0){return false}e=e.slice(n[0].length);if(!e){return true}n=C.ID_Continue.exec(e);return!!n&&n[0].length===e.length}function parse_js_number(e,t=true){if(!t&&e.includes("e")){return NaN}if(m.test(e)){return parseInt(e.substr(2),16)}else if(h.test(e)){return parseInt(e.substr(1),8)}else if(E.test(e)){return parseInt(e.substr(2),8)}else if(g.test(e)){return parseInt(e.substr(2),2)}else if(v.test(e)){return parseFloat(e)}else{var n=parseFloat(e);if(n==e)return n}}class JS_Parse_Error extends Error{constructor(e,t,n,i,r){super();this.name="SyntaxError";this.message=e;this.filename=t;this.line=n;this.col=i;this.pos=r}}function js_error(e,t,n,i,r){throw new JS_Parse_Error(e,t,n,i,r)}function is_token(e,t,n){return e.type==t&&(n==null||e.value==n)}var F={};function tokenizer(e,t,n,i){var r={text:e,filename:t,pos:0,tokpos:0,line:1,tokline:0,col:0,tokcol:0,newline_before:false,regex_allowed:false,brace_counter:0,template_braces:[],comments_before:[],directives:{},directive_stack:[]};function peek(){return get_full_char(r.text,r.pos)}function is_option_chain_op(){const e=r.text.charCodeAt(r.pos+1)===46;if(!e)return false;const t=r.text.charCodeAt(r.pos+2);return t<48||t>57}function next(e,t){var n=get_full_char(r.text,r.pos++);if(e&&!n)throw F;if(S.has(n)){r.newline_before=r.newline_before||!t;++r.line;r.col=0;if(n=="\r"&&peek()=="\n"){++r.pos;n="\n"}}else{if(n.length>1){++r.pos;++r.col}++r.col}return n}function forward(e){while(e--)next()}function looking_at(e){return r.text.substr(r.pos,e.length)==e}function find_eol(){var e=r.text;for(var t=r.pos,n=r.text.length;t<n;++t){var i=e[t];if(S.has(i))return t}return-1}function find(e,t){var n=r.text.indexOf(e,r.pos);if(t&&n==-1)throw F;return n}function start_token(){r.tokline=r.line;r.tokcol=r.col;r.tokpos=r.pos}var o=false;var f=null;function token(e,n,i){r.regex_allowed=e=="operator"&&!x.has(n)||e=="keyword"&&_.has(n)||e=="punc"&&T.has(n)||e=="arrow";if(e=="punc"&&(n=="."||n=="?.")){o=true}else if(!i){o=false}const a=r.tokline;const s=r.tokcol;const u=r.tokpos;const l=r.newline_before;const c=t;let p=[];let d=[];if(!i){p=r.comments_before;d=r.comments_before=[]}r.newline_before=false;const m=new AST_Token(e,n,a,s,u,l,p,d,c);if(!i)f=m;return m}function skip_whitespace(){while(y.has(peek()))next()}function read_while(e){var t="",n,i=0;while((n=peek())&&e(n,i++))t+=next();return t}function parse_error(e){js_error(e,t,r.tokline,r.tokcol,r.tokpos)}function read_num(e){var t=false,n=false,i=false,r=e==".",o=false,s=false;var u=read_while((function(a,u){if(o)return false;var l=a.charCodeAt(0);switch(l){case 95:return s=true;case 98:case 66:return i=true;case 111:case 79:case 120:case 88:return i?false:i=true;case 101:case 69:return i?true:t?false:t=n=true;case 45:return n||u==0&&!e;case 43:return n;case n=false,46:return!r&&!i&&!t?r=true:false}if(a==="n"){o=true;return true}return d.test(a)}));if(e)u=e+u;a=u;if(h.test(u)&&next_token.has_directive("use strict")){parse_error("Legacy octal literals are not allowed in strict mode")}if(s){if(u.endsWith("_")){parse_error("Numeric separators are not allowed at the end of numeric literals")}else if(u.includes("__")){parse_error("Only one underscore is allowed as numeric separator")}u=u.replace(/_/g,"")}if(u.endsWith("n")){const e=u.slice(0,-1);const t=m.test(e);const n=parse_js_number(e,t);if(!r&&D.test(u)&&!isNaN(n))return token("big_int",e);parse_error("Invalid or unexpected token")}var l=parse_js_number(u);if(!isNaN(l)){return token("num",l)}else{parse_error("Invalid syntax: "+u)}}function is_octal(e){return e>="0"&&e<="7"}function read_escaped_char(e,t,n){var i=next(true,e);switch(i.charCodeAt(0)){case 110:return"\n";case 114:return"\r";case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 120:return String.fromCharCode(hex_bytes(2,t));case 117:if(peek()=="{"){next(true);if(peek()==="}")parse_error("Expecting hex-character between {}");while(peek()=="0")next(true);var o,a=find("}",true)-r.pos;if(a>6||(o=hex_bytes(a,t))>1114111){parse_error("Unicode reference out of bounds")}next(true);return from_char_code(o)}return String.fromCharCode(hex_bytes(4,t));case 10:return"";case 13:if(peek()=="\n"){next(true,e);return""}}if(is_octal(i)){if(n&&t){const e=i==="0"&&!is_octal(peek());if(!e){parse_error("Octal escape sequences are not allowed in template strings")}}return read_octal_escape_sequence(i,t)}return i}function read_octal_escape_sequence(e,t){var n=peek();if(n>="0"&&n<="7"){e+=next(true);if(e[0]<="3"&&(n=peek())>="0"&&n<="7")e+=next(true)}if(e==="0")return"\0";if(e.length>0&&next_token.has_directive("use strict")&&t)parse_error("Legacy octal escape sequences are not allowed in strict mode");return String.fromCharCode(parseInt(e,8))}function hex_bytes(e,t){var n=0;for(;e>0;--e){if(!t&&isNaN(parseInt(peek(),16))){return parseInt(n,16)||""}var i=next(true);if(isNaN(parseInt(i,16)))parse_error("Invalid hex-character pattern in string");n+=i}return parseInt(n,16)}var E=with_eof_error("Unterminated string constant",(function(){const e=r.pos;var t=next(),n=[];for(;;){var i=next(true,true);if(i=="\\")i=read_escaped_char(true,true);else if(i=="\r"||i=="\n")parse_error("Unterminated string constant");else if(i==t)break;n.push(i)}var o=token("string",n.join(""));a=r.text.slice(e,r.pos);o.quote=t;return o}));var g=with_eof_error("Unterminated template",(function(e){if(e){r.template_braces.push(r.brace_counter)}var t="",n="",i,o;next(true,true);while((i=next(true,true))!="`"){if(i=="\r"){if(peek()=="\n")++r.pos;i="\n"}else if(i=="$"&&peek()=="{"){next(true,true);r.brace_counter++;o=token(e?"template_head":"template_substitution",t);s.set(o,n);o.template_end=false;return o}n+=i;if(i=="\\"){var a=r.pos;var u=f&&(f.type==="name"||f.type==="punc"&&(f.value===")"||f.value==="]"));i=read_escaped_char(true,!u,true);n+=r.text.substr(a,r.pos-a)}t+=i}r.template_braces.pop();o=token(e?"template_head":"template_substitution",t);s.set(o,n);o.template_end=true;return o}));function skip_line_comment(e){var t=r.regex_allowed;var n=find_eol(),i;if(n==-1){i=r.text.substr(r.pos);r.pos=r.text.length}else{i=r.text.substring(r.pos,n);r.pos=n}r.col=r.tokcol+(r.pos-r.tokpos);r.comments_before.push(token(e,i,true));r.regex_allowed=t;return next_token}var v=with_eof_error("Unterminated multiline comment",(function(){var e=r.regex_allowed;var t=find("*/",true);var n=r.text.substring(r.pos,t).replace(/\r\n|\r|\u2028|\u2029/g,"\n");forward(get_full_char_length(n)+2);r.comments_before.push(token("comment2",n,true));r.newline_before=r.newline_before||n.includes("\n");r.regex_allowed=e;return next_token}));var A=with_eof_error("Unterminated identifier name",(function(){var e=[],t,n=false;var read_escaped_identifier_char=function(){n=true;next();if(peek()!=="u"){parse_error("Expecting UnicodeEscapeSequence -- uXXXX or u{XXXX}")}return read_escaped_char(false,true)};if((t=peek())==="\\"){t=read_escaped_identifier_char();if(!is_identifier_start(t)){parse_error("First identifier char is an invalid identifier char")}}else if(is_identifier_start(t)){next()}else{return""}e.push(t);while((t=peek())!=null){if((t=peek())==="\\"){t=read_escaped_identifier_char();if(!is_identifier_char(t)){parse_error("Invalid escaped identifier char")}}else{if(!is_identifier_char(t)){break}next()}e.push(t)}const i=e.join("");if(c.has(i)&&n){parse_error("Escaped characters are not allowed in keywords")}return i}));var C=with_eof_error("Unterminated regular expression",(function(e){var t=false,n,i=false;while(n=next(true))if(S.has(n)){parse_error("Unexpected line terminator")}else if(t){e+="\\"+n;t=false}else if(n=="["){i=true;e+=n}else if(n=="]"&&i){i=false;e+=n}else if(n=="/"&&!i){break}else if(n=="\\"){t=true}else{e+=n}const r=A();return token("regexp","/"+e+"/"+r)}));function read_operator(e){function grow(e){if(!peek())return e;var t=e+peek();if(b.has(t)){next();return grow(t)}else{return e}}return token("operator",grow(e||next()))}function handle_slash(){next();switch(peek()){case"/":next();return skip_line_comment("comment1");case"*":next();return v()}return r.regex_allowed?C(""):read_operator("/")}function handle_eq_sign(){next();if(peek()===">"){next();return token("arrow","=>")}else{return read_operator("=")}}function handle_dot(){next();if(is_digit(peek().charCodeAt(0))){return read_num(".")}if(peek()==="."){next();next();return token("expand","...")}return token("punc",".")}function read_word(){var e=A();if(o)return token("name",e);return l.has(e)?token("atom",e):!u.has(e)?token("name",e):b.has(e)?token("operator",e):token("keyword",e)}function read_private_word(){next();return token("privatename",A())}function with_eof_error(e,t){return function(n){try{return t(n)}catch(t){if(t===F)parse_error(e);else throw t}}}function next_token(e){if(e!=null)return C(e);if(i&&r.pos==0&&looking_at("#!")){start_token();forward(2);skip_line_comment("comment5")}for(;;){skip_whitespace();start_token();if(n){if(looking_at("\x3c!--")){forward(4);skip_line_comment("comment3");continue}if(looking_at("--\x3e")&&r.newline_before){forward(3);skip_line_comment("comment4");continue}}var t=peek();if(!t)return token("eof");var o=t.charCodeAt(0);switch(o){case 34:case 39:return E();case 46:return handle_dot();case 47:{var a=handle_slash();if(a===next_token)continue;return a}case 61:return handle_eq_sign();case 63:{if(!is_option_chain_op())break;next();next();return token("punc","?.")}case 96:return g(true);case 123:r.brace_counter++;break;case 125:r.brace_counter--;if(r.template_braces.length>0&&r.template_braces[r.template_braces.length-1]===r.brace_counter)return g(false);break}if(is_digit(o))return read_num();if(k.has(t))return token("punc",next());if(p.has(t))return read_operator();if(o==92||is_identifier_start(t))return read_word();if(o==35)return read_private_word();break}parse_error("Unexpected character '"+t+"'")}next_token.next=next;next_token.peek=peek;next_token.context=function(e){if(e)r=e;return r};next_token.add_directive=function(e){r.directive_stack[r.directive_stack.length-1].push(e);if(r.directives[e]===undefined){r.directives[e]=1}else{r.directives[e]++}};next_token.push_directives_stack=function(){r.directive_stack.push([])};next_token.pop_directives_stack=function(){var e=r.directive_stack[r.directive_stack.length-1];for(var t=0;t<e.length;t++){r.directives[e[t]]--}r.directive_stack.pop()};next_token.has_directive=function(e){return r.directives[e]>0};return next_token}var O=makePredicate(["typeof","void","delete","--","++","!","~","-","+"]);var x=makePredicate(["--","++"]);var M=makePredicate(["=","+=","-=","??=","&&=","||=","/=","*=","**=","%=",">>=","<<=",">>>=","|=","^=","&="]);var w=makePredicate(["??=","&&=","||="]);var N=function(e,t){for(var n=0;n<e.length;++n){var i=e[n];for(var r=0;r<i.length;++r){t[i[r]]=n+1}}return t}([["||"],["??"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]],{});var I=makePredicate(["atom","num","big_int","string","regexp","name"]);function parse(e,t){const n=new WeakMap;t=defaults(t,{bare_returns:false,ecma:null,expression:false,filename:null,html5_comments:true,module:false,shebang:true,strict:false,toplevel:null},true);var i={input:typeof e=="string"?tokenizer(e,t.filename,t.html5_comments,t.shebang):e,token:null,prev:null,peeked:null,in_function:0,in_async:-1,in_generator:-1,in_directives:true,in_loop:0,labels:[]};i.token=next();function is(e,t){return is_token(i.token,e,t)}function peek(){return i.peeked||(i.peeked=i.input())}function next(){i.prev=i.token;if(!i.peeked)peek();i.token=i.peeked;i.peeked=null;i.in_directives=i.in_directives&&(i.token.type=="string"||is("punc",";"));return i.token}function prev(){return i.prev}function croak(e,t,n,r){var o=i.input.context();js_error(e,o.filename,t!=null?t:o.tokline,n!=null?n:o.tokcol,r!=null?r:o.tokpos)}function token_error(e,t){croak(t,e.line,e.col)}function unexpected(e){if(e==null)e=i.token;token_error(e,"Unexpected token: "+e.type+" ("+e.value+")")}function expect_token(e,t){if(is(e,t)){return next()}token_error(i.token,"Unexpected token "+i.token.type+" «"+i.token.value+"»"+", expected "+e+" «"+t+"»")}function expect(e){return expect_token("punc",e)}function has_newline_before(e){return e.nlb||!e.comments_before.every((e=>!e.nlb))}function can_insert_semicolon(){return!t.strict&&(is("eof")||is("punc","}")||has_newline_before(i.token))}function is_in_generator(){return i.in_generator===i.in_function}function is_in_async(){return i.in_async===i.in_function}function can_await(){return i.in_async===i.in_function||i.in_function===0&&i.input.has_directive("use strict")}function semicolon(e){if(is("punc",";"))next();else if(!e&&!can_insert_semicolon())unexpected()}function parenthesised(){expect("(");var e=expression(true);expect(")");return e}function embed_tokens(e){return function _embed_tokens_wrapper(...t){const n=i.token;const r=e(...t);r.start=n;r.end=prev();return r}}function handle_regexp(){if(is("operator","/")||is("operator","/=")){i.peeked=null;i.token=i.input(i.token.value.substr(1))}}var r=embed_tokens((function statement(e,n,r){handle_regexp();switch(i.token.type){case"string":if(i.in_directives){var o=peek();if(!a.includes("\\")&&(is_token(o,"punc",";")||is_token(o,"punc","}")||has_newline_before(o)||is_token(o,"eof"))){i.input.add_directive(i.token.value)}else{i.in_directives=false}}var s=i.in_directives,u=simple_statement();return s&&u.body instanceof Gt?new G(u.body):u;case"template_head":case"num":case"big_int":case"regexp":case"operator":case"atom":return simple_statement();case"name":if(i.token.value=="async"&&is_token(peek(),"keyword","function")){next();next();if(n){croak("functions are not allowed as the body of a loop")}return function_(fe,false,true,e)}if(i.token.value=="import"&&!is_token(peek(),"punc","(")&&!is_token(peek(),"punc",".")){next();var l=import_statement();semicolon();return l}return is_token(peek(),"punc",":")?labeled_statement():simple_statement();case"punc":switch(i.token.value){case"{":return new W({start:i.token,body:block_(),end:prev()});case"[":case"(":return simple_statement();case";":i.in_directives=false;next();return new q;default:unexpected()}case"keyword":switch(i.token.value){case"break":next();return break_cont(be);case"continue":next();return break_cont(ye);case"debugger":next();semicolon();return new K;case"do":next();var c=in_loop(statement);expect_token("keyword","while");var f=parenthesised();semicolon(true);return new Q({body:c,condition:f});case"while":next();return new J({condition:parenthesised(),body:in_loop((function(){return statement(false,true)}))});case"for":next();return for_();case"class":next();if(n){croak("classes are not allowed as the body of a loop")}if(r){croak("classes are not allowed as the body of an if")}return class_(ht,e);case"function":next();if(n){croak("functions are not allowed as the body of a loop")}return function_(fe,false,false,e);case"if":next();return if_();case"return":if(i.in_function==0&&!t.bare_returns)croak("'return' outside of function");next();var _=null;if(is("punc",";")){next()}else if(!can_insert_semicolon()){_=expression(true);semicolon()}return new ge({value:_});case"switch":next();return new ke({expression:parenthesised(),body:in_loop(switch_body_)});case"throw":next();if(has_newline_before(i.token))croak("Illegal newline after 'throw'");var _=expression(true);semicolon();return new ve({value:_});case"try":next();return try_();case"var":next();var l=var_();semicolon();return l;case"let":next();var l=let_();semicolon();return l;case"const":next();var l=const_();semicolon();return l;case"with":if(i.input.has_directive("use strict")){croak("Strict mode may not include a with statement")}next();return new ie({expression:parenthesised(),body:statement()});case"export":if(!is_token(peek(),"punc","(")){next();var l=export_statement();if(is("punc",";"))semicolon();return l}}}unexpected()}));function labeled_statement(){var e=as_symbol(It);if(e.name==="await"&&is_in_async()){token_error(i.prev,"await cannot be used as label inside async function")}if(i.labels.some((t=>t.name===e.name))){croak("Label "+e.name+" defined twice")}expect(":");i.labels.push(e);var t=r();i.labels.pop();if(!(t instanceof $)){e.references.forEach((function(t){if(t instanceof ye){t=t.label.start;croak("Continue label `"+e.name+"` refers to non-IterationStatement.",t.line,t.col,t.pos)}}))}return new j({body:t,label:e})}function simple_statement(e){return new H({body:(e=expression(true),semicolon(),e)})}function break_cont(e){var t=null,n;if(!can_insert_semicolon()){t=as_symbol(Vt,true)}if(t!=null){n=i.labels.find((e=>e.name===t.name));if(!n)croak("Undefined label "+t.name);t.thedef=n}else if(i.in_loop==0)croak(e.TYPE+" not inside a loop or switch");semicolon();var r=new e({label:t});if(n)n.references.push(r);return r}function for_(){var e="`for await` invalid in this context";var t=i.token;if(t.type=="name"&&t.value=="await"){if(!can_await()){token_error(t,e)}next()}else{t=false}expect("(");var n=null;if(!is("punc",";")){n=is("keyword","var")?(next(),var_(true)):is("keyword","let")?(next(),let_(true)):is("keyword","const")?(next(),const_(true)):expression(true,true);var r=is("operator","in");var o=is("name","of");if(t&&!o){token_error(t,e)}if(r||o){if(n instanceof we){if(n.definitions.length>1)token_error(n.start,"Only one variable declaration allowed in for..in loop")}else if(!(is_assignable(n)||(n=to_destructuring(n))instanceof _e)){token_error(n.start,"Invalid left-hand side in for..in loop")}next();if(r){return for_in(n)}else{return for_of(n,!!t)}}}else if(t){token_error(t,e)}return regular_for(n)}function regular_for(e){expect(";");var t=is("punc",";")?null:expression(true);expect(";");var n=is("punc",")")?null:expression(true);expect(")");return new ee({init:e,condition:t,step:n,body:in_loop((function(){return r(false,true)}))})}function for_of(e,t){var n=e instanceof we?e.definitions[0].name:null;var i=expression(true);expect(")");return new ne({await:t,init:e,name:n,object:i,body:in_loop((function(){return r(false,true)}))})}function for_in(e){var t=expression(true);expect(")");return new te({init:e,object:t,body:in_loop((function(){return r(false,true)}))})}var arrow_function=function(e,t,n){if(has_newline_before(i.token)){croak("Unexpected newline before arrow (=>)")}expect_token("arrow","=>");var r=_function_body(is("punc","{"),false,n);var o=r instanceof Array&&r.length?r[r.length-1].end:r instanceof Array?e:r.end;return new ce({start:e,end:o,async:n,argnames:t,body:r})};var function_=function(e,t,n,i){var r=e===fe;var o=is("operator","*");if(o){next()}var a=is("name")?as_symbol(r?kt:Ft):null;if(r&&!a){if(i){e=le}else{unexpected()}}if(a&&e!==ue&&!(a instanceof Dt))unexpected(prev());var s=[];var u=_function_body(true,o||t,n,a,s);return new e({start:s.start,end:u.end,is_generator:o,async:n,name:a,argnames:s,body:u})};class UsedParametersTracker{constructor(e,t,n=false){this.is_parameter=e;this.duplicates_ok=n;this.parameters=new Set;this.duplicate=null;this.default_assignment=false;this.spread=false;this.strict_mode=!!t}add_parameter(e){if(this.parameters.has(e.value)){if(this.duplicate===null){this.duplicate=e}this.check_strict()}else{this.parameters.add(e.value);if(this.is_parameter){switch(e.value){case"arguments":case"eval":case"yield":if(this.strict_mode){token_error(e,"Unexpected "+e.value+" identifier as parameter inside strict mode")}break;default:if(c.has(e.value)){unexpected()}}}}}mark_default_assignment(e){if(this.default_assignment===false){this.default_assignment=e}}mark_spread(e){if(this.spread===false){this.spread=e}}mark_strict_mode(){this.strict_mode=true}is_strict(){return this.default_assignment!==false||this.spread!==false||this.strict_mode}check_strict(){if(this.is_strict()&&this.duplicate!==null&&!this.duplicates_ok){token_error(this.duplicate,"Parameter "+this.duplicate.value+" was used already")}}}function parameters(e){var t=new UsedParametersTracker(true,i.input.has_directive("use strict"));expect("(");while(!is("punc",")")){var n=parameter(t);e.push(n);if(!is("punc",")")){expect(",")}if(n instanceof ae){break}}next()}function parameter(e,t){var n;var r=false;if(e===undefined){e=new UsedParametersTracker(true,i.input.has_directive("use strict"))}if(is("expand","...")){r=i.token;e.mark_spread(i.token);next()}n=binding_element(e,t);if(is("operator","=")&&r===false){e.mark_default_assignment(i.token);next();n=new nt({start:n.start,left:n,operator:"=",right:expression(false),end:i.token})}if(r!==false){if(!is("punc",")")){unexpected()}n=new ae({start:r,expression:n,end:r})}e.check_strict();return n}function binding_element(e,t){var n=[];var r=true;var o=false;var a;var s=i.token;if(e===undefined){const n=i.input.has_directive("use strict");const r=t===bt;e=new UsedParametersTracker(false,n,r)}t=t===undefined?Tt:t;if(is("punc","[")){next();while(!is("punc","]")){if(r){r=false}else{expect(",")}if(is("expand","...")){o=true;a=i.token;e.mark_spread(i.token);next()}if(is("punc")){switch(i.token.value){case",":n.push(new Zt({start:i.token,end:i.token}));continue;case"]":break;case"[":case"{":n.push(binding_element(e,t));break;default:unexpected()}}else if(is("name")){e.add_parameter(i.token);n.push(as_symbol(t))}else{croak("Invalid function parameter")}if(is("operator","=")&&o===false){e.mark_default_assignment(i.token);next();n[n.length-1]=new nt({start:n[n.length-1].start,left:n[n.length-1],operator:"=",right:expression(false),end:i.token})}if(o){if(!is("punc","]")){croak("Rest element must be last element")}n[n.length-1]=new ae({start:a,expression:n[n.length-1],end:a})}}expect("]");e.check_strict();return new _e({start:s,names:n,is_array:true,end:prev()})}else if(is("punc","{")){next();while(!is("punc","}")){if(r){r=false}else{expect(",")}if(is("expand","...")){o=true;a=i.token;e.mark_spread(i.token);next()}if(is("name")&&(is_token(peek(),"punc")||is_token(peek(),"operator"))&&[",","}","="].includes(peek().value)){e.add_parameter(i.token);var u=prev();var l=as_symbol(t);if(o){n.push(new ae({start:a,expression:l,end:l.end}))}else{n.push(new at({start:u,key:l.name,value:l,end:l.end}))}}else if(is("punc","}")){continue}else{var c=i.token;var f=as_property_name();if(f===null){unexpected(prev())}else if(prev().type==="name"&&!is("punc",":")){n.push(new at({start:prev(),key:f,value:new t({start:prev(),name:f,end:prev()}),end:prev()}))}else{expect(":");n.push(new at({start:c,quote:c.quote,key:f,value:binding_element(e,t),end:prev()}))}}if(o){if(!is("punc","}")){croak("Rest element must be last element")}}else if(is("operator","=")){e.mark_default_assignment(i.token);next();n[n.length-1].value=new nt({start:n[n.length-1].value.start,left:n[n.length-1].value,operator:"=",right:expression(false),end:i.token})}}expect("}");e.check_strict();return new _e({start:s,names:n,is_array:false,end:prev()})}else if(is("name")){e.add_parameter(i.token);return as_symbol(t)}else{croak("Invalid function parameter")}}function params_or_seq_(e,t){var n;var r;var o;var a=[];expect("(");while(!is("punc",")")){if(n)unexpected(n);if(is("expand","...")){n=i.token;if(t)r=i.token;next();a.push(new ae({start:prev(),expression:expression(),end:i.token}))}else{a.push(expression())}if(!is("punc",")")){expect(",");if(is("punc",")")){o=prev();if(t)r=o}}}expect(")");if(e&&is("arrow","=>")){if(n&&o)unexpected(o)}else if(r){unexpected(r)}return a}function _function_body(e,t,n,r,o){var a=i.in_loop;var s=i.labels;var u=i.in_generator;var l=i.in_async;++i.in_function;if(t)i.in_generator=i.in_function;if(n)i.in_async=i.in_function;if(o)parameters(o);if(e)i.in_directives=true;i.in_loop=0;i.labels=[];if(e){i.input.push_directives_stack();var c=block_();if(r)_verify_symbol(r);if(o)o.forEach(_verify_symbol);i.input.pop_directives_stack()}else{var c=[new ge({start:i.token,value:expression(false),end:i.token})]}--i.in_function;i.in_loop=a;i.labels=s;i.in_generator=u;i.in_async=l;return c}function _await_expression(){if(!can_await()){croak("Unexpected await expression outside async function",i.prev.line,i.prev.col,i.prev.pos)}return new Se({start:prev(),end:i.token,expression:maybe_unary(true)})}function _yield_expression(){if(!is_in_generator()){croak("Unexpected yield expression outside generator function",i.prev.line,i.prev.col,i.prev.pos)}var e=i.token;var t=false;var n=true;if(can_insert_semicolon()||is("punc")&&A.has(i.token.value)){n=false}else if(is("operator","*")){t=true;next()}return new Ae({start:e,is_star:t,expression:n?expression():null,end:prev()})}function if_(){var e=parenthesised(),t=r(false,false,true),n=null;if(is("keyword","else")){next();n=r(false,false,true)}return new Te({condition:e,body:t,alternative:n})}function block_(){expect("{");var e=[];while(!is("punc","}")){if(is("eof"))unexpected();e.push(r())}next();return e}function switch_body_(){expect("{");var e=[],t=null,n=null,o;while(!is("punc","}")){if(is("eof"))unexpected();if(is("keyword","case")){if(n)n.end=prev();t=[];n=new Fe({start:(o=i.token,next(),o),expression:expression(true),body:t});e.push(n);expect(":")}else if(is("keyword","default")){if(n)n.end=prev();t=[];n=new Re({start:(o=i.token,next(),expect(":"),o),body:t});e.push(n)}else{if(!t)unexpected();t.push(r())}}if(n)n.end=prev();next();return e}function try_(){var e=block_(),t=null,n=null;if(is("keyword","catch")){var r=i.token;next();if(is("punc","{")){var o=null}else{expect("(");var o=parameter(undefined,Mt);expect(")")}t=new xe({start:r,argname:o,body:block_(),end:prev()})}if(is("keyword","finally")){var r=i.token;next();n=new Me({start:r,body:block_(),end:prev()})}if(!t&&!n)croak("Missing catch/finally blocks");return new Oe({body:e,bcatch:t,bfinally:n})}function vardefs(e,t){var n=[];var r;for(;;){var o=t==="var"?bt:t==="const"?St:t==="let"?At:null;if(is("punc","{")||is("punc","[")){r=new Be({start:i.token,name:binding_element(undefined,o),value:is("operator","=")?(expect_token("operator","="),expression(false,e)):null,end:prev()})}else{r=new Be({start:i.token,name:as_symbol(o),value:is("operator","=")?(next(),expression(false,e)):!e&&t==="const"?croak("Missing initializer in const declaration"):null,end:prev()});if(r.name.name=="import")croak("Unexpected token: import")}n.push(r);if(!is("punc",","))break;next()}return n}var var_=function(e){return new Ne({start:prev(),definitions:vardefs(e,"var"),end:prev()})};var let_=function(e){return new Ie({start:prev(),definitions:vardefs(e,"let"),end:prev()})};var const_=function(e){return new Pe({start:prev(),definitions:vardefs(e,"const"),end:prev()})};var new_=function(e){var t=i.token;expect_token("operator","new");if(is("punc",".")){next();expect_token("name","target");return subscripts(new vt({start:t,end:prev()}),e)}var n=expr_atom(false),r;if(is("punc","(")){next();r=expr_list(")",true)}else{r=[]}var o=new Ge({start:t,expression:n,args:r,end:prev()});annotate(o);return subscripts(o,e)};function as_atom_node(){var e=i.token,t;switch(e.type){case"name":t=_make_symbol(Pt);break;case"num":t=new Ht({start:e,end:e,value:e.value,raw:a});break;case"big_int":t=new Xt({start:e,end:e,value:e.value});break;case"string":t=new Gt({start:e,end:e,value:e.value,quote:e.quote});break;case"regexp":const[n,i,r]=e.value.match(/^\/(.*)\/(\w*)$/);t=new Wt({start:e,end:e,value:{source:i,flags:r}});break;case"atom":switch(e.value){case"false":t=new en({start:e,end:e});break;case"true":t=new tn({start:e,end:e});break;case"null":t=new Yt({start:e,end:e});break}break}next();return t}function to_fun_args(e,t){var insert_default=function(e,t){if(t){return new nt({start:e.start,left:e,operator:"=",right:t,end:t.end})}return e};if(e instanceof rt){return insert_default(new _e({start:e.start,end:e.end,is_array:false,names:e.properties.map((e=>to_fun_args(e)))}),t)}else if(e instanceof at){e.value=to_fun_args(e.value);return insert_default(e,t)}else if(e instanceof Zt){return e}else if(e instanceof _e){e.names=e.names.map((e=>to_fun_args(e)));return insert_default(e,t)}else if(e instanceof Pt){return insert_default(new Tt({name:e.name,start:e.start,end:e.end}),t)}else if(e instanceof ae){e.expression=to_fun_args(e.expression);return insert_default(e,t)}else if(e instanceof it){return insert_default(new _e({start:e.start,end:e.end,is_array:true,names:e.elements.map((e=>to_fun_args(e)))}),t)}else if(e instanceof tt){return insert_default(to_fun_args(e.left,e.right),t)}else if(e instanceof nt){e.left=to_fun_args(e.left);return e}else{croak("Invalid function parameter",e.start.line,e.start.col)}}var expr_atom=function(e,t){if(is("operator","new")){return new_(e)}if(is("operator","import")){return import_meta()}var r=i.token;var a;var s=is("name","async")&&(a=peek()).value!="["&&a.type!="arrow"&&as_atom_node();if(is("punc")){switch(i.token.value){case"(":if(s&&!e)break;var u=params_or_seq_(t,!s);if(t&&is("arrow","=>")){return arrow_function(r,u.map((e=>to_fun_args(e))),!!s)}var c=s?new Ke({expression:s,args:u}):u.length==1?u[0]:new He({expressions:u});if(c.start){const e=r.comments_before.length;n.set(r,e);c.start.comments_before.unshift(...r.comments_before);r.comments_before=c.start.comments_before;if(e==0&&r.comments_before.length>0){var f=r.comments_before[0];if(!f.nlb){f.nlb=r.nlb;r.nlb=false}}r.comments_after=c.start.comments_after}c.start=r;var _=prev();if(c.end){_.comments_before=c.end.comments_before;c.end.comments_after.push(..._.comments_after);_.comments_after=c.end.comments_after}c.end=_;if(c instanceof Ke)annotate(c);return subscripts(c,e);case"[":return subscripts(o(),e);case"{":return subscripts(l(),e)}if(!s)unexpected()}if(t&&is("name")&&is_token(peek(),"arrow")){var p=new Tt({name:i.token.value,start:r,end:r});next();return arrow_function(r,[p],!!s)}if(is("keyword","function")){next();var d=function_(le,false,!!s);d.start=r;d.end=prev();return subscripts(d,e)}if(s)return subscripts(s,e);if(is("keyword","class")){next();var m=class_(Et);m.start=r;m.end=prev();return subscripts(m,e)}if(is("template_head")){return subscripts(template_string(),e)}if(I.has(i.token.type)){return subscripts(as_atom_node(),e)}unexpected()};function template_string(){var e=[],t=i.token;e.push(new me({start:i.token,raw:s.get(i.token),value:i.token.value,end:i.token}));while(!i.token.template_end){next();handle_regexp();e.push(expression(true));e.push(new me({start:i.token,raw:s.get(i.token),value:i.token.value,end:i.token}))}next();return new de({start:t,segments:e,end:i.token})}function expr_list(e,t,n){var r=true,o=[];while(!is("punc",e)){if(r)r=false;else expect(",");if(t&&is("punc",e))break;if(is("punc",",")&&n){o.push(new Zt({start:i.token,end:i.token}))}else if(is("expand","...")){next();o.push(new ae({start:prev(),expression:expression(),end:i.token}))}else{o.push(expression(false))}}next();return o}var o=embed_tokens((function(){expect("[");return new it({elements:expr_list("]",!t.strict,true)})}));var u=embed_tokens(((e,t)=>function_(ue,e,t)));var l=embed_tokens((function object_or_destructuring_(){var e=i.token,n=true,r=[];expect("{");while(!is("punc","}")){if(n)n=false;else expect(",");if(!t.strict&&is("punc","}"))break;e=i.token;if(e.type=="expand"){next();r.push(new ae({start:e,expression:expression(false),end:prev()}));continue}var o=as_property_name();var a;if(!is("punc",":")){var s=concise_method_or_getset(o,e);if(s){r.push(s);continue}a=new Pt({start:prev(),name:o,end:prev()})}else if(o===null){unexpected(prev())}else{next();a=expression(false)}if(is("operator","=")){next();a=new tt({start:e,left:a,operator:"=",right:expression(false),logical:false,end:prev()})}r.push(new at({start:e,quote:e.quote,key:o instanceof z?o:""+o,value:a,end:prev()}))}next();return new rt({properties:r})}));function class_(e,t){var n,r,o,a,s=[];i.input.push_directives_stack();i.input.add_directive("use strict");if(i.token.type=="name"&&i.token.value!="extends"){o=as_symbol(e===ht?Ot:xt)}if(e===ht&&!o){if(t){e=Et}else{unexpected()}}if(i.token.value=="extends"){next();a=expression(true)}expect("{");while(is("punc",";")){next()}while(!is("punc","}")){n=i.token;r=concise_method_or_getset(as_property_name(),n,true);if(!r){unexpected()}s.push(r);while(is("punc",";")){next()}}i.input.pop_directives_stack();next();return new e({start:n,name:o,extends:a,properties:s,end:prev()})}function concise_method_or_getset(e,t,n){const get_symbol_ast=(e,n=Ct)=>{if(typeof e==="string"||typeof e==="number"){return new n({start:t,name:""+e,end:prev()})}else if(e===null){unexpected()}return e};const is_not_method_start=()=>!is("punc","(")&&!is("punc",",")&&!is("punc","}")&&!is("punc",";")&&!is("operator","=");var i=false;var r=false;var o=false;var a=false;var s=null;if(n&&e==="static"&&is_not_method_start()){r=true;e=as_property_name()}if(e==="async"&&is_not_method_start()){i=true;e=as_property_name()}if(prev().type==="operator"&&prev().value==="*"){o=true;e=as_property_name()}if((e==="get"||e==="set")&&is_not_method_start()){s=e;e=as_property_name()}if(prev().type==="privatename"){a=true}const l=prev();if(s!=null){if(!a){const n=s==="get"?ct:lt;e=get_symbol_ast(e);return new n({start:t,static:r,key:e,quote:e instanceof Ct?l.quote:undefined,value:u(),end:prev()})}else{const n=s==="get"?ut:st;return new n({start:t,static:r,key:get_symbol_ast(e),value:u(),end:prev()})}}if(is("punc","(")){e=get_symbol_ast(e);const n=a?_t:ft;var c=new n({start:t,static:r,is_generator:o,async:i,key:e,quote:e instanceof Ct?l.quote:undefined,value:u(o,i),end:prev()});return c}if(n){const n=get_symbol_ast(e,Rt);const i=n instanceof Rt?l.quote:undefined;const o=a?mt:dt;if(is("operator","=")){next();return new o({start:t,static:r,quote:i,key:n,value:expression(false),end:prev()})}else if(is("name")||is("privatename")||is("operator","*")||is("punc",";")||is("punc","}")){return new o({start:t,static:r,quote:i,key:n,end:prev()})}}}function maybe_import_assertion(){if(is("name","assert")&&!has_newline_before(i.token)){next();return l()}return null}function import_statement(){var e=prev();var t;var n;if(is("name")){t=as_symbol(wt)}if(is("punc",",")){next()}n=map_names(true);if(n||t){expect_token("name","from")}var r=i.token;if(r.type!=="string"){unexpected()}next();const o=maybe_import_assertion();return new Ve({start:e,imported_name:t,imported_names:n,module_name:new Gt({start:r,value:r.value,quote:r.quote,end:r}),assert_clause:o,end:i.token})}function import_meta(){var e=i.token;expect_token("operator","import");expect_token("punc",".");expect_token("name","meta");return subscripts(new ze({start:e,end:prev()}),false)}function map_name(e){function make_symbol(e){return new e({name:as_property_name(),start:prev(),end:prev()})}var t=e?Nt:Lt;var n=e?wt:Bt;var r=i.token;var o;var a;if(e){o=make_symbol(t)}else{a=make_symbol(n)}if(is("name","as")){next();if(e){a=make_symbol(n)}else{o=make_symbol(t)}}else if(e){a=new n(o)}else{o=new t(a)}return new Le({start:r,foreign_name:o,name:a,end:prev()})}function map_nameAsterisk(e,t){var n=e?Nt:Lt;var r=e?wt:Bt;var o=i.token;var a;var s=prev();t=t||new r({name:"*",start:o,end:s});a=new n({name:"*",start:o,end:s});return new Le({start:o,foreign_name:a,name:t,end:s})}function map_names(e){var t;if(is("punc","{")){next();t=[];while(!is("punc","}")){t.push(map_name(e));if(is("punc",",")){next()}}next()}else if(is("operator","*")){var n;next();if(e&&is("name","as")){next();n=as_symbol(e?wt:Lt)}t=[map_nameAsterisk(e,n)]}return t}function export_statement(){var e=i.token;var t;var n;if(is("keyword","default")){t=true;next()}else if(n=map_names(false)){if(is("name","from")){next();var o=i.token;if(o.type!=="string"){unexpected()}next();const r=maybe_import_assertion();return new Ue({start:e,is_default:t,exported_names:n,module_name:new Gt({start:o,value:o.value,quote:o.quote,end:o}),end:prev(),assert_clause:r})}else{return new Ue({start:e,is_default:t,exported_names:n,end:prev()})}}var a;var s;var u;if(is("punc","{")||t&&(is("keyword","class")||is("keyword","function"))&&is_token(peek(),"punc")){s=expression(false);semicolon()}else if((a=r(t))instanceof we&&t){unexpected(a.start)}else if(a instanceof we||a instanceof fe||a instanceof ht){u=a}else if(a instanceof Et||a instanceof le){s=a}else if(a instanceof H){s=a.body}else{unexpected(a.start)}return new Ue({start:e,is_default:t,exported_value:s,exported_definition:u,end:prev(),assert_clause:null})}function as_property_name(){var e=i.token;switch(e.type){case"punc":if(e.value==="["){next();var t=expression(false);expect("]");return t}else unexpected(e);case"operator":if(e.value==="*"){next();return null}if(!["delete","in","instanceof","new","typeof","void"].includes(e.value)){unexpected(e)}case"name":case"privatename":case"string":case"num":case"big_int":case"keyword":case"atom":next();return e.value;default:unexpected(e)}}function as_name(){var e=i.token;if(e.type!="name"&&e.type!="privatename")unexpected();next();return e.value}function _make_symbol(e){var t=i.token.value;return new(t=="this"?zt:t=="super"?Ut:e)({name:String(t),start:i.token,end:i.token})}function _verify_symbol(e){var t=e.name;if(is_in_generator()&&t=="yield"){token_error(e.start,"Yield cannot be used as identifier inside generators")}if(i.input.has_directive("use strict")){if(t=="yield"){token_error(e.start,"Unexpected yield identifier inside strict mode")}if(e instanceof Dt&&(t=="arguments"||t=="eval")){token_error(e.start,"Unexpected "+t+" in strict mode")}}}function as_symbol(e,t){if(!is("name")){if(!t)croak("Name expected");return null}var n=_make_symbol(e);_verify_symbol(n);next();return n}function annotate(e){var t=e.start;var i=t.comments_before;const r=n.get(t);var o=r!=null?r:i.length;while(--o>=0){var a=i[o];if(/[@#]__/.test(a.value)){if(/[@#]__PURE__/.test(a.value)){set_annotation(e,rn);break}if(/[@#]__INLINE__/.test(a.value)){set_annotation(e,on);break}if(/[@#]__NOINLINE__/.test(a.value)){set_annotation(e,an);break}}}}var subscripts=function(e,t,n){var i=e.start;if(is("punc",".")){next();const r=is("privatename")?qe:We;return subscripts(new r({start:i,expression:e,optional:false,property:as_name(),end:prev()}),t,n)}if(is("punc","[")){next();var r=expression(true);expect("]");return subscripts(new Ye({start:i,expression:e,optional:false,property:r,end:prev()}),t,n)}if(t&&is("punc","(")){next();var o=new Ke({start:i,expression:e,optional:false,args:call_args(),end:prev()});annotate(o);return subscripts(o,true,n)}if(is("punc","?.")){next();let n;if(t&&is("punc","(")){next();const t=new Ke({start:i,optional:true,expression:e,args:call_args(),end:prev()});annotate(t);n=subscripts(t,true,true)}else if(is("name")||is("privatename")){const r=is("privatename")?qe:We;n=subscripts(new r({start:i,expression:e,optional:true,property:as_name(),end:prev()}),t,true)}else if(is("punc","[")){next();const r=expression(true);expect("]");n=subscripts(new Ye({start:i,expression:e,optional:true,property:r,end:prev()}),t,true)}if(!n)unexpected();if(n instanceof je)return n;return new je({start:i,expression:n,end:prev()})}if(is("template_head")){if(n){unexpected()}return subscripts(new pe({start:i,prefix:e,template_string:template_string(),end:prev()}),t)}return e};function call_args(){var e=[];while(!is("punc",")")){if(is("expand","...")){next();e.push(new ae({start:prev(),expression:expression(false),end:prev()}))}else{e.push(expression(false))}if(!is("punc",")")){expect(",")}}next();return e}var maybe_unary=function(e,t){var n=i.token;if(n.type=="name"&&n.value=="await"&&can_await()){next();return _await_expression()}if(is("operator")&&O.has(n.value)){next();handle_regexp();var r=make_unary(Ze,n,maybe_unary(e));r.start=n;r.end=prev();return r}var o=expr_atom(e,t);while(is("operator")&&x.has(i.token.value)&&!has_newline_before(i.token)){if(o instanceof ce)unexpected();o=make_unary(Qe,i.token,o);o.start=n;o.end=i.token;next()}return o};function make_unary(e,t,n){var r=t.value;switch(r){case"++":case"--":if(!is_assignable(n))croak("Invalid use of "+r+" operator",t.line,t.col,t.pos);break;case"delete":if(n instanceof Pt&&i.input.has_directive("use strict"))croak("Calling delete on expression not allowed in strict mode",n.start.line,n.start.col,n.start.pos);break}return new e({operator:r,expression:n})}var expr_op=function(e,t,n){var r=is("operator")?i.token.value:null;if(r=="in"&&n)r=null;if(r=="**"&&e instanceof Ze&&!is_token(e.start,"punc","(")&&e.operator!=="--"&&e.operator!=="++")unexpected(e.start);var o=r!=null?N[r]:null;if(o!=null&&(o>t||r==="**"&&t===o)){next();var a=expr_op(maybe_unary(true),o,n);return expr_op(new Je({start:e.start,left:e,operator:r,right:a,end:a.end}),t,n)}return e};function expr_ops(e){return expr_op(maybe_unary(true,true),0,e)}var maybe_conditional=function(e){var t=i.token;var n=expr_ops(e);if(is("operator","?")){next();var r=expression(false);expect(":");return new et({start:t,condition:n,consequent:r,alternative:expression(false,e),end:prev()})}return n};function is_assignable(e){return e instanceof Xe||e instanceof Pt}function to_destructuring(e){if(e instanceof rt){e=new _e({start:e.start,names:e.properties.map(to_destructuring),is_array:false,end:e.end})}else if(e instanceof it){var t=[];for(var n=0;n<e.elements.length;n++){if(e.elements[n]instanceof ae){if(n+1!==e.elements.length){token_error(e.elements[n].start,"Spread must the be last element in destructuring array")}e.elements[n].expression=to_destructuring(e.elements[n].expression)}t.push(to_destructuring(e.elements[n]))}e=new _e({start:e.start,names:t,is_array:true,end:e.end})}else if(e instanceof ot){e.value=to_destructuring(e.value)}else if(e instanceof tt){e=new nt({start:e.start,left:e.left,operator:"=",right:e.right,end:e.end})}return e}var maybe_assign=function(e){handle_regexp();var t=i.token;if(t.type=="name"&&t.value=="yield"){if(is_in_generator()){next();return _yield_expression()}else if(i.input.has_directive("use strict")){token_error(i.token,"Unexpected yield identifier inside strict mode")}}var n=maybe_conditional(e);var r=i.token.value;if(is("operator")&&M.has(r)){if(is_assignable(n)||(n=to_destructuring(n))instanceof _e){next();return new tt({start:t,left:n,operator:r,right:maybe_assign(e),logical:w.has(r),end:prev()})}croak("Invalid assignment")}return n};var expression=function(e,t){var n=i.token;var r=[];while(true){r.push(maybe_assign(t));if(!e||!is("punc",","))break;next();e=true}return r.length==1?r[0]:new He({start:n,expressions:r,end:peek()})};function in_loop(e){++i.in_loop;var t=e();--i.in_loop;return t}if(t.expression){return expression(true)}return function parse_toplevel(){var e=i.token;var n=[];i.input.push_directives_stack();if(t.module)i.input.add_directive("use strict");while(!is("eof")){n.push(r())}i.input.pop_directives_stack();var o=prev();var a=t.toplevel;if(a){a.body=a.body.concat(n);a.end=o}else{a=new oe({start:e,body:n,end:o})}s=new Map;return a}()}function DEFNODE(e,t,n,i,r=z){if(!t)t=[];else t=t.split(/\s+/);var o=t;if(r&&r.PROPS)t=t.concat(r.PROPS);const a=r&&Object.create(r.prototype);if(a){n.prototype=a;n.BASE=r}if(r)r.SUBCLASSES.push(n);n.prototype.CTOR=n;n.prototype.constructor=n;n.PROPS=t||null;n.SELF_PROPS=o;n.SUBCLASSES=[];if(e){n.prototype.TYPE=n.TYPE=e}if(i)for(let e in i)if(HOP(i,e)){if(e[0]==="$"){n[e.substr(1)]=i[e]}else{n.prototype[e]=i[e]}}n.DEFMETHOD=function(e,t){this.prototype[e]=t};return n}const has_tok_flag=(e,t)=>Boolean(e.flags&t);const set_tok_flag=(e,t,n)=>{if(n){e.flags|=t}else{e.flags&=~t}};const P=1;const B=2;const L=4;const V=8;class AST_Token{constructor(e,t,n,i,r,o,a,s,u){this.flags=o?1:0;this.type=e;this.value=t;this.line=n;this.col=i;this.pos=r;this.comments_before=a;this.comments_after=s;this.file=u;Object.seal(this)}get nlb(){return has_tok_flag(this,P)}set nlb(e){set_tok_flag(this,P,e)}get quote(){return!has_tok_flag(this,L)?"":has_tok_flag(this,B)?"'":'"'}set quote(e){set_tok_flag(this,B,e==="'");set_tok_flag(this,L,!!e)}get template_end(){return has_tok_flag(this,V)}set template_end(e){set_tok_flag(this,V,e)}}var z=DEFNODE("Node","start end",(function AST_Node(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{_clone:function(e){if(e){var t=this.clone();return t.transform(new TreeTransformer((function(e){if(e!==t){return e.clone(true)}})))}return new this.CTOR(this)},clone:function(e){return this._clone(e)},$documentation:"Base class of all AST nodes",$propdoc:{start:"[AST_Token] The first token of this node",end:"[AST_Token] The last token of this node"},_walk:function(e){return e._visit(this)},walk:function(e){return this._walk(e)},_children_backwards:()=>{}},null);var U=DEFNODE("Statement",null,(function AST_Statement(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class of all statements"});var K=DEFNODE("Debugger",null,(function AST_Debugger(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Represents a debugger statement"},U);var G=DEFNODE("Directive","value quote",(function AST_Directive(e){if(e){this.value=e.value;this.quote=e.quote;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:'Represents a directive, like "use strict";',$propdoc:{value:"[string] The value of this directive as a plain string (it's not an AST_String!)",quote:"[string] the original quote character"}},U);var H=DEFNODE("SimpleStatement","body",(function AST_SimpleStatement(e){if(e){this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A statement consisting of an expression, i.e. a = 1 + 2",$propdoc:{body:"[AST_Node] an expression node (should not be instanceof AST_Statement)"},_walk:function(e){return e._visit(this,(function(){this.body._walk(e)}))},_children_backwards(e){e(this.body)}},U);function walk_body(e,t){const n=e.body;for(var i=0,r=n.length;i<r;i++){n[i]._walk(t)}}function clone_block_scope(e){var t=this._clone(e);if(this.block_scope){t.block_scope=this.block_scope.clone()}return t}var X=DEFNODE("Block","body block_scope",(function AST_Block(e){if(e){this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A body of statements (usually braced)",$propdoc:{body:"[AST_Statement*] an array of statements",block_scope:"[AST_Scope] the block scope"},_walk:function(e){return e._visit(this,(function(){walk_body(this,e)}))},_children_backwards(e){let t=this.body.length;while(t--)e(this.body[t])},clone:clone_block_scope},U);var W=DEFNODE("BlockStatement",null,(function AST_BlockStatement(e){if(e){this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A block statement"},X);var q=DEFNODE("EmptyStatement",null,(function AST_EmptyStatement(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The empty statement (empty block or simply a semicolon)"},U);var Y=DEFNODE("StatementWithBody","body",(function AST_StatementWithBody(e){if(e){this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for all statements that contain one nested body: `For`, `ForIn`, `Do`, `While`, `With`",$propdoc:{body:"[AST_Statement] the body; this should always be present, even if it's an AST_EmptyStatement"}},U);var j=DEFNODE("LabeledStatement","label",(function AST_LabeledStatement(e){if(e){this.label=e.label;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Statement with a label",$propdoc:{label:"[AST_Label] a label definition"},_walk:function(e){return e._visit(this,(function(){this.label._walk(e);this.body._walk(e)}))},_children_backwards(e){e(this.body);e(this.label)},clone:function(e){var t=this._clone(e);if(e){var n=t.label;var i=this.label;t.walk(new TreeWalker((function(e){if(e instanceof De&&e.label&&e.label.thedef===i){e.label.thedef=n;n.references.push(e)}})))}return t}},Y);var $=DEFNODE("IterationStatement","block_scope",(function AST_IterationStatement(e){if(e){this.block_scope=e.block_scope;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Internal class.  All loops inherit from it.",$propdoc:{block_scope:"[AST_Scope] the block scope for this iteration statement."},clone:clone_block_scope},Y);var Z=DEFNODE("DWLoop","condition",(function AST_DWLoop(e){if(e){this.condition=e.condition;this.block_scope=e.block_scope;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for do/while statements",$propdoc:{condition:"[AST_Node] the loop condition.  Should not be instanceof AST_Statement"}},$);var Q=DEFNODE("Do",null,(function AST_Do(e){if(e){this.condition=e.condition;this.block_scope=e.block_scope;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `do` statement",_walk:function(e){return e._visit(this,(function(){this.body._walk(e);this.condition._walk(e)}))},_children_backwards(e){e(this.condition);e(this.body)}},Z);var J=DEFNODE("While",null,(function AST_While(e){if(e){this.condition=e.condition;this.block_scope=e.block_scope;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `while` statement",_walk:function(e){return e._visit(this,(function(){this.condition._walk(e);this.body._walk(e)}))},_children_backwards(e){e(this.body);e(this.condition)}},Z);var ee=DEFNODE("For","init condition step",(function AST_For(e){if(e){this.init=e.init;this.condition=e.condition;this.step=e.step;this.block_scope=e.block_scope;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `for` statement",$propdoc:{init:"[AST_Node?] the `for` initialization code, or null if empty",condition:"[AST_Node?] the `for` termination clause, or null if empty",step:"[AST_Node?] the `for` update clause, or null if empty"},_walk:function(e){return e._visit(this,(function(){if(this.init)this.init._walk(e);if(this.condition)this.condition._walk(e);if(this.step)this.step._walk(e);this.body._walk(e)}))},_children_backwards(e){e(this.body);if(this.step)e(this.step);if(this.condition)e(this.condition);if(this.init)e(this.init)}},$);var te=DEFNODE("ForIn","init object",(function AST_ForIn(e){if(e){this.init=e.init;this.object=e.object;this.block_scope=e.block_scope;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `for ... in` statement",$propdoc:{init:"[AST_Node] the `for/in` initialization code",object:"[AST_Node] the object that we're looping through"},_walk:function(e){return e._visit(this,(function(){this.init._walk(e);this.object._walk(e);this.body._walk(e)}))},_children_backwards(e){e(this.body);if(this.object)e(this.object);if(this.init)e(this.init)}},$);var ne=DEFNODE("ForOf","await",(function AST_ForOf(e){if(e){this.await=e.await;this.init=e.init;this.object=e.object;this.block_scope=e.block_scope;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `for ... of` statement"},te);var ie=DEFNODE("With","expression",(function AST_With(e){if(e){this.expression=e.expression;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `with` statement",$propdoc:{expression:"[AST_Node] the `with` expression"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e);this.body._walk(e)}))},_children_backwards(e){e(this.body);e(this.expression)}},Y);var re=DEFNODE("Scope","variables uses_with uses_eval parent_scope enclosed cname",(function AST_Scope(e){if(e){this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for all statements introducing a lexical scope",$propdoc:{variables:"[Map/S] a map of name -> SymbolDef for all variables/functions defined in this scope",uses_with:"[boolean/S] tells whether this scope uses the `with` statement",uses_eval:"[boolean/S] tells whether this scope contains a direct call to the global `eval`",parent_scope:"[AST_Scope?/S] link to the parent scope",enclosed:"[SymbolDef*/S] a list of all symbol definitions that are accessed from this scope or any subscopes",cname:"[integer/S] current index for mangling variables (used internally by the mangler)"},get_defun_scope:function(){var e=this;while(e.is_block_scope()){e=e.parent_scope}return e},clone:function(e,t){var n=this._clone(e);if(e&&this.variables&&t&&!this._block_scope){n.figure_out_scope({},{toplevel:t,parent_scope:this.parent_scope})}else{if(this.variables)n.variables=new Map(this.variables);if(this.enclosed)n.enclosed=this.enclosed.slice();if(this._block_scope)n._block_scope=this._block_scope}return n},pinned:function(){return this.uses_eval||this.uses_with}},X);var oe=DEFNODE("Toplevel","globals",(function AST_Toplevel(e){if(e){this.globals=e.globals;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The toplevel scope",$propdoc:{globals:"[Map/S] a map of name -> SymbolDef for all undeclared names"},wrap_commonjs:function(e){var t=this.body;var n="(function(exports){'$ORIG';})(typeof "+e+"=='undefined'?("+e+"={}):"+e+");";n=parse(n);n=n.transform(new TreeTransformer((function(e){if(e instanceof G&&e.value=="$ORIG"){return i.splice(t)}})));return n},wrap_enclose:function(e){if(typeof e!="string")e="";var t=e.indexOf(":");if(t<0)t=e.length;var n=this.body;return parse(["(function(",e.slice(0,t),'){"$ORIG"})(',e.slice(t+1),")"].join("")).transform(new TreeTransformer((function(e){if(e instanceof G&&e.value=="$ORIG"){return i.splice(n)}})))}},re);var ae=DEFNODE("Expansion","expression",(function AST_Expansion(e){if(e){this.expression=e.expression;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An expandible argument, such as ...rest, a splat, such as [1,2,...all], or an expansion in a variable declaration, such as var [first, ...rest] = list",$propdoc:{expression:"[AST_Node] the thing to be expanded"},_walk:function(e){return e._visit(this,(function(){this.expression.walk(e)}))},_children_backwards(e){e(this.expression)}});var se=DEFNODE("Lambda","name argnames uses_arguments is_generator async",(function AST_Lambda(e){if(e){this.name=e.name;this.argnames=e.argnames;this.uses_arguments=e.uses_arguments;this.is_generator=e.is_generator;this.async=e.async;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for functions",$propdoc:{name:"[AST_SymbolDeclaration?] the name of this function",argnames:"[AST_SymbolFunarg|AST_Destructuring|AST_Expansion|AST_DefaultAssign*] array of function arguments, destructurings, or expanding arguments",uses_arguments:"[boolean/S] tells whether this function accesses the arguments array",is_generator:"[boolean] is this a generator method",async:"[boolean] is this method async"},args_as_names:function(){var e=[];for(var t=0;t<this.argnames.length;t++){if(this.argnames[t]instanceof _e){e.push(...this.argnames[t].all_symbols())}else{e.push(this.argnames[t])}}return e},_walk:function(e){return e._visit(this,(function(){if(this.name)this.name._walk(e);var t=this.argnames;for(var n=0,i=t.length;n<i;n++){t[n]._walk(e)}walk_body(this,e)}))},_children_backwards(e){let t=this.body.length;while(t--)e(this.body[t]);t=this.argnames.length;while(t--)e(this.argnames[t]);if(this.name)e(this.name)},is_braceless(){return this.body[0]instanceof ge&&this.body[0].value},length_property(){let e=0;for(const t of this.argnames){if(t instanceof Tt||t instanceof _e){e++}}return e}},re);var ue=DEFNODE("Accessor",null,(function AST_Accessor(e){if(e){this.name=e.name;this.argnames=e.argnames;this.uses_arguments=e.uses_arguments;this.is_generator=e.is_generator;this.async=e.async;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A setter/getter function.  The `name` property is always null."},se);var le=DEFNODE("Function",null,(function AST_Function(e){if(e){this.name=e.name;this.argnames=e.argnames;this.uses_arguments=e.uses_arguments;this.is_generator=e.is_generator;this.async=e.async;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A function expression"},se);var ce=DEFNODE("Arrow",null,(function AST_Arrow(e){if(e){this.name=e.name;this.argnames=e.argnames;this.uses_arguments=e.uses_arguments;this.is_generator=e.is_generator;this.async=e.async;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An ES6 Arrow function ((a) => b)"},se);var fe=DEFNODE("Defun",null,(function AST_Defun(e){if(e){this.name=e.name;this.argnames=e.argnames;this.uses_arguments=e.uses_arguments;this.is_generator=e.is_generator;this.async=e.async;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A function definition"},se);var _e=DEFNODE("Destructuring","names is_array",(function AST_Destructuring(e){if(e){this.names=e.names;this.is_array=e.is_array;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A destructuring of several names. Used in destructuring assignment and with destructuring function argument names",$propdoc:{names:"[AST_Node*] Array of properties or elements",is_array:"[Boolean] Whether the destructuring represents an object or array"},_walk:function(e){return e._visit(this,(function(){this.names.forEach((function(t){t._walk(e)}))}))},_children_backwards(e){let t=this.names.length;while(t--)e(this.names[t])},all_symbols:function(){var e=[];this.walk(new TreeWalker((function(t){if(t instanceof gt){e.push(t)}})));return e}});var pe=DEFNODE("PrefixedTemplateString","template_string prefix",(function AST_PrefixedTemplateString(e){if(e){this.template_string=e.template_string;this.prefix=e.prefix;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A templatestring with a prefix, such as String.raw`foobarbaz`",$propdoc:{template_string:"[AST_TemplateString] The template string",prefix:"[AST_Node] The prefix, which will get called."},_walk:function(e){return e._visit(this,(function(){this.prefix._walk(e);this.template_string._walk(e)}))},_children_backwards(e){e(this.template_string);e(this.prefix)}});var de=DEFNODE("TemplateString","segments",(function AST_TemplateString(e){if(e){this.segments=e.segments;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A template string literal",$propdoc:{segments:"[AST_Node*] One or more segments, starting with AST_TemplateSegment. AST_Node may follow AST_TemplateSegment, but each AST_Node must be followed by AST_TemplateSegment."},_walk:function(e){return e._visit(this,(function(){this.segments.forEach((function(t){t._walk(e)}))}))},_children_backwards(e){let t=this.segments.length;while(t--)e(this.segments[t])}});var me=DEFNODE("TemplateSegment","value raw",(function AST_TemplateSegment(e){if(e){this.value=e.value;this.raw=e.raw;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A segment of a template string literal",$propdoc:{value:"Content of the segment",raw:"Raw source of the segment"}});var he=DEFNODE("Jump",null,(function AST_Jump(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for “jumps” (for now that's `return`, `throw`, `break` and `continue`)"},U);var Ee=DEFNODE("Exit","value",(function AST_Exit(e){if(e){this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for “exits” (`return` and `throw`)",$propdoc:{value:"[AST_Node?] the value returned or thrown by this statement; could be null for AST_Return"},_walk:function(e){return e._visit(this,this.value&&function(){this.value._walk(e)})},_children_backwards(e){if(this.value)e(this.value)}},he);var ge=DEFNODE("Return",null,(function AST_Return(e){if(e){this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `return` statement"},Ee);var ve=DEFNODE("Throw",null,(function AST_Throw(e){if(e){this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `throw` statement"},Ee);var De=DEFNODE("LoopControl","label",(function AST_LoopControl(e){if(e){this.label=e.label;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for loop control statements (`break` and `continue`)",$propdoc:{label:"[AST_LabelRef?] the label, or null if none"},_walk:function(e){return e._visit(this,this.label&&function(){this.label._walk(e)})},_children_backwards(e){if(this.label)e(this.label)}},he);var be=DEFNODE("Break",null,(function AST_Break(e){if(e){this.label=e.label;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `break` statement"},De);var ye=DEFNODE("Continue",null,(function AST_Continue(e){if(e){this.label=e.label;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `continue` statement"},De);var Se=DEFNODE("Await","expression",(function AST_Await(e){if(e){this.expression=e.expression;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An `await` statement",$propdoc:{expression:"[AST_Node] the mandatory expression being awaited"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))},_children_backwards(e){e(this.expression)}});var Ae=DEFNODE("Yield","expression is_star",(function AST_Yield(e){if(e){this.expression=e.expression;this.is_star=e.is_star;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `yield` statement",$propdoc:{expression:"[AST_Node?] the value returned or thrown by this statement; could be null (representing undefined) but only when is_star is set to false",is_star:"[Boolean] Whether this is a yield or yield* statement"},_walk:function(e){return e._visit(this,this.expression&&function(){this.expression._walk(e)})},_children_backwards(e){if(this.expression)e(this.expression)}});var Te=DEFNODE("If","condition alternative",(function AST_If(e){if(e){this.condition=e.condition;this.alternative=e.alternative;this.body=e.body;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `if` statement",$propdoc:{condition:"[AST_Node] the `if` condition",alternative:"[AST_Statement?] the `else` part, or null if not present"},_walk:function(e){return e._visit(this,(function(){this.condition._walk(e);this.body._walk(e);if(this.alternative)this.alternative._walk(e)}))},_children_backwards(e){if(this.alternative){e(this.alternative)}e(this.body);e(this.condition)}},Y);var ke=DEFNODE("Switch","expression",(function AST_Switch(e){if(e){this.expression=e.expression;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `switch` statement",$propdoc:{expression:"[AST_Node] the `switch` “discriminant”"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e);walk_body(this,e)}))},_children_backwards(e){let t=this.body.length;while(t--)e(this.body[t]);e(this.expression)}},X);var Ce=DEFNODE("SwitchBranch",null,(function AST_SwitchBranch(e){if(e){this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for `switch` branches"},X);var Re=DEFNODE("Default",null,(function AST_Default(e){if(e){this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `default` switch branch"},Ce);var Fe=DEFNODE("Case","expression",(function AST_Case(e){if(e){this.expression=e.expression;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `case` switch branch",$propdoc:{expression:"[AST_Node] the `case` expression"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e);walk_body(this,e)}))},_children_backwards(e){let t=this.body.length;while(t--)e(this.body[t]);e(this.expression)}},Ce);var Oe=DEFNODE("Try","bcatch bfinally",(function AST_Try(e){if(e){this.bcatch=e.bcatch;this.bfinally=e.bfinally;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `try` statement",$propdoc:{bcatch:"[AST_Catch?] the catch block, or null if not present",bfinally:"[AST_Finally?] the finally block, or null if not present"},_walk:function(e){return e._visit(this,(function(){walk_body(this,e);if(this.bcatch)this.bcatch._walk(e);if(this.bfinally)this.bfinally._walk(e)}))},_children_backwards(e){if(this.bfinally)e(this.bfinally);if(this.bcatch)e(this.bcatch);let t=this.body.length;while(t--)e(this.body[t])}},X);var xe=DEFNODE("Catch","argname",(function AST_Catch(e){if(e){this.argname=e.argname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `catch` node; only makes sense as part of a `try` statement",$propdoc:{argname:"[AST_SymbolCatch|AST_Destructuring|AST_Expansion|AST_DefaultAssign] symbol for the exception"},_walk:function(e){return e._visit(this,(function(){if(this.argname)this.argname._walk(e);walk_body(this,e)}))},_children_backwards(e){let t=this.body.length;while(t--)e(this.body[t]);if(this.argname)e(this.argname)}},X);var Me=DEFNODE("Finally",null,(function AST_Finally(e){if(e){this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `finally` node; only makes sense as part of a `try` statement"},X);var we=DEFNODE("Definitions","definitions",(function AST_Definitions(e){if(e){this.definitions=e.definitions;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for `var` or `const` nodes (variable declarations/initializations)",$propdoc:{definitions:"[AST_VarDef*] array of variable definitions"},_walk:function(e){return e._visit(this,(function(){var t=this.definitions;for(var n=0,i=t.length;n<i;n++){t[n]._walk(e)}}))},_children_backwards(e){let t=this.definitions.length;while(t--)e(this.definitions[t])}},U);var Ne=DEFNODE("Var",null,(function AST_Var(e){if(e){this.definitions=e.definitions;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `var` statement"},we);var Ie=DEFNODE("Let",null,(function AST_Let(e){if(e){this.definitions=e.definitions;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `let` statement"},we);var Pe=DEFNODE("Const",null,(function AST_Const(e){if(e){this.definitions=e.definitions;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A `const` statement"},we);var Be=DEFNODE("VarDef","name value",(function AST_VarDef(e){if(e){this.name=e.name;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A variable declaration; only appears in a AST_Definitions node",$propdoc:{name:"[AST_Destructuring|AST_SymbolConst|AST_SymbolLet|AST_SymbolVar] name of the variable",value:"[AST_Node?] initializer, or null of there's no initializer"},_walk:function(e){return e._visit(this,(function(){this.name._walk(e);if(this.value)this.value._walk(e)}))},_children_backwards(e){if(this.value)e(this.value);e(this.name)}});var Le=DEFNODE("NameMapping","foreign_name name",(function AST_NameMapping(e){if(e){this.foreign_name=e.foreign_name;this.name=e.name;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The part of the export/import statement that declare names from a module.",$propdoc:{foreign_name:"[AST_SymbolExportForeign|AST_SymbolImportForeign] The name being exported/imported (as specified in the module)",name:"[AST_SymbolExport|AST_SymbolImport] The name as it is visible to this module."},_walk:function(e){return e._visit(this,(function(){this.foreign_name._walk(e);this.name._walk(e)}))},_children_backwards(e){e(this.name);e(this.foreign_name)}});var Ve=DEFNODE("Import","imported_name imported_names module_name assert_clause",(function AST_Import(e){if(e){this.imported_name=e.imported_name;this.imported_names=e.imported_names;this.module_name=e.module_name;this.assert_clause=e.assert_clause;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An `import` statement",$propdoc:{imported_name:"[AST_SymbolImport] The name of the variable holding the module's default export.",imported_names:"[AST_NameMapping*] The names of non-default imported variables",module_name:"[AST_String] String literal describing where this module came from",assert_clause:"[AST_Object?] The import assertion"},_walk:function(e){return e._visit(this,(function(){if(this.imported_name){this.imported_name._walk(e)}if(this.imported_names){this.imported_names.forEach((function(t){t._walk(e)}))}this.module_name._walk(e)}))},_children_backwards(e){e(this.module_name);if(this.imported_names){let t=this.imported_names.length;while(t--)e(this.imported_names[t])}if(this.imported_name)e(this.imported_name)}});var ze=DEFNODE("ImportMeta",null,(function AST_ImportMeta(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A reference to import.meta"});var Ue=DEFNODE("Export","exported_definition exported_value is_default exported_names module_name assert_clause",(function AST_Export(e){if(e){this.exported_definition=e.exported_definition;this.exported_value=e.exported_value;this.is_default=e.is_default;this.exported_names=e.exported_names;this.module_name=e.module_name;this.assert_clause=e.assert_clause;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An `export` statement",$propdoc:{exported_definition:"[AST_Defun|AST_Definitions|AST_DefClass?] An exported definition",exported_value:"[AST_Node?] An exported value",exported_names:"[AST_NameMapping*?] List of exported names",module_name:"[AST_String?] Name of the file to load exports from",is_default:"[Boolean] Whether this is the default exported value of this module",assert_clause:"[AST_Object?] The import assertion"},_walk:function(e){return e._visit(this,(function(){if(this.exported_definition){this.exported_definition._walk(e)}if(this.exported_value){this.exported_value._walk(e)}if(this.exported_names){this.exported_names.forEach((function(t){t._walk(e)}))}if(this.module_name){this.module_name._walk(e)}}))},_children_backwards(e){if(this.module_name)e(this.module_name);if(this.exported_names){let t=this.exported_names.length;while(t--)e(this.exported_names[t])}if(this.exported_value)e(this.exported_value);if(this.exported_definition)e(this.exported_definition)}},U);var Ke=DEFNODE("Call","expression args optional _annotations",(function AST_Call(e){if(e){this.expression=e.expression;this.args=e.args;this.optional=e.optional;this._annotations=e._annotations;this.start=e.start;this.end=e.end;this.initialize()}this.flags=0}),{$documentation:"A function call expression",$propdoc:{expression:"[AST_Node] expression to invoke as function",args:"[AST_Node*] array of arguments",optional:"[boolean] whether this is an optional call (IE ?.() )",_annotations:"[number] bitfield containing information about the call"},initialize(){if(this._annotations==null)this._annotations=0},_walk(e){return e._visit(this,(function(){var t=this.args;for(var n=0,i=t.length;n<i;n++){t[n]._walk(e)}this.expression._walk(e)}))},_children_backwards(e){let t=this.args.length;while(t--)e(this.args[t]);e(this.expression)}});var Ge=DEFNODE("New",null,(function AST_New(e){if(e){this.expression=e.expression;this.args=e.args;this.optional=e.optional;this._annotations=e._annotations;this.start=e.start;this.end=e.end;this.initialize()}this.flags=0}),{$documentation:"An object instantiation.  Derives from a function call since it has exactly the same properties"},Ke);var He=DEFNODE("Sequence","expressions",(function AST_Sequence(e){if(e){this.expressions=e.expressions;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A sequence expression (comma-separated expressions)",$propdoc:{expressions:"[AST_Node*] array of expressions (at least two)"},_walk:function(e){return e._visit(this,(function(){this.expressions.forEach((function(t){t._walk(e)}))}))},_children_backwards(e){let t=this.expressions.length;while(t--)e(this.expressions[t])}});var Xe=DEFNODE("PropAccess","expression property optional",(function AST_PropAccess(e){if(e){this.expression=e.expression;this.property=e.property;this.optional=e.optional;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:'Base class for property access expressions, i.e. `a.foo` or `a["foo"]`',$propdoc:{expression:"[AST_Node] the “container” expression",property:"[AST_Node|string] the property to access.  For AST_Dot & AST_DotHash this is always a plain string, while for AST_Sub it's an arbitrary AST_Node",optional:"[boolean] whether this is an optional property access (IE ?.)"}});var We=DEFNODE("Dot","quote",(function AST_Dot(e){if(e){this.quote=e.quote;this.expression=e.expression;this.property=e.property;this.optional=e.optional;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A dotted property access expression",$propdoc:{quote:"[string] the original quote character when transformed from AST_Sub"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))},_children_backwards(e){e(this.expression)}},Xe);var qe=DEFNODE("DotHash","",(function AST_DotHash(e){if(e){this.expression=e.expression;this.property=e.property;this.optional=e.optional;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A dotted property access to a private property",_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))},_children_backwards(e){e(this.expression)}},Xe);var Ye=DEFNODE("Sub",null,(function AST_Sub(e){if(e){this.expression=e.expression;this.property=e.property;this.optional=e.optional;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:'Index-style property access, i.e. `a["foo"]`',_walk:function(e){return e._visit(this,(function(){this.expression._walk(e);this.property._walk(e)}))},_children_backwards(e){e(this.property);e(this.expression)}},Xe);var je=DEFNODE("Chain","expression",(function AST_Chain(e){if(e){this.expression=e.expression;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A chain expression like a?.b?.(c)?.[d]",$propdoc:{expression:"[AST_Call|AST_Dot|AST_DotHash|AST_Sub] chain element."},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))},_children_backwards(e){e(this.expression)}});var $e=DEFNODE("Unary","operator expression",(function AST_Unary(e){if(e){this.operator=e.operator;this.expression=e.expression;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for unary expressions",$propdoc:{operator:"[string] the operator",expression:"[AST_Node] expression that this unary operator applies to"},_walk:function(e){return e._visit(this,(function(){this.expression._walk(e)}))},_children_backwards(e){e(this.expression)}});var Ze=DEFNODE("UnaryPrefix",null,(function AST_UnaryPrefix(e){if(e){this.operator=e.operator;this.expression=e.expression;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Unary prefix expression, i.e. `typeof i` or `++i`"},$e);var Qe=DEFNODE("UnaryPostfix",null,(function AST_UnaryPostfix(e){if(e){this.operator=e.operator;this.expression=e.expression;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Unary postfix expression, i.e. `i++`"},$e);var Je=DEFNODE("Binary","operator left right",(function AST_Binary(e){if(e){this.operator=e.operator;this.left=e.left;this.right=e.right;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Binary expression, i.e. `a + b`",$propdoc:{left:"[AST_Node] left-hand side expression",operator:"[string] the operator",right:"[AST_Node] right-hand side expression"},_walk:function(e){return e._visit(this,(function(){this.left._walk(e);this.right._walk(e)}))},_children_backwards(e){e(this.right);e(this.left)}});var et=DEFNODE("Conditional","condition consequent alternative",(function AST_Conditional(e){if(e){this.condition=e.condition;this.consequent=e.consequent;this.alternative=e.alternative;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Conditional expression using the ternary operator, i.e. `a ? b : c`",$propdoc:{condition:"[AST_Node]",consequent:"[AST_Node]",alternative:"[AST_Node]"},_walk:function(e){return e._visit(this,(function(){this.condition._walk(e);this.consequent._walk(e);this.alternative._walk(e)}))},_children_backwards(e){e(this.alternative);e(this.consequent);e(this.condition)}});var tt=DEFNODE("Assign","logical",(function AST_Assign(e){if(e){this.logical=e.logical;this.operator=e.operator;this.left=e.left;this.right=e.right;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An assignment expression — `a = b + 5`",$propdoc:{logical:"Whether it's a logical assignment"}},Je);var nt=DEFNODE("DefaultAssign",null,(function AST_DefaultAssign(e){if(e){this.operator=e.operator;this.left=e.left;this.right=e.right;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A default assignment expression like in `(a = 3) => a`"},Je);var it=DEFNODE("Array","elements",(function AST_Array(e){if(e){this.elements=e.elements;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An array literal",$propdoc:{elements:"[AST_Node*] array of elements"},_walk:function(e){return e._visit(this,(function(){var t=this.elements;for(var n=0,i=t.length;n<i;n++){t[n]._walk(e)}}))},_children_backwards(e){let t=this.elements.length;while(t--)e(this.elements[t])}});var rt=DEFNODE("Object","properties",(function AST_Object(e){if(e){this.properties=e.properties;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"An object literal",$propdoc:{properties:"[AST_ObjectProperty*] array of properties"},_walk:function(e){return e._visit(this,(function(){var t=this.properties;for(var n=0,i=t.length;n<i;n++){t[n]._walk(e)}}))},_children_backwards(e){let t=this.properties.length;while(t--)e(this.properties[t])}});var ot=DEFNODE("ObjectProperty","key value",(function AST_ObjectProperty(e){if(e){this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for literal object properties",$propdoc:{key:"[string|AST_Node] property name. For ObjectKeyVal this is a string. For getters, setters and computed property this is an AST_Node.",value:"[AST_Node] property value.  For getters and setters this is an AST_Accessor."},_walk:function(e){return e._visit(this,(function(){if(this.key instanceof z)this.key._walk(e);this.value._walk(e)}))},_children_backwards(e){e(this.value);if(this.key instanceof z)e(this.key)}});var at=DEFNODE("ObjectKeyVal","quote",(function AST_ObjectKeyVal(e){if(e){this.quote=e.quote;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A key: value object property",$propdoc:{quote:"[string] the original quote character"},computed_key(){return this.key instanceof z}},ot);var st=DEFNODE("PrivateSetter","static",(function AST_PrivateSetter(e){if(e){this.static=e.static;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$propdoc:{static:"[boolean] whether this is a static private setter"},$documentation:"A private setter property",computed_key(){return false}},ot);var ut=DEFNODE("PrivateGetter","static",(function AST_PrivateGetter(e){if(e){this.static=e.static;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$propdoc:{static:"[boolean] whether this is a static private getter"},$documentation:"A private getter property",computed_key(){return false}},ot);var lt=DEFNODE("ObjectSetter","quote static",(function AST_ObjectSetter(e){if(e){this.quote=e.quote;this.static=e.static;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$propdoc:{quote:"[string|undefined] the original quote character, if any",static:"[boolean] whether this is a static setter (classes only)"},$documentation:"An object setter property",computed_key(){return!(this.key instanceof Ct)}},ot);var ct=DEFNODE("ObjectGetter","quote static",(function AST_ObjectGetter(e){if(e){this.quote=e.quote;this.static=e.static;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$propdoc:{quote:"[string|undefined] the original quote character, if any",static:"[boolean] whether this is a static getter (classes only)"},$documentation:"An object getter property",computed_key(){return!(this.key instanceof Ct)}},ot);var ft=DEFNODE("ConciseMethod","quote static is_generator async",(function AST_ConciseMethod(e){if(e){this.quote=e.quote;this.static=e.static;this.is_generator=e.is_generator;this.async=e.async;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$propdoc:{quote:"[string|undefined] the original quote character, if any",static:"[boolean] is this method static (classes only)",is_generator:"[boolean] is this a generator method",async:"[boolean] is this method async"},$documentation:"An ES6 concise method inside an object or class",computed_key(){return!(this.key instanceof Ct)}},ot);var _t=DEFNODE("PrivateMethod","",(function AST_PrivateMethod(e){if(e){this.quote=e.quote;this.static=e.static;this.is_generator=e.is_generator;this.async=e.async;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A private class method inside a class"},ft);var pt=DEFNODE("Class","name extends properties",(function AST_Class(e){if(e){this.name=e.name;this.extends=e.extends;this.properties=e.properties;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$propdoc:{name:"[AST_SymbolClass|AST_SymbolDefClass?] optional class name.",extends:"[AST_Node]? optional parent class",properties:"[AST_ObjectProperty*] array of properties"},$documentation:"An ES6 class",_walk:function(e){return e._visit(this,(function(){if(this.name){this.name._walk(e)}if(this.extends){this.extends._walk(e)}this.properties.forEach((t=>t._walk(e)))}))},_children_backwards(e){let t=this.properties.length;while(t--)e(this.properties[t]);if(this.extends)e(this.extends);if(this.name)e(this.name)}},re);var dt=DEFNODE("ClassProperty","static quote",(function AST_ClassProperty(e){if(e){this.static=e.static;this.quote=e.quote;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A class property",$propdoc:{static:"[boolean] whether this is a static key",quote:"[string] which quote is being used"},_walk:function(e){return e._visit(this,(function(){if(this.key instanceof z)this.key._walk(e);if(this.value instanceof z)this.value._walk(e)}))},_children_backwards(e){if(this.value instanceof z)e(this.value);if(this.key instanceof z)e(this.key)},computed_key(){return!(this.key instanceof Rt)}},ot);var mt=DEFNODE("ClassPrivateProperty","",(function AST_ClassPrivateProperty(e){if(e){this.static=e.static;this.quote=e.quote;this.key=e.key;this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A class property for a private property"},dt);var ht=DEFNODE("DefClass",null,(function AST_DefClass(e){if(e){this.name=e.name;this.extends=e.extends;this.properties=e.properties;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A class definition"},pt);var Et=DEFNODE("ClassExpression",null,(function AST_ClassExpression(e){if(e){this.name=e.name;this.extends=e.extends;this.properties=e.properties;this.variables=e.variables;this.uses_with=e.uses_with;this.uses_eval=e.uses_eval;this.parent_scope=e.parent_scope;this.enclosed=e.enclosed;this.cname=e.cname;this.body=e.body;this.block_scope=e.block_scope;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A class expression."},pt);var gt=DEFNODE("Symbol","scope name thedef",(function AST_Symbol(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$propdoc:{name:"[string] name of this symbol",scope:"[AST_Scope/S] the current scope (not necessarily the definition scope)",thedef:"[SymbolDef/S] the definition of this symbol"},$documentation:"Base class for all symbols"});var vt=DEFNODE("NewTarget",null,(function AST_NewTarget(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A reference to new.target"});var Dt=DEFNODE("SymbolDeclaration","init",(function AST_SymbolDeclaration(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A declaration symbol (symbol in var/const, function name or argument, symbol in catch)"},gt);var bt=DEFNODE("SymbolVar",null,(function AST_SymbolVar(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol defining a variable"},Dt);var yt=DEFNODE("SymbolBlockDeclaration",null,(function AST_SymbolBlockDeclaration(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for block-scoped declaration symbols"},Dt);var St=DEFNODE("SymbolConst",null,(function AST_SymbolConst(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A constant declaration"},yt);var At=DEFNODE("SymbolLet",null,(function AST_SymbolLet(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A block-scoped `let` declaration"},yt);var Tt=DEFNODE("SymbolFunarg",null,(function AST_SymbolFunarg(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol naming a function argument"},bt);var kt=DEFNODE("SymbolDefun",null,(function AST_SymbolDefun(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol defining a function"},Dt);var Ct=DEFNODE("SymbolMethod",null,(function AST_SymbolMethod(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol in an object defining a method"},gt);var Rt=DEFNODE("SymbolClassProperty",null,(function AST_SymbolClassProperty(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol for a class property"},gt);var Ft=DEFNODE("SymbolLambda",null,(function AST_SymbolLambda(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol naming a function expression"},Dt);var Ot=DEFNODE("SymbolDefClass",null,(function AST_SymbolDefClass(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol naming a class's name in a class declaration. Lexically scoped to its containing scope, and accessible within the class."},yt);var xt=DEFNODE("SymbolClass",null,(function AST_SymbolClass(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol naming a class's name. Lexically scoped to the class."},Dt);var Mt=DEFNODE("SymbolCatch",null,(function AST_SymbolCatch(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol naming the exception in catch"},yt);var wt=DEFNODE("SymbolImport",null,(function AST_SymbolImport(e){if(e){this.init=e.init;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol referring to an imported name"},yt);var Nt=DEFNODE("SymbolImportForeign",null,(function AST_SymbolImportForeign(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A symbol imported from a module, but it is defined in the other module, and its real name is irrelevant for this module's purposes"},gt);var It=DEFNODE("Label","references",(function AST_Label(e){if(e){this.references=e.references;this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end;this.initialize()}this.flags=0}),{$documentation:"Symbol naming a label (declaration)",$propdoc:{references:"[AST_LoopControl*] a list of nodes referring to this label"},initialize:function(){this.references=[];this.thedef=this}},gt);var Pt=DEFNODE("SymbolRef",null,(function AST_SymbolRef(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Reference to some symbol (not definition/declaration)"},gt);var Bt=DEFNODE("SymbolExport",null,(function AST_SymbolExport(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Symbol referring to a name to export"},Pt);var Lt=DEFNODE("SymbolExportForeign",null,(function AST_SymbolExportForeign(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A symbol exported from this module, but it is used in the other module, and its real name is irrelevant for this module's purposes"},gt);var Vt=DEFNODE("LabelRef",null,(function AST_LabelRef(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Reference to a label symbol"},gt);var zt=DEFNODE("This",null,(function AST_This(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The `this` symbol"},gt);var Ut=DEFNODE("Super",null,(function AST_Super(e){if(e){this.scope=e.scope;this.name=e.name;this.thedef=e.thedef;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The `super` symbol"},zt);var Kt=DEFNODE("Constant",null,(function AST_Constant(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for all constants",getValue:function(){return this.value}});var Gt=DEFNODE("String","value quote",(function AST_String(e){if(e){this.value=e.value;this.quote=e.quote;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A string literal",$propdoc:{value:"[string] the contents of this string",quote:"[string] the original quote character"}},Kt);var Ht=DEFNODE("Number","value raw",(function AST_Number(e){if(e){this.value=e.value;this.raw=e.raw;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A number literal",$propdoc:{value:"[number] the numeric value",raw:"[string] numeric value as string"}},Kt);var Xt=DEFNODE("BigInt","value",(function AST_BigInt(e){if(e){this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A big int literal",$propdoc:{value:"[string] big int value"}},Kt);var Wt=DEFNODE("RegExp","value",(function AST_RegExp(e){if(e){this.value=e.value;this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A regexp literal",$propdoc:{value:"[RegExp] the actual regexp"}},Kt);var qt=DEFNODE("Atom",null,(function AST_Atom(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for atoms"},Kt);var Yt=DEFNODE("Null",null,(function AST_Null(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The `null` atom",value:null},qt);var jt=DEFNODE("NaN",null,(function AST_NaN(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The impossible value",value:0/0},qt);var $t=DEFNODE("Undefined",null,(function AST_Undefined(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The `undefined` value",value:function(){}()},qt);var Zt=DEFNODE("Hole",null,(function AST_Hole(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"A hole in an array",value:function(){}()},qt);var Qt=DEFNODE("Infinity",null,(function AST_Infinity(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The `Infinity` value",value:1/0},qt);var Jt=DEFNODE("Boolean",null,(function AST_Boolean(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"Base class for booleans"},qt);var en=DEFNODE("False",null,(function AST_False(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The `false` atom",value:false},Jt);var tn=DEFNODE("True",null,(function AST_True(e){if(e){this.start=e.start;this.end=e.end}this.flags=0}),{$documentation:"The `true` atom",value:true},Jt);function walk(e,t,n=[e]){const i=n.push.bind(n);while(n.length){const e=n.pop();const r=t(e,n);if(r){if(r===nn)return true;continue}e._children_backwards(i)}return false}function walk_parent(e,t,n){const i=[e];const r=i.push.bind(i);const o=n?n.slice():[];const a=[];let s;const u={parent:(e=0)=>{if(e===-1){return s}if(n&&e>=o.length){e-=o.length;return n[n.length-(e+1)]}return o[o.length-(1+e)]}};while(i.length){s=i.pop();while(a.length&&i.length==a[a.length-1]){o.pop();a.pop()}const e=t(s,u);if(e){if(e===nn)return true;continue}const n=i.length;s._children_backwards(r);if(i.length>n){o.push(s);a.push(n-1)}}return false}const nn=Symbol("abort walk");class TreeWalker{constructor(e){this.visit=e;this.stack=[];this.directives=Object.create(null)}_visit(e,t){this.push(e);var n=this.visit(e,t?function(){t.call(e)}:noop);if(!n&&t){t.call(e)}this.pop();return n}parent(e){return this.stack[this.stack.length-2-(e||0)]}push(e){if(e instanceof se){this.directives=Object.create(this.directives)}else if(e instanceof G&&!this.directives[e.value]){this.directives[e.value]=e}else if(e instanceof pt){this.directives=Object.create(this.directives);if(!this.directives["use strict"]){this.directives["use strict"]=e}}this.stack.push(e)}pop(){var e=this.stack.pop();if(e instanceof se||e instanceof pt){this.directives=Object.getPrototypeOf(this.directives)}}self(){return this.stack[this.stack.length-1]}find_parent(e){var t=this.stack;for(var n=t.length;--n>=0;){var i=t[n];if(i instanceof e)return i}}find_scope(){for(let e=0;;e++){const t=this.parent(e);if(t instanceof oe)return t;if(t instanceof se)return t;if(t.block_scope)return t.block_scope}}has_directive(e){var t=this.directives[e];if(t)return t;var n=this.stack[this.stack.length-1];if(n instanceof re&&n.body){for(var i=0;i<n.body.length;++i){var r=n.body[i];if(!(r instanceof G))break;if(r.value==e)return r}}}loopcontrol_target(e){var t=this.stack;if(e.label)for(var n=t.length;--n>=0;){var i=t[n];if(i instanceof j&&i.label.name==e.label.name)return i.body}else for(var n=t.length;--n>=0;){var i=t[n];if(i instanceof $||e instanceof be&&i instanceof ke)return i}}}class TreeTransformer extends TreeWalker{constructor(e,t){super();this.before=e;this.after=t}}const rn=1;const on=2;const an=4;function def_transform(e,t){e.DEFMETHOD("transform",(function(e,n){let i=undefined;e.push(this);if(e.before)i=e.before(this,t,n);if(i===undefined){i=this;t(i,e);if(e.after){const t=e.after(i,n);if(t!==undefined)i=t}}e.pop();return i}))}function do_list(e,t){return i(e,(function(e){return e.transform(t,true)}))}def_transform(z,noop);def_transform(j,(function(e,t){e.label=e.label.transform(t);e.body=e.body.transform(t)}));def_transform(H,(function(e,t){e.body=e.body.transform(t)}));def_transform(X,(function(e,t){e.body=do_list(e.body,t)}));def_transform(Q,(function(e,t){e.body=e.body.transform(t);e.condition=e.condition.transform(t)}));def_transform(J,(function(e,t){e.condition=e.condition.transform(t);e.body=e.body.transform(t)}));def_transform(ee,(function(e,t){if(e.init)e.init=e.init.transform(t);if(e.condition)e.condition=e.condition.transform(t);if(e.step)e.step=e.step.transform(t);e.body=e.body.transform(t)}));def_transform(te,(function(e,t){e.init=e.init.transform(t);e.object=e.object.transform(t);e.body=e.body.transform(t)}));def_transform(ie,(function(e,t){e.expression=e.expression.transform(t);e.body=e.body.transform(t)}));def_transform(Ee,(function(e,t){if(e.value)e.value=e.value.transform(t)}));def_transform(De,(function(e,t){if(e.label)e.label=e.label.transform(t)}));def_transform(Te,(function(e,t){e.condition=e.condition.transform(t);e.body=e.body.transform(t);if(e.alternative)e.alternative=e.alternative.transform(t)}));def_transform(ke,(function(e,t){e.expression=e.expression.transform(t);e.body=do_list(e.body,t)}));def_transform(Fe,(function(e,t){e.expression=e.expression.transform(t);e.body=do_list(e.body,t)}));def_transform(Oe,(function(e,t){e.body=do_list(e.body,t);if(e.bcatch)e.bcatch=e.bcatch.transform(t);if(e.bfinally)e.bfinally=e.bfinally.transform(t)}));def_transform(xe,(function(e,t){if(e.argname)e.argname=e.argname.transform(t);e.body=do_list(e.body,t)}));def_transform(we,(function(e,t){e.definitions=do_list(e.definitions,t)}));def_transform(Be,(function(e,t){e.name=e.name.transform(t);if(e.value)e.value=e.value.transform(t)}));def_transform(_e,(function(e,t){e.names=do_list(e.names,t)}));def_transform(se,(function(e,t){if(e.name)e.name=e.name.transform(t);e.argnames=do_list(e.argnames,t);if(e.body instanceof z){e.body=e.body.transform(t)}else{e.body=do_list(e.body,t)}}));def_transform(Ke,(function(e,t){e.expression=e.expression.transform(t);e.args=do_list(e.args,t)}));def_transform(He,(function(e,t){const n=do_list(e.expressions,t);e.expressions=n.length?n:[new Ht({value:0})]}));def_transform(Xe,(function(e,t){e.expression=e.expression.transform(t)}));def_transform(Ye,(function(e,t){e.expression=e.expression.transform(t);e.property=e.property.transform(t)}));def_transform(je,(function(e,t){e.expression=e.expression.transform(t)}));def_transform(Ae,(function(e,t){if(e.expression)e.expression=e.expression.transform(t)}));def_transform(Se,(function(e,t){e.expression=e.expression.transform(t)}));def_transform($e,(function(e,t){e.expression=e.expression.transform(t)}));def_transform(Je,(function(e,t){e.left=e.left.transform(t);e.right=e.right.transform(t)}));def_transform(et,(function(e,t){e.condition=e.condition.transform(t);e.consequent=e.consequent.transform(t);e.alternative=e.alternative.transform(t)}));def_transform(it,(function(e,t){e.elements=do_list(e.elements,t)}));def_transform(rt,(function(e,t){e.properties=do_list(e.properties,t)}));def_transform(ot,(function(e,t){if(e.key instanceof z){e.key=e.key.transform(t)}if(e.value)e.value=e.value.transform(t)}));def_transform(pt,(function(e,t){if(e.name)e.name=e.name.transform(t);if(e.extends)e.extends=e.extends.transform(t);e.properties=do_list(e.properties,t)}));def_transform(ae,(function(e,t){e.expression=e.expression.transform(t)}));def_transform(Le,(function(e,t){e.foreign_name=e.foreign_name.transform(t);e.name=e.name.transform(t)}));def_transform(Ve,(function(e,t){if(e.imported_name)e.imported_name=e.imported_name.transform(t);if(e.imported_names)do_list(e.imported_names,t);e.module_name=e.module_name.transform(t)}));def_transform(Ue,(function(e,t){if(e.exported_definition)e.exported_definition=e.exported_definition.transform(t);if(e.exported_value)e.exported_value=e.exported_value.transform(t);if(e.exported_names)do_list(e.exported_names,t);if(e.module_name)e.module_name=e.module_name.transform(t)}));def_transform(de,(function(e,t){e.segments=do_list(e.segments,t)}));def_transform(pe,(function(e,t){e.prefix=e.prefix.transform(t);e.template_string=e.template_string.transform(t)}));(function(){var normalize_directives=function(e){var t=true;for(var n=0;n<e.length;n++){if(t&&e[n]instanceof U&&e[n].body instanceof Gt){e[n]=new G({start:e[n].start,end:e[n].end,value:e[n].body.value})}else if(t&&!(e[n]instanceof U&&e[n].body instanceof Gt)){t=false}}return e};const assert_clause_from_moz=e=>{if(e&&e.length>0){return new rt({start:my_start_token(e),end:my_end_token(e),properties:e.map((e=>new at({start:my_start_token(e),end:my_end_token(e),key:e.key.name||e.key.value,value:from_moz(e.value)})))})}return null};var e={Program:function(e){return new oe({start:my_start_token(e),end:my_end_token(e),body:normalize_directives(e.body.map(from_moz))})},ArrayPattern:function(e){return new _e({start:my_start_token(e),end:my_end_token(e),names:e.elements.map((function(e){if(e===null){return new Zt}return from_moz(e)})),is_array:true})},ObjectPattern:function(e){return new _e({start:my_start_token(e),end:my_end_token(e),names:e.properties.map(from_moz),is_array:false})},AssignmentPattern:function(e){return new nt({start:my_start_token(e),end:my_end_token(e),left:from_moz(e.left),operator:"=",right:from_moz(e.right)})},SpreadElement:function(e){return new ae({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.argument)})},RestElement:function(e){return new ae({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.argument)})},TemplateElement:function(e){return new me({start:my_start_token(e),end:my_end_token(e),value:e.value.cooked,raw:e.value.raw})},TemplateLiteral:function(e){var t=[];for(var n=0;n<e.quasis.length;n++){t.push(from_moz(e.quasis[n]));if(e.expressions[n]){t.push(from_moz(e.expressions[n]))}}return new de({start:my_start_token(e),end:my_end_token(e),segments:t})},TaggedTemplateExpression:function(e){return new pe({start:my_start_token(e),end:my_end_token(e),template_string:from_moz(e.quasi),prefix:from_moz(e.tag)})},FunctionDeclaration:function(e){return new fe({start:my_start_token(e),end:my_end_token(e),name:from_moz(e.id),argnames:e.params.map(from_moz),is_generator:e.generator,async:e.async,body:normalize_directives(from_moz(e.body).body)})},FunctionExpression:function(e){return new le({start:my_start_token(e),end:my_end_token(e),name:from_moz(e.id),argnames:e.params.map(from_moz),is_generator:e.generator,async:e.async,body:normalize_directives(from_moz(e.body).body)})},ArrowFunctionExpression:function(e){const t=e.body.type==="BlockStatement"?from_moz(e.body).body:[make_node(ge,{},{value:from_moz(e.body)})];return new ce({start:my_start_token(e),end:my_end_token(e),argnames:e.params.map(from_moz),body:t,async:e.async})},ExpressionStatement:function(e){return new H({start:my_start_token(e),end:my_end_token(e),body:from_moz(e.expression)})},TryStatement:function(e){var t=e.handlers||[e.handler];if(t.length>1||e.guardedHandlers&&e.guardedHandlers.length){throw new Error("Multiple catch clauses are not supported.")}return new Oe({start:my_start_token(e),end:my_end_token(e),body:from_moz(e.block).body,bcatch:from_moz(t[0]),bfinally:e.finalizer?new Me(from_moz(e.finalizer)):null})},Property:function(e){var t=e.key;var n={start:my_start_token(t||e.value),end:my_end_token(e.value),key:t.type=="Identifier"?t.name:t.value,value:from_moz(e.value)};if(e.computed){n.key=from_moz(e.key)}if(e.method){n.is_generator=e.value.generator;n.async=e.value.async;if(!e.computed){n.key=new Ct({name:n.key})}else{n.key=from_moz(e.key)}return new ft(n)}if(e.kind=="init"){if(t.type!="Identifier"&&t.type!="Literal"){n.key=from_moz(t)}return new at(n)}if(typeof n.key==="string"||typeof n.key==="number"){n.key=new Ct({name:n.key})}n.value=new ue(n.value);if(e.kind=="get")return new ct(n);if(e.kind=="set")return new lt(n);if(e.kind=="method"){n.async=e.value.async;n.is_generator=e.value.generator;n.quote=e.computed?'"':null;return new ft(n)}},MethodDefinition:function(e){var t={start:my_start_token(e),end:my_end_token(e),key:e.computed?from_moz(e.key):new Ct({name:e.key.name||e.key.value}),value:from_moz(e.value),static:e.static};if(e.kind=="get"){return new ct(t)}if(e.kind=="set"){return new lt(t)}t.is_generator=e.value.generator;t.async=e.value.async;return new ft(t)},FieldDefinition:function(e){let t;if(e.computed){t=from_moz(e.key)}else{if(e.key.type!=="Identifier")throw new Error("Non-Identifier key in FieldDefinition");t=from_moz(e.key)}return new dt({start:my_start_token(e),end:my_end_token(e),key:t,value:from_moz(e.value),static:e.static})},PropertyDefinition:function(e){let t;if(e.computed){t=from_moz(e.key)}else{if(e.key.type!=="Identifier")throw new Error("Non-Identifier key in PropertyDefinition");t=from_moz(e.key)}return new dt({start:my_start_token(e),end:my_end_token(e),key:t,value:from_moz(e.value),static:e.static})},ArrayExpression:function(e){return new it({start:my_start_token(e),end:my_end_token(e),elements:e.elements.map((function(e){return e===null?new Zt:from_moz(e)}))})},ObjectExpression:function(e){return new rt({start:my_start_token(e),end:my_end_token(e),properties:e.properties.map((function(e){if(e.type==="SpreadElement"){return from_moz(e)}e.type="Property";return from_moz(e)}))})},SequenceExpression:function(e){return new He({start:my_start_token(e),end:my_end_token(e),expressions:e.expressions.map(from_moz)})},MemberExpression:function(e){return new(e.computed?Ye:We)({start:my_start_token(e),end:my_end_token(e),property:e.computed?from_moz(e.property):e.property.name,expression:from_moz(e.object),optional:e.optional||false})},ChainExpression:function(e){return new je({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.expression)})},SwitchCase:function(e){return new(e.test?Fe:Re)({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.test),body:e.consequent.map(from_moz)})},VariableDeclaration:function(e){return new(e.kind==="const"?Pe:e.kind==="let"?Ie:Ne)({start:my_start_token(e),end:my_end_token(e),definitions:e.declarations.map(from_moz)})},ImportDeclaration:function(e){var t=null;var n=null;e.specifiers.forEach((function(e){if(e.type==="ImportSpecifier"){if(!n){n=[]}n.push(new Le({start:my_start_token(e),end:my_end_token(e),foreign_name:from_moz(e.imported),name:from_moz(e.local)}))}else if(e.type==="ImportDefaultSpecifier"){t=from_moz(e.local)}else if(e.type==="ImportNamespaceSpecifier"){if(!n){n=[]}n.push(new Le({start:my_start_token(e),end:my_end_token(e),foreign_name:new Nt({name:"*"}),name:from_moz(e.local)}))}}));return new Ve({start:my_start_token(e),end:my_end_token(e),imported_name:t,imported_names:n,module_name:from_moz(e.source),assert_clause:assert_clause_from_moz(e.assertions)})},ExportAllDeclaration:function(e){return new Ue({start:my_start_token(e),end:my_end_token(e),exported_names:[new Le({name:new Lt({name:"*"}),foreign_name:new Lt({name:"*"})})],module_name:from_moz(e.source),assert_clause:assert_clause_from_moz(e.assertions)})},ExportNamedDeclaration:function(e){return new Ue({start:my_start_token(e),end:my_end_token(e),exported_definition:from_moz(e.declaration),exported_names:e.specifiers&&e.specifiers.length?e.specifiers.map((function(e){return new Le({foreign_name:from_moz(e.exported),name:from_moz(e.local)})})):null,module_name:from_moz(e.source),assert_clause:assert_clause_from_moz(e.assertions)})},ExportDefaultDeclaration:function(e){return new Ue({start:my_start_token(e),end:my_end_token(e),exported_value:from_moz(e.declaration),is_default:true})},Literal:function(e){var t=e.value,n={start:my_start_token(e),end:my_end_token(e)};var i=e.regex;if(i&&i.pattern){n.value={source:i.pattern,flags:i.flags};return new Wt(n)}else if(i){const i=e.raw||t;const r=i.match(/^\/(.*)\/(\w*)$/);if(!r)throw new Error("Invalid regex source "+i);const[o,a,s]=r;n.value={source:a,flags:s};return new Wt(n)}if(t===null)return new Yt(n);switch(typeof t){case"string":n.value=t;return new Gt(n);case"number":n.value=t;n.raw=e.raw||t.toString();return new Ht(n);case"boolean":return new(t?tn:en)(n)}},MetaProperty:function(e){if(e.meta.name==="new"&&e.property.name==="target"){return new vt({start:my_start_token(e),end:my_end_token(e)})}else if(e.meta.name==="import"&&e.property.name==="meta"){return new ze({start:my_start_token(e),end:my_end_token(e)})}},Identifier:function(e){var n=t[t.length-2];return new(n.type=="LabeledStatement"?It:n.type=="VariableDeclarator"&&n.id===e?n.kind=="const"?St:n.kind=="let"?At:bt:/Import.*Specifier/.test(n.type)?n.local===e?wt:Nt:n.type=="ExportSpecifier"?n.local===e?Bt:Lt:n.type=="FunctionExpression"?n.id===e?Ft:Tt:n.type=="FunctionDeclaration"?n.id===e?kt:Tt:n.type=="ArrowFunctionExpression"?n.params.includes(e)?Tt:Pt:n.type=="ClassExpression"?n.id===e?xt:Pt:n.type=="Property"?n.key===e&&n.computed||n.value===e?Pt:Ct:n.type=="PropertyDefinition"||n.type==="FieldDefinition"?n.key===e&&n.computed||n.value===e?Pt:Rt:n.type=="ClassDeclaration"?n.id===e?Ot:Pt:n.type=="MethodDefinition"?n.computed?Pt:Ct:n.type=="CatchClause"?Mt:n.type=="BreakStatement"||n.type=="ContinueStatement"?Vt:Pt)({start:my_start_token(e),end:my_end_token(e),name:e.name})},BigIntLiteral(e){return new Xt({start:my_start_token(e),end:my_end_token(e),value:e.value})},EmptyStatement:function(e){return new q({start:my_start_token(e),end:my_end_token(e)})},BlockStatement:function(e){return new W({start:my_start_token(e),end:my_end_token(e),body:e.body.map(from_moz)})},IfStatement:function(e){return new Te({start:my_start_token(e),end:my_end_token(e),condition:from_moz(e.test),body:from_moz(e.consequent),alternative:from_moz(e.alternate)})},LabeledStatement:function(e){return new j({start:my_start_token(e),end:my_end_token(e),label:from_moz(e.label),body:from_moz(e.body)})},BreakStatement:function(e){return new be({start:my_start_token(e),end:my_end_token(e),label:from_moz(e.label)})},ContinueStatement:function(e){return new ye({start:my_start_token(e),end:my_end_token(e),label:from_moz(e.label)})},WithStatement:function(e){return new ie({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.object),body:from_moz(e.body)})},SwitchStatement:function(e){return new ke({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.discriminant),body:e.cases.map(from_moz)})},ReturnStatement:function(e){return new ge({start:my_start_token(e),end:my_end_token(e),value:from_moz(e.argument)})},ThrowStatement:function(e){return new ve({start:my_start_token(e),end:my_end_token(e),value:from_moz(e.argument)})},WhileStatement:function(e){return new J({start:my_start_token(e),end:my_end_token(e),condition:from_moz(e.test),body:from_moz(e.body)})},DoWhileStatement:function(e){return new Q({start:my_start_token(e),end:my_end_token(e),condition:from_moz(e.test),body:from_moz(e.body)})},ForStatement:function(e){return new ee({start:my_start_token(e),end:my_end_token(e),init:from_moz(e.init),condition:from_moz(e.test),step:from_moz(e.update),body:from_moz(e.body)})},ForInStatement:function(e){return new te({start:my_start_token(e),end:my_end_token(e),init:from_moz(e.left),object:from_moz(e.right),body:from_moz(e.body)})},ForOfStatement:function(e){return new ne({start:my_start_token(e),end:my_end_token(e),init:from_moz(e.left),object:from_moz(e.right),body:from_moz(e.body),await:e.await})},AwaitExpression:function(e){return new Se({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.argument)})},YieldExpression:function(e){return new Ae({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.argument),is_star:e.delegate})},DebuggerStatement:function(e){return new K({start:my_start_token(e),end:my_end_token(e)})},VariableDeclarator:function(e){return new Be({start:my_start_token(e),end:my_end_token(e),name:from_moz(e.id),value:from_moz(e.init)})},CatchClause:function(e){return new xe({start:my_start_token(e),end:my_end_token(e),argname:from_moz(e.param),body:from_moz(e.body).body})},ThisExpression:function(e){return new zt({start:my_start_token(e),end:my_end_token(e)})},Super:function(e){return new Ut({start:my_start_token(e),end:my_end_token(e)})},BinaryExpression:function(e){return new Je({start:my_start_token(e),end:my_end_token(e),operator:e.operator,left:from_moz(e.left),right:from_moz(e.right)})},LogicalExpression:function(e){return new Je({start:my_start_token(e),end:my_end_token(e),operator:e.operator,left:from_moz(e.left),right:from_moz(e.right)})},AssignmentExpression:function(e){return new tt({start:my_start_token(e),end:my_end_token(e),operator:e.operator,left:from_moz(e.left),right:from_moz(e.right)})},ConditionalExpression:function(e){return new et({start:my_start_token(e),end:my_end_token(e),condition:from_moz(e.test),consequent:from_moz(e.consequent),alternative:from_moz(e.alternate)})},NewExpression:function(e){return new Ge({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.callee),args:e.arguments.map(from_moz)})},CallExpression:function(e){return new Ke({start:my_start_token(e),end:my_end_token(e),expression:from_moz(e.callee),optional:e.optional,args:e.arguments.map(from_moz)})}};e.UpdateExpression=e.UnaryExpression=function To_Moz_Unary(e){var t="prefix"in e?e.prefix:e.type=="UnaryExpression"?true:false;return new(t?Ze:Qe)({start:my_start_token(e),end:my_end_token(e),operator:e.operator,expression:from_moz(e.argument)})};e.ClassDeclaration=e.ClassExpression=function From_Moz_Class(e){return new(e.type==="ClassDeclaration"?ht:Et)({start:my_start_token(e),end:my_end_token(e),name:from_moz(e.id),extends:from_moz(e.superClass),properties:e.body.body.map(from_moz)})};def_to_moz(q,(function To_Moz_EmptyStatement(){return{type:"EmptyStatement"}}));def_to_moz(W,(function To_Moz_BlockStatement(e){return{type:"BlockStatement",body:e.body.map(to_moz)}}));def_to_moz(Te,(function To_Moz_IfStatement(e){return{type:"IfStatement",test:to_moz(e.condition),consequent:to_moz(e.body),alternate:to_moz(e.alternative)}}));def_to_moz(j,(function To_Moz_LabeledStatement(e){return{type:"LabeledStatement",label:to_moz(e.label),body:to_moz(e.body)}}));def_to_moz(be,(function To_Moz_BreakStatement(e){return{type:"BreakStatement",label:to_moz(e.label)}}));def_to_moz(ye,(function To_Moz_ContinueStatement(e){return{type:"ContinueStatement",label:to_moz(e.label)}}));def_to_moz(ie,(function To_Moz_WithStatement(e){return{type:"WithStatement",object:to_moz(e.expression),body:to_moz(e.body)}}));def_to_moz(ke,(function To_Moz_SwitchStatement(e){return{type:"SwitchStatement",discriminant:to_moz(e.expression),cases:e.body.map(to_moz)}}));def_to_moz(ge,(function To_Moz_ReturnStatement(e){return{type:"ReturnStatement",argument:to_moz(e.value)}}));def_to_moz(ve,(function To_Moz_ThrowStatement(e){return{type:"ThrowStatement",argument:to_moz(e.value)}}));def_to_moz(J,(function To_Moz_WhileStatement(e){return{type:"WhileStatement",test:to_moz(e.condition),body:to_moz(e.body)}}));def_to_moz(Q,(function To_Moz_DoWhileStatement(e){return{type:"DoWhileStatement",test:to_moz(e.condition),body:to_moz(e.body)}}));def_to_moz(ee,(function To_Moz_ForStatement(e){return{type:"ForStatement",init:to_moz(e.init),test:to_moz(e.condition),update:to_moz(e.step),body:to_moz(e.body)}}));def_to_moz(te,(function To_Moz_ForInStatement(e){return{type:"ForInStatement",left:to_moz(e.init),right:to_moz(e.object),body:to_moz(e.body)}}));def_to_moz(ne,(function To_Moz_ForOfStatement(e){return{type:"ForOfStatement",left:to_moz(e.init),right:to_moz(e.object),body:to_moz(e.body),await:e.await}}));def_to_moz(Se,(function To_Moz_AwaitExpression(e){return{type:"AwaitExpression",argument:to_moz(e.expression)}}));def_to_moz(Ae,(function To_Moz_YieldExpression(e){return{type:"YieldExpression",argument:to_moz(e.expression),delegate:e.is_star}}));def_to_moz(K,(function To_Moz_DebuggerStatement(){return{type:"DebuggerStatement"}}));def_to_moz(Be,(function To_Moz_VariableDeclarator(e){return{type:"VariableDeclarator",id:to_moz(e.name),init:to_moz(e.value)}}));def_to_moz(xe,(function To_Moz_CatchClause(e){return{type:"CatchClause",param:to_moz(e.argname),body:to_moz_block(e)}}));def_to_moz(zt,(function To_Moz_ThisExpression(){return{type:"ThisExpression"}}));def_to_moz(Ut,(function To_Moz_Super(){return{type:"Super"}}));def_to_moz(Je,(function To_Moz_BinaryExpression(e){return{type:"BinaryExpression",operator:e.operator,left:to_moz(e.left),right:to_moz(e.right)}}));def_to_moz(Je,(function To_Moz_LogicalExpression(e){return{type:"LogicalExpression",operator:e.operator,left:to_moz(e.left),right:to_moz(e.right)}}));def_to_moz(tt,(function To_Moz_AssignmentExpression(e){return{type:"AssignmentExpression",operator:e.operator,left:to_moz(e.left),right:to_moz(e.right)}}));def_to_moz(et,(function To_Moz_ConditionalExpression(e){return{type:"ConditionalExpression",test:to_moz(e.condition),consequent:to_moz(e.consequent),alternate:to_moz(e.alternative)}}));def_to_moz(Ge,(function To_Moz_NewExpression(e){return{type:"NewExpression",callee:to_moz(e.expression),arguments:e.args.map(to_moz)}}));def_to_moz(Ke,(function To_Moz_CallExpression(e){return{type:"CallExpression",callee:to_moz(e.expression),optional:e.optional,arguments:e.args.map(to_moz)}}));def_to_moz(oe,(function To_Moz_Program(e){return to_moz_scope("Program",e)}));def_to_moz(ae,(function To_Moz_Spread(e){return{type:to_moz_in_destructuring()?"RestElement":"SpreadElement",argument:to_moz(e.expression)}}));def_to_moz(pe,(function To_Moz_TaggedTemplateExpression(e){return{type:"TaggedTemplateExpression",tag:to_moz(e.prefix),quasi:to_moz(e.template_string)}}));def_to_moz(de,(function To_Moz_TemplateLiteral(e){var t=[];var n=[];for(var i=0;i<e.segments.length;i++){if(i%2!==0){n.push(to_moz(e.segments[i]))}else{t.push({type:"TemplateElement",value:{raw:e.segments[i].raw,cooked:e.segments[i].value},tail:i===e.segments.length-1})}}return{type:"TemplateLiteral",quasis:t,expressions:n}}));def_to_moz(fe,(function To_Moz_FunctionDeclaration(e){return{type:"FunctionDeclaration",id:to_moz(e.name),params:e.argnames.map(to_moz),generator:e.is_generator,async:e.async,body:to_moz_scope("BlockStatement",e)}}));def_to_moz(le,(function To_Moz_FunctionExpression(e,t){var n=t.is_generator!==undefined?t.is_generator:e.is_generator;return{type:"FunctionExpression",id:to_moz(e.name),params:e.argnames.map(to_moz),generator:n,async:e.async,body:to_moz_scope("BlockStatement",e)}}));def_to_moz(ce,(function To_Moz_ArrowFunctionExpression(e){var t={type:"BlockStatement",body:e.body.map(to_moz)};return{type:"ArrowFunctionExpression",params:e.argnames.map(to_moz),async:e.async,body:t}}));def_to_moz(_e,(function To_Moz_ObjectPattern(e){if(e.is_array){return{type:"ArrayPattern",elements:e.names.map(to_moz)}}return{type:"ObjectPattern",properties:e.names.map(to_moz)}}));def_to_moz(G,(function To_Moz_Directive(e){return{type:"ExpressionStatement",expression:{type:"Literal",value:e.value,raw:e.print_to_string()},directive:e.value}}));def_to_moz(H,(function To_Moz_ExpressionStatement(e){return{type:"ExpressionStatement",expression:to_moz(e.body)}}));def_to_moz(Ce,(function To_Moz_SwitchCase(e){return{type:"SwitchCase",test:to_moz(e.expression),consequent:e.body.map(to_moz)}}));def_to_moz(Oe,(function To_Moz_TryStatement(e){return{type:"TryStatement",block:to_moz_block(e),handler:to_moz(e.bcatch),guardedHandlers:[],finalizer:to_moz(e.bfinally)}}));def_to_moz(xe,(function To_Moz_CatchClause(e){return{type:"CatchClause",param:to_moz(e.argname),guard:null,body:to_moz_block(e)}}));def_to_moz(we,(function To_Moz_VariableDeclaration(e){return{type:"VariableDeclaration",kind:e instanceof Pe?"const":e instanceof Ie?"let":"var",declarations:e.definitions.map(to_moz)}}));const assert_clause_to_moz=e=>{const t=[];if(e){for(const{key:n,value:i}of e.properties){const e=is_basic_identifier_string(n)?{type:"Identifier",name:n}:{type:"Literal",value:n,raw:JSON.stringify(n)};t.push({type:"ImportAttribute",key:e,value:to_moz(i)})}}return t};def_to_moz(Ue,(function To_Moz_ExportDeclaration(e){if(e.exported_names){if(e.exported_names[0].name.name==="*"){return{type:"ExportAllDeclaration",source:to_moz(e.module_name),assertions:assert_clause_to_moz(e.assert_clause)}}return{type:"ExportNamedDeclaration",specifiers:e.exported_names.map((function(e){return{type:"ExportSpecifier",exported:to_moz(e.foreign_name),local:to_moz(e.name)}})),declaration:to_moz(e.exported_definition),source:to_moz(e.module_name),assertions:assert_clause_to_moz(e.assert_clause)}}return{type:e.is_default?"ExportDefaultDeclaration":"ExportNamedDeclaration",declaration:to_moz(e.exported_value||e.exported_definition)}}));def_to_moz(Ve,(function To_Moz_ImportDeclaration(e){var t=[];if(e.imported_name){t.push({type:"ImportDefaultSpecifier",local:to_moz(e.imported_name)})}if(e.imported_names&&e.imported_names[0].foreign_name.name==="*"){t.push({type:"ImportNamespaceSpecifier",local:to_moz(e.imported_names[0].name)})}else if(e.imported_names){e.imported_names.forEach((function(e){t.push({type:"ImportSpecifier",local:to_moz(e.name),imported:to_moz(e.foreign_name)})}))}return{type:"ImportDeclaration",specifiers:t,source:to_moz(e.module_name),assertions:assert_clause_to_moz(e.assert_clause)}}));def_to_moz(ze,(function To_Moz_MetaProperty(){return{type:"MetaProperty",meta:{type:"Identifier",name:"import"},property:{type:"Identifier",name:"meta"}}}));def_to_moz(He,(function To_Moz_SequenceExpression(e){return{type:"SequenceExpression",expressions:e.expressions.map(to_moz)}}));def_to_moz(qe,(function To_Moz_PrivateMemberExpression(e){return{type:"MemberExpression",object:to_moz(e.expression),computed:false,property:{type:"PrivateIdentifier",name:e.property},optional:e.optional}}));def_to_moz(Xe,(function To_Moz_MemberExpression(e){var t=e instanceof Ye;return{type:"MemberExpression",object:to_moz(e.expression),computed:t,property:t?to_moz(e.property):{type:"Identifier",name:e.property},optional:e.optional}}));def_to_moz(je,(function To_Moz_ChainExpression(e){return{type:"ChainExpression",expression:to_moz(e.expression)}}));def_to_moz($e,(function To_Moz_Unary(e){return{type:e.operator=="++"||e.operator=="--"?"UpdateExpression":"UnaryExpression",operator:e.operator,prefix:e instanceof Ze,argument:to_moz(e.expression)}}));def_to_moz(Je,(function To_Moz_BinaryExpression(e){if(e.operator=="="&&to_moz_in_destructuring()){return{type:"AssignmentPattern",left:to_moz(e.left),right:to_moz(e.right)}}const t=e.operator=="&&"||e.operator=="||"||e.operator==="??"?"LogicalExpression":"BinaryExpression";return{type:t,left:to_moz(e.left),operator:e.operator,right:to_moz(e.right)}}));def_to_moz(it,(function To_Moz_ArrayExpression(e){return{type:"ArrayExpression",elements:e.elements.map(to_moz)}}));def_to_moz(rt,(function To_Moz_ObjectExpression(e){return{type:"ObjectExpression",properties:e.properties.map(to_moz)}}));def_to_moz(ot,(function To_Moz_Property(e,t){var n=e.key instanceof z?to_moz(e.key):{type:"Identifier",value:e.key};if(typeof e.key==="number"){n={type:"Literal",value:Number(e.key)}}if(typeof e.key==="string"){n={type:"Identifier",name:e.key}}var i;var r=typeof e.key==="string"||typeof e.key==="number";var o=r?false:!(e.key instanceof gt)||e.key instanceof Pt;if(e instanceof at){i="init";o=!r}else if(e instanceof ct){i="get"}else if(e instanceof lt){i="set"}if(e instanceof ut||e instanceof st){const t=e instanceof ut?"get":"set";return{type:"MethodDefinition",computed:false,kind:t,static:e.static,key:{type:"PrivateIdentifier",name:e.key.name},value:to_moz(e.value)}}if(e instanceof mt){return{type:"PropertyDefinition",key:{type:"PrivateIdentifier",name:e.key.name},value:to_moz(e.value),computed:false,static:e.static}}if(e instanceof dt){return{type:"PropertyDefinition",key:n,value:to_moz(e.value),computed:o,static:e.static}}if(t instanceof pt){return{type:"MethodDefinition",computed:o,kind:i,static:e.static,key:to_moz(e.key),value:to_moz(e.value)}}return{type:"Property",computed:o,kind:i,key:n,value:to_moz(e.value)}}));def_to_moz(ft,(function To_Moz_MethodDefinition(e,t){if(t instanceof rt){return{type:"Property",computed:!(e.key instanceof gt)||e.key instanceof Pt,kind:"init",method:true,shorthand:false,key:to_moz(e.key),value:to_moz(e.value)}}const n=e instanceof _t?{type:"PrivateIdentifier",name:e.key.name}:to_moz(e.key);return{type:"MethodDefinition",kind:e.key==="constructor"?"constructor":"method",key:n,value:to_moz(e.value),computed:!(e.key instanceof gt)||e.key instanceof Pt,static:e.static}}));def_to_moz(pt,(function To_Moz_Class(e){var t=e instanceof Et?"ClassExpression":"ClassDeclaration";return{type:t,superClass:to_moz(e.extends),id:e.name?to_moz(e.name):null,body:{type:"ClassBody",body:e.properties.map(to_moz)}}}));def_to_moz(vt,(function To_Moz_MetaProperty(){return{type:"MetaProperty",meta:{type:"Identifier",name:"new"},property:{type:"Identifier",name:"target"}}}));def_to_moz(gt,(function To_Moz_Identifier(e,t){if(e instanceof Ct&&t.quote){return{type:"Literal",value:e.name}}var n=e.definition();return{type:"Identifier",name:n?n.mangled_name||n.name:e.name}}));def_to_moz(Wt,(function To_Moz_RegExpLiteral(e){const t=e.value.source;const n=e.value.flags;return{type:"Literal",value:null,raw:e.print_to_string(),regex:{pattern:t,flags:n}}}));def_to_moz(Kt,(function To_Moz_Literal(e){var t=e.value;return{type:"Literal",value:t,raw:e.raw||e.print_to_string()}}));def_to_moz(qt,(function To_Moz_Atom(e){return{type:"Identifier",name:String(e.value)}}));def_to_moz(Xt,(e=>({type:"BigIntLiteral",value:e.value})));Jt.DEFMETHOD("to_mozilla_ast",Kt.prototype.to_mozilla_ast);Yt.DEFMETHOD("to_mozilla_ast",Kt.prototype.to_mozilla_ast);Zt.DEFMETHOD("to_mozilla_ast",(function To_Moz_ArrayHole(){return null}));X.DEFMETHOD("to_mozilla_ast",W.prototype.to_mozilla_ast);se.DEFMETHOD("to_mozilla_ast",le.prototype.to_mozilla_ast);function my_start_token(e){var t=e.loc,n=t&&t.start;var i=e.range;return new AST_Token("","",n&&n.line||0,n&&n.column||0,i?i[0]:e.start,false,[],[],t&&t.source)}function my_end_token(e){var t=e.loc,n=t&&t.end;var i=e.range;return new AST_Token("","",n&&n.line||0,n&&n.column||0,i?i[0]:e.end,false,[],[],t&&t.source)}var t=null;function from_moz(n){t.push(n);var i=n!=null?e[n.type](n):null;t.pop();return i}z.from_mozilla_ast=function(e){var n=t;t=[];var i=from_moz(e);t=n;return i};function set_moz_loc(e,t){var n=e.start;var i=e.end;if(!(n&&i)){return t}if(n.pos!=null&&i.endpos!=null){t.range=[n.pos,i.endpos]}if(n.line){t.loc={start:{line:n.line,column:n.col},end:i.endline?{line:i.endline,column:i.endcol}:null};if(n.file){t.loc.source=n.file}}return t}function def_to_moz(e,t){e.DEFMETHOD("to_mozilla_ast",(function(e){return set_moz_loc(this,t(this,e))}))}var n=null;function to_moz(e){if(n===null){n=[]}n.push(e);var t=e!=null?e.to_mozilla_ast(n[n.length-2]):null;n.pop();if(n.length===0){n=null}return t}function to_moz_in_destructuring(){var e=n.length;while(e--){if(n[e]instanceof _e){return true}}return false}function to_moz_block(e){return{type:"BlockStatement",body:e.body.map(to_moz)}}function to_moz_scope(e,t){var n=t.body.map(to_moz);if(t.body[0]instanceof H&&t.body[0].body instanceof Gt){n.unshift(to_moz(new q(t.body[0])))}return{type:e,body:n}}})();function first_in_statement(e){let t=e.parent(-1);for(let n=0,i;i=e.parent(n);n++){if(i instanceof U&&i.body===t)return true;if(i instanceof He&&i.expressions[0]===t||i.TYPE==="Call"&&i.expression===t||i instanceof pe&&i.prefix===t||i instanceof We&&i.expression===t||i instanceof Ye&&i.expression===t||i instanceof et&&i.condition===t||i instanceof Je&&i.left===t||i instanceof Qe&&i.expression===t){t=i}else{return false}}}function left_is_object(e){if(e instanceof rt)return true;if(e instanceof He)return left_is_object(e.expressions[0]);if(e.TYPE==="Call")return left_is_object(e.expression);if(e instanceof pe)return left_is_object(e.prefix);if(e instanceof We||e instanceof Ye)return left_is_object(e.expression);if(e instanceof et)return left_is_object(e.condition);if(e instanceof Je)return left_is_object(e.left);if(e instanceof Qe)return left_is_object(e.expression);return false}const sn=/^$|[;{][\s\n]*$/;const un=10;const ln=32;const cn=/[@#]__(PURE|INLINE|NOINLINE)__/g;function is_some_comments(e){return(e.type==="comment2"||e.type==="comment1")&&/@preserve|@copyright|@lic|@cc_on|^\**!/i.test(e.value)}class Rope{constructor(){this.committed="";this.current=""}append(e){this.current+=e}insertAt(e,t){const{committed:n,current:i}=this;if(t<n.length){this.committed=n.slice(0,t)+e+n.slice(t)}else if(t===n.length){this.committed+=e}else{t-=n.length;this.committed+=i.slice(0,t)+e;this.current=i.slice(t)}}charAt(e){const{committed:t}=this;if(e<t.length)return t[e];return this.current[e-t.length]}curLength(){return this.current.length}length(){return this.committed.length+this.current.length}toString(){return this.committed+this.current}}function OutputStream(e){var t=!e;e=defaults(e,{ascii_only:false,beautify:false,braces:false,comments:"some",ecma:5,ie8:false,indent_level:4,indent_start:0,inline_script:true,keep_numbers:false,keep_quoted_props:false,max_line_len:false,preamble:null,preserve_annotations:false,quote_keys:false,quote_style:0,safari10:false,semicolons:true,shebang:true,shorthand:undefined,source_map:null,webkit:false,width:80,wrap_iife:false,wrap_func_args:true,_destroy_ast:false},true);if(e.shorthand===undefined)e.shorthand=e.ecma>5;var n=return_false;if(e.comments){let t=e.comments;if(typeof e.comments==="string"&&/^\/.*\/[a-zA-Z]*$/.test(e.comments)){var i=e.comments.lastIndexOf("/");t=new RegExp(e.comments.substr(1,i-1),e.comments.substr(i+1))}if(t instanceof RegExp){n=function(e){return e.type!="comment5"&&t.test(e.value)}}else if(typeof t==="function"){n=function(e){return e.type!="comment5"&&t(this,e)}}else if(t==="some"){n=is_some_comments}else{n=return_true}}var r=0;var o=0;var a=1;var s=0;var u=new Rope;let l=new Set;var c=e.ascii_only?function(t,n=false,i=false){if(e.ecma>=2015&&!e.safari10&&!i){t=t.replace(/[\ud800-\udbff][\udc00-\udfff]/g,(function(e){var t=get_full_char_code(e,0).toString(16);return"\\u{"+t+"}"}))}return t.replace(/[\u0000-\u001f\u007f-\uffff]/g,(function(e){var t=e.charCodeAt(0).toString(16);if(t.length<=2&&!n){while(t.length<2)t="0"+t;return"\\x"+t}else{while(t.length<4)t="0"+t;return"\\u"+t}}))}:function(e){return e.replace(/[\ud800-\udbff][\udc00-\udfff]|([\ud800-\udbff]|[\udc00-\udfff])/g,(function(e,t){if(t){return"\\u"+t.charCodeAt(0).toString(16)}return e}))};function make_string(t,n){var i=0,r=0;t=t.replace(/[\\\b\f\n\r\v\t\x22\x27\u2028\u2029\0\ufeff]/g,(function(n,o){switch(n){case'"':++i;return'"';case"'":++r;return"'";case"\\":return"\\\\";case"\n":return"\\n";case"\r":return"\\r";case"\t":return"\\t";case"\b":return"\\b";case"\f":return"\\f";case"\v":return e.ie8?"\\x0B":"\\v";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";case"\ufeff":return"\\ufeff";case"\0":return/[0-9]/.test(get_full_char(t,o+1))?"\\x00":"\\0"}return n}));function quote_single(){return"'"+t.replace(/\x27/g,"\\'")+"'"}function quote_double(){return'"'+t.replace(/\x22/g,'\\"')+'"'}function quote_template(){return"`"+t.replace(/`/g,"\\`")+"`"}t=c(t);if(n==="`")return quote_template();switch(e.quote_style){case 1:return quote_single();case 2:return quote_double();case 3:return n=="'"?quote_single():quote_double();default:return i>r?quote_single():quote_double()}}function encode_string(t,n){var i=make_string(t,n);if(e.inline_script){i=i.replace(/<\x2f(script)([>\/\t\n\f\r ])/gi,"<\\/$1$2");i=i.replace(/\x3c!--/g,"\\x3c!--");i=i.replace(/--\x3e/g,"--\\x3e")}return i}function make_name(e){e=e.toString();e=c(e,true);return e}function make_indent(t){return" ".repeat(e.indent_start+r-t*e.indent_level)}var f=false;var _=false;var p=false;var d=0;var m=false;var h=false;var E=-1;var g="";var v,D,b=e.source_map&&[];var y=b?function(){b.forEach((function(t){try{let{name:n,token:i}=t;if(i.type=="name"||i.type==="privatename"){n=i.value}else if(n instanceof gt){n=i.type==="string"?i.value:n.name}e.source_map.add(t.token.file,t.line,t.col,t.token.line,t.token.col,is_basic_identifier_string(n)?n:undefined)}catch(e){}}));b=[]}:noop;var S=e.max_line_len?function(){if(o>e.max_line_len){if(d){u.insertAt("\n",d);const e=u.curLength();if(b){var t=e-o;b.forEach((function(e){e.line++;e.col+=t}))}a++;s++;o=e}}if(d){d=0;y()}}:noop;var A=makePredicate("( [ + * / - , . `");function print(t){t=String(t);var n=get_full_char(t,0);if(m&&n){m=false;if(n!=="\n"){print("\n");k()}}if(h&&n){h=false;if(!/[\s;})]/.test(n)){T()}}E=-1;var i=g.charAt(g.length-1);if(p){p=false;if(i===":"&&n==="}"||(!n||!";}".includes(n))&&i!==";"){if(e.semicolons||A.has(n)){u.append(";");o++;s++}else{S();if(o>0){u.append("\n");s++;a++;o=0}if(/^\s+$/.test(t)){p=true}}if(!e.beautify)_=false}}if(_){if(is_identifier_char(i)&&(is_identifier_char(n)||n=="\\")||n=="/"&&n==i||(n=="+"||n=="-")&&n==g){u.append(" ");o++;s++}_=false}if(v){b.push({token:v,name:D,line:a,col:o});v=false;if(!d)y()}u.append(t);f=t[t.length-1]=="(";s+=t.length;var r=t.split(/\r?\n/),l=r.length-1;a+=l;o+=r[0].length;if(l>0){S();o=r[l].length}g=t}var star=function(){print("*")};var T=e.beautify?function(){print(" ")}:function(){_=true};var k=e.beautify?function(t){if(e.beautify){print(make_indent(t?.5:0))}}:noop;var C=e.beautify?function(e,t){if(e===true)e=next_indent();var n=r;r=e;var i=t();r=n;return i}:function(e,t){return t()};var R=e.beautify?function(){if(E<0)return print("\n");if(u.charAt(E)!="\n"){u.insertAt("\n",E);s++;a++}E++}:e.max_line_len?function(){S();d=u.length()}:noop;var F=e.beautify?function(){print(";")}:function(){p=true};function force_semicolon(){p=false;print(";")}function next_indent(){return r+e.indent_level}function with_block(e){var t;print("{");R();C(next_indent(),(function(){t=e()}));k();print("}");return t}function with_parens(e){print("(");var t=e();print(")");return t}function with_square(e){print("[");var t=e();print("]");return t}function comma(){print(",");T()}function colon(){print(":");T()}var O=b?function(e,t){v=e;D=t}:noop;function get(){if(d){S()}return u.toString()}function has_nlb(){const e=u.toString();let t=e.length-1;while(t>=0){const n=e.charCodeAt(t);if(n===un){return true}if(n!==ln){return false}t--}return true}function filter_comment(t){if(!e.preserve_annotations){t=t.replace(cn," ")}if(/^\s*$/.test(t)){return""}return t.replace(/(<\s*\/\s*)(script)/i,"<\\/$2")}function prepend_comments(t){var i=this;var r=t.start;if(!r)return;var o=i.printed_comments;const a=t instanceof Ee&&t.value;if(r.comments_before&&o.has(r.comments_before)){if(a){r.comments_before=[]}else{return}}var u=r.comments_before;if(!u){u=r.comments_before=[]}o.add(u);if(a){var l=new TreeWalker((function(e){var t=l.parent();if(t instanceof Ee||t instanceof Je&&t.left===e||t.TYPE=="Call"&&t.expression===e||t instanceof et&&t.condition===e||t instanceof We&&t.expression===e||t instanceof He&&t.expressions[0]===e||t instanceof Ye&&t.expression===e||t instanceof Qe){if(!e.start)return;var n=e.start.comments_before;if(n&&!o.has(n)){o.add(n);u=u.concat(n)}}else{return true}}));l.push(t);t.value.walk(l)}if(s==0){if(u.length>0&&e.shebang&&u[0].type==="comment5"&&!o.has(u[0])){print("#!"+u.shift().value+"\n");k()}var c=e.preamble;if(c){print(c.replace(/\r\n?|[\n\u2028\u2029]|\s*$/g,"\n"))}}u=u.filter(n,t).filter((e=>!o.has(e)));if(u.length==0)return;var f=has_nlb();u.forEach((function(e,t){o.add(e);if(!f){if(e.nlb){print("\n");k();f=true}else if(t>0){T()}}if(/comment[134]/.test(e.type)){var n=filter_comment(e.value);if(n){print("//"+n+"\n");k()}f=true}else if(e.type=="comment2"){var n=filter_comment(e.value);if(n){print("/*"+n+"*/")}f=false}}));if(!f){if(r.nlb){print("\n");k()}else{T()}}}function append_comments(e,t){var i=this;var r=e.end;if(!r)return;var o=i.printed_comments;var a=r[t?"comments_before":"comments_after"];if(!a||o.has(a))return;if(!(e instanceof U||a.every((e=>!/comment[134]/.test(e.type)))))return;o.add(a);var s=u.length();a.filter(n,e).forEach((function(e,n){if(o.has(e))return;o.add(e);h=false;if(m){print("\n");k();m=false}else if(e.nlb&&(n>0||!has_nlb())){print("\n");k()}else if(n>0||!t){T()}if(/comment[134]/.test(e.type)){const t=filter_comment(e.value);if(t){print("//"+t)}m=true}else if(e.type=="comment2"){const t=filter_comment(e.value);if(t){print("/*"+t+"*/")}h=true}}));if(u.length()>s)E=s}const x=e["_destroy_ast"]?function gc_scope(e){e.body.length=0;e.argnames.length=0}:noop;var M=[];return{get:get,toString:get,indent:k,in_directive:false,use_asm:null,active_scope:null,indentation:function(){return r},current_width:function(){return o-r},should_break:function(){return e.width&&this.current_width()>=e.width},has_parens:function(){return f},newline:R,print:print,star:star,space:T,comma:comma,colon:colon,last:function(){return g},semicolon:F,force_semicolon:force_semicolon,to_utf8:c,print_name:function(e){print(make_name(e))},print_string:function(e,t,n){var i=encode_string(e,t);if(n===true&&!i.includes("\\")){if(!sn.test(u.toString())){force_semicolon()}force_semicolon()}print(i)},print_template_string_chars:function(e){var t=encode_string(e,"`").replace(/\${/g,"\\${");return print(t.substr(1,t.length-2))},encode_string:encode_string,next_indent:next_indent,with_indent:C,with_block:with_block,with_parens:with_parens,with_square:with_square,add_mapping:O,option:function(t){return e[t]},gc_scope:x,printed_comments:l,prepend_comments:t?noop:prepend_comments,append_comments:t||n===return_false?noop:append_comments,line:function(){return a},col:function(){return o},pos:function(){return s},push_node:function(e){M.push(e)},pop_node:function(){return M.pop()},parent:function(e){return M[M.length-2-(e||0)]}}}(function(){function DEFPRINT(e,t){e.DEFMETHOD("_codegen",t)}z.DEFMETHOD("print",(function(e,t){var n=this,i=n._codegen;if(n instanceof re){e.active_scope=n}else if(!e.use_asm&&n instanceof G&&n.value=="use asm"){e.use_asm=e.active_scope}function doit(){e.prepend_comments(n);n.add_source_map(e);i(n,e);e.append_comments(n)}e.push_node(n);if(t||n.needs_parens(e)){e.with_parens(doit)}else{doit()}e.pop_node();if(n===e.use_asm){e.use_asm=null}}));z.DEFMETHOD("_print",z.prototype.print);z.DEFMETHOD("print_to_string",(function(e){var t=OutputStream(e);this.print(t);return t.get()}));function PARENS(e,t){if(Array.isArray(e)){e.forEach((function(e){PARENS(e,t)}))}else{e.DEFMETHOD("needs_parens",t)}}PARENS(z,return_false);PARENS(le,(function(e){if(!e.has_parens()&&first_in_statement(e)){return true}if(e.option("webkit")){var t=e.parent();if(t instanceof Xe&&t.expression===this){return true}}if(e.option("wrap_iife")){var t=e.parent();if(t instanceof Ke&&t.expression===this){return true}}if(e.option("wrap_func_args")){var t=e.parent();if(t instanceof Ke&&t.args.includes(this)){return true}}return false}));PARENS(ce,(function(e){var t=e.parent();if(e.option("wrap_func_args")&&t instanceof Ke&&t.args.includes(this)){return true}return t instanceof Xe&&t.expression===this}));PARENS(rt,(function(e){return!e.has_parens()&&first_in_statement(e)}));PARENS(Et,first_in_statement);PARENS($e,(function(e){var t=e.parent();return t instanceof Xe&&t.expression===this||t instanceof Ke&&t.expression===this||t instanceof Je&&t.operator==="**"&&this instanceof Ze&&t.left===this&&this.operator!=="++"&&this.operator!=="--"}));PARENS(Se,(function(e){var t=e.parent();return t instanceof Xe&&t.expression===this||t instanceof Ke&&t.expression===this||t instanceof Je&&t.operator==="**"&&t.left===this||e.option("safari10")&&t instanceof Ze}));PARENS(He,(function(e){var t=e.parent();return t instanceof Ke||t instanceof $e||t instanceof Je||t instanceof Be||t instanceof Xe||t instanceof it||t instanceof ot||t instanceof et||t instanceof ce||t instanceof nt||t instanceof ae||t instanceof ne&&this===t.object||t instanceof Ae||t instanceof Ue}));PARENS(Je,(function(e){var t=e.parent();if(t instanceof Ke&&t.expression===this)return true;if(t instanceof $e)return true;if(t instanceof Xe&&t.expression===this)return true;if(t instanceof Je){const e=t.operator;const n=this.operator;if(n==="??"&&(e==="||"||e==="&&")){return true}if(e==="??"&&(n==="||"||n==="&&")){return true}const i=N[e];const r=N[n];if(i>r||i==r&&(this===t.right||e=="**")){return true}}}));PARENS(Ae,(function(e){var t=e.parent();if(t instanceof Je&&t.operator!=="=")return true;if(t instanceof Ke&&t.expression===this)return true;if(t instanceof et&&t.condition===this)return true;if(t instanceof $e)return true;if(t instanceof Xe&&t.expression===this)return true}));PARENS(Xe,(function(e){var t=e.parent();if(t instanceof Ge&&t.expression===this){return walk(this,(e=>{if(e instanceof re)return true;if(e instanceof Ke){return nn}}))}}));PARENS(Ke,(function(e){var t=e.parent(),n;if(t instanceof Ge&&t.expression===this||t instanceof Ue&&t.is_default&&this.expression instanceof le)return true;return this.expression instanceof le&&t instanceof Xe&&t.expression===this&&(n=e.parent(1))instanceof tt&&n.left===t}));PARENS(Ge,(function(e){var t=e.parent();if(this.args.length===0&&(t instanceof Xe||t instanceof Ke&&t.expression===this||t instanceof pe&&t.prefix===this))return true}));PARENS(Ht,(function(e){var t=e.parent();if(t instanceof Xe&&t.expression===this){var n=this.getValue();if(n<0||/^0/.test(make_num(n))){return true}}}));PARENS(Xt,(function(e){var t=e.parent();if(t instanceof Xe&&t.expression===this){var n=this.getValue();if(n.startsWith("-")){return true}}}));PARENS([tt,et],(function(e){var t=e.parent();if(t instanceof $e)return true;if(t instanceof Je&&!(t instanceof tt))return true;if(t instanceof Ke&&t.expression===this)return true;if(t instanceof et&&t.condition===this)return true;if(t instanceof Xe&&t.expression===this)return true;if(this instanceof tt&&this.left instanceof _e&&this.left.is_array===false)return true}));DEFPRINT(G,(function(e,t){t.print_string(e.value,e.quote);t.semicolon()}));DEFPRINT(ae,(function(e,t){t.print("...");e.expression.print(t)}));DEFPRINT(_e,(function(e,t){t.print(e.is_array?"[":"{");var n=e.names.length;e.names.forEach((function(e,i){if(i>0)t.comma();e.print(t);if(i==n-1&&e instanceof Zt)t.comma()}));t.print(e.is_array?"]":"}")}));DEFPRINT(K,(function(e,t){t.print("debugger");t.semicolon()}));function display_body(e,t,n,i){var r=e.length-1;n.in_directive=i;e.forEach((function(e,i){if(n.in_directive===true&&!(e instanceof G||e instanceof q||e instanceof H&&e.body instanceof Gt)){n.in_directive=false}if(!(e instanceof q)){n.indent();e.print(n);if(!(i==r&&t)){n.newline();if(t)n.newline()}}if(n.in_directive===true&&e instanceof H&&e.body instanceof Gt){n.in_directive=false}}));n.in_directive=false}Y.DEFMETHOD("_do_print_body",(function(e){force_statement(this.body,e)}));DEFPRINT(U,(function(e,t){e.body.print(t);t.semicolon()}));DEFPRINT(oe,(function(e,t){display_body(e.body,true,t,true);t.print("")}));DEFPRINT(j,(function(e,t){e.label.print(t);t.colon();e.body.print(t)}));DEFPRINT(H,(function(e,t){e.body.print(t);t.semicolon()}));function print_braced_empty(e,t){t.print("{");t.with_indent(t.next_indent(),(function(){t.append_comments(e,true)}));t.print("}")}function print_braced(e,t,n){if(e.body.length>0){t.with_block((function(){display_body(e.body,false,t,n)}))}else print_braced_empty(e,t)}DEFPRINT(W,(function(e,t){print_braced(e,t)}));DEFPRINT(q,(function(e,t){t.semicolon()}));DEFPRINT(Q,(function(e,t){t.print("do");t.space();make_block(e.body,t);t.space();t.print("while");t.space();t.with_parens((function(){e.condition.print(t)}));t.semicolon()}));DEFPRINT(J,(function(e,t){t.print("while");t.space();t.with_parens((function(){e.condition.print(t)}));t.space();e._do_print_body(t)}));DEFPRINT(ee,(function(e,t){t.print("for");t.space();t.with_parens((function(){if(e.init){if(e.init instanceof we){e.init.print(t)}else{parenthesize_for_noin(e.init,t,true)}t.print(";");t.space()}else{t.print(";")}if(e.condition){e.condition.print(t);t.print(";");t.space()}else{t.print(";")}if(e.step){e.step.print(t)}}));t.space();e._do_print_body(t)}));DEFPRINT(te,(function(e,t){t.print("for");if(e.await){t.space();t.print("await")}t.space();t.with_parens((function(){e.init.print(t);t.space();t.print(e instanceof ne?"of":"in");t.space();e.object.print(t)}));t.space();e._do_print_body(t)}));DEFPRINT(ie,(function(e,t){t.print("with");t.space();t.with_parens((function(){e.expression.print(t)}));t.space();e._do_print_body(t)}));se.DEFMETHOD("_do_print",(function(e,t){var n=this;if(!t){if(n.async){e.print("async");e.space()}e.print("function");if(n.is_generator){e.star()}if(n.name){e.space()}}if(n.name instanceof gt){n.name.print(e)}else if(t&&n.name instanceof z){e.with_square((function(){n.name.print(e)}))}e.with_parens((function(){n.argnames.forEach((function(t,n){if(n)e.comma();t.print(e)}))}));e.space();print_braced(n,e,true)}));DEFPRINT(se,(function(e,t){e._do_print(t);t.gc_scope(e)}));DEFPRINT(pe,(function(e,t){var n=e.prefix;var i=n instanceof se||n instanceof Je||n instanceof et||n instanceof He||n instanceof $e||n instanceof We&&n.expression instanceof rt;if(i)t.print("(");e.prefix.print(t);if(i)t.print(")");e.template_string.print(t)}));DEFPRINT(de,(function(e,t){var n=t.parent()instanceof pe;t.print("`");for(var i=0;i<e.segments.length;i++){if(!(e.segments[i]instanceof me)){t.print("${");e.segments[i].print(t);t.print("}")}else if(n){t.print(e.segments[i].raw)}else{t.print_template_string_chars(e.segments[i].value)}}t.print("`")}));DEFPRINT(me,(function(e,t){t.print_template_string_chars(e.value)}));ce.DEFMETHOD("_do_print",(function(e){var t=this;var n=e.parent();var i=n instanceof Je&&!(n instanceof tt)||n instanceof $e||n instanceof Ke&&t===n.expression;if(i){e.print("(")}if(t.async){e.print("async");e.space()}if(t.argnames.length===1&&t.argnames[0]instanceof gt){t.argnames[0].print(e)}else{e.with_parens((function(){t.argnames.forEach((function(t,n){if(n)e.comma();t.print(e)}))}))}e.space();e.print("=>");e.space();const r=t.body[0];if(t.body.length===1&&r instanceof ge){const t=r.value;if(!t){e.print("{}")}else if(left_is_object(t)){e.print("(");t.print(e);e.print(")")}else{t.print(e)}}else{print_braced(t,e)}if(i){e.print(")")}e.gc_scope(t)}));Ee.DEFMETHOD("_do_print",(function(e,t){e.print(t);if(this.value){e.space();const t=this.value.start.comments_before;if(t&&t.length&&!e.printed_comments.has(t)){e.print("(");this.value.print(e);e.print(")")}else{this.value.print(e)}}e.semicolon()}));DEFPRINT(ge,(function(e,t){e._do_print(t,"return")}));DEFPRINT(ve,(function(e,t){e._do_print(t,"throw")}));DEFPRINT(Ae,(function(e,t){var n=e.is_star?"*":"";t.print("yield"+n);if(e.expression){t.space();e.expression.print(t)}}));DEFPRINT(Se,(function(e,t){t.print("await");t.space();var n=e.expression;var i=!(n instanceof Ke||n instanceof Pt||n instanceof Xe||n instanceof $e||n instanceof Kt||n instanceof Se||n instanceof rt);if(i)t.print("(");e.expression.print(t);if(i)t.print(")")}));De.DEFMETHOD("_do_print",(function(e,t){e.print(t);if(this.label){e.space();this.label.print(e)}e.semicolon()}));DEFPRINT(be,(function(e,t){e._do_print(t,"break")}));DEFPRINT(ye,(function(e,t){e._do_print(t,"continue")}));function make_then(e,t){var n=e.body;if(t.option("braces")||t.option("ie8")&&n instanceof Q)return make_block(n,t);if(!n)return t.force_semicolon();while(true){if(n instanceof Te){if(!n.alternative){make_block(e.body,t);return}n=n.alternative}else if(n instanceof Y){n=n.body}else break}force_statement(e.body,t)}DEFPRINT(Te,(function(e,t){t.print("if");t.space();t.with_parens((function(){e.condition.print(t)}));t.space();if(e.alternative){make_then(e,t);t.space();t.print("else");t.space();if(e.alternative instanceof Te)e.alternative.print(t);else force_statement(e.alternative,t)}else{e._do_print_body(t)}}));DEFPRINT(ke,(function(e,t){t.print("switch");t.space();t.with_parens((function(){e.expression.print(t)}));t.space();var n=e.body.length-1;if(n<0)print_braced_empty(e,t);else t.with_block((function(){e.body.forEach((function(e,i){t.indent(true);e.print(t);if(i<n&&e.body.length>0)t.newline()}))}))}));Ce.DEFMETHOD("_do_print_body",(function(e){e.newline();this.body.forEach((function(t){e.indent();t.print(e);e.newline()}))}));DEFPRINT(Re,(function(e,t){t.print("default:");e._do_print_body(t)}));DEFPRINT(Fe,(function(e,t){t.print("case");t.space();e.expression.print(t);t.print(":");e._do_print_body(t)}));DEFPRINT(Oe,(function(e,t){t.print("try");t.space();print_braced(e,t);if(e.bcatch){t.space();e.bcatch.print(t)}if(e.bfinally){t.space();e.bfinally.print(t)}}));DEFPRINT(xe,(function(e,t){t.print("catch");if(e.argname){t.space();t.with_parens((function(){e.argname.print(t)}))}t.space();print_braced(e,t)}));DEFPRINT(Me,(function(e,t){t.print("finally");t.space();print_braced(e,t)}));we.DEFMETHOD("_do_print",(function(e,t){e.print(t);e.space();this.definitions.forEach((function(t,n){if(n)e.comma();t.print(e)}));var n=e.parent();var i=n instanceof ee||n instanceof te;var r=!i||n&&n.init!==this;if(r)e.semicolon()}));DEFPRINT(Ie,(function(e,t){e._do_print(t,"let")}));DEFPRINT(Ne,(function(e,t){e._do_print(t,"var")}));DEFPRINT(Pe,(function(e,t){e._do_print(t,"const")}));DEFPRINT(Ve,(function(e,t){t.print("import");t.space();if(e.imported_name){e.imported_name.print(t)}if(e.imported_name&&e.imported_names){t.print(",");t.space()}if(e.imported_names){if(e.imported_names.length===1&&e.imported_names[0].foreign_name.name==="*"){e.imported_names[0].print(t)}else{t.print("{");e.imported_names.forEach((function(n,i){t.space();n.print(t);if(i<e.imported_names.length-1){t.print(",")}}));t.space();t.print("}")}}if(e.imported_name||e.imported_names){t.space();t.print("from");t.space()}e.module_name.print(t);if(e.assert_clause){t.print("assert");e.assert_clause.print(t)}t.semicolon()}));DEFPRINT(ze,(function(e,t){t.print("import.meta")}));DEFPRINT(Le,(function(e,t){var n=t.parent()instanceof Ve;var i=e.name.definition();var r=(i&&i.mangled_name||e.name.name)!==e.foreign_name.name;if(r){if(n){t.print(e.foreign_name.name)}else{e.name.print(t)}t.space();t.print("as");t.space();if(n){e.name.print(t)}else{t.print(e.foreign_name.name)}}else{e.name.print(t)}}));DEFPRINT(Ue,(function(e,t){t.print("export");t.space();if(e.is_default){t.print("default");t.space()}if(e.exported_names){if(e.exported_names.length===1&&e.exported_names[0].name.name==="*"){e.exported_names[0].print(t)}else{t.print("{");e.exported_names.forEach((function(n,i){t.space();n.print(t);if(i<e.exported_names.length-1){t.print(",")}}));t.space();t.print("}")}}else if(e.exported_value){e.exported_value.print(t)}else if(e.exported_definition){e.exported_definition.print(t);if(e.exported_definition instanceof we)return}if(e.module_name){t.space();t.print("from");t.space();e.module_name.print(t)}if(e.assert_clause){t.print("assert");e.assert_clause.print(t)}if(e.exported_value&&!(e.exported_value instanceof fe||e.exported_value instanceof le||e.exported_value instanceof pt)||e.module_name||e.exported_names){t.semicolon()}}));function parenthesize_for_noin(e,t,n){var i=false;if(n){i=walk(e,(e=>{if(e instanceof re&&!(e instanceof ce)){return true}if(e instanceof Je&&e.operator=="in"){return nn}}))}e.print(t,i)}DEFPRINT(Be,(function(e,t){e.name.print(t);if(e.value){t.space();t.print("=");t.space();var n=t.parent(1);var i=n instanceof ee||n instanceof te;parenthesize_for_noin(e.value,t,i)}}));DEFPRINT(Ke,(function(e,t){e.expression.print(t);if(e instanceof Ge&&e.args.length===0)return;if(e.expression instanceof Ke||e.expression instanceof se){t.add_mapping(e.start)}if(e.optional)t.print("?.");t.with_parens((function(){e.args.forEach((function(e,n){if(n)t.comma();e.print(t)}))}))}));DEFPRINT(Ge,(function(e,t){t.print("new");t.space();Ke.prototype._codegen(e,t)}));He.DEFMETHOD("_do_print",(function(e){this.expressions.forEach((function(t,n){if(n>0){e.comma();if(e.should_break()){e.newline();e.indent()}}t.print(e)}))}));DEFPRINT(He,(function(e,t){e._do_print(t)}));DEFPRINT(We,(function(e,t){var n=e.expression;n.print(t);var i=e.property;var r=f.has(i)?t.option("ie8"):!is_identifier_string(i,t.option("ecma")>=2015||t.option("safari10"));if(e.optional)t.print("?.");if(r){t.print("[");t.add_mapping(e.end);t.print_string(i);t.print("]")}else{if(n instanceof Ht&&n.getValue()>=0){if(!/[xa-f.)]/i.test(t.last())){t.print(".")}}if(!e.optional)t.print(".");t.add_mapping(e.end);t.print_name(i)}}));DEFPRINT(qe,(function(e,t){var n=e.expression;n.print(t);var i=e.property;if(e.optional)t.print("?");t.print(".#");t.add_mapping(e.end);t.print_name(i)}));DEFPRINT(Ye,(function(e,t){e.expression.print(t);if(e.optional)t.print("?.");t.print("[");e.property.print(t);t.print("]")}));DEFPRINT(je,(function(e,t){e.expression.print(t)}));DEFPRINT(Ze,(function(e,t){var n=e.operator;t.print(n);if(/^[a-z]/i.test(n)||/[+-]$/.test(n)&&e.expression instanceof Ze&&/^[+-]/.test(e.expression.operator)){t.space()}e.expression.print(t)}));DEFPRINT(Qe,(function(e,t){e.expression.print(t);t.print(e.operator)}));DEFPRINT(Je,(function(e,t){var n=e.operator;e.left.print(t);if(n[0]==">"&&e.left instanceof Qe&&e.left.operator=="--"){t.print(" ")}else{t.space()}t.print(n);if((n=="<"||n=="<<")&&e.right instanceof Ze&&e.right.operator=="!"&&e.right.expression instanceof Ze&&e.right.expression.operator=="--"){t.print(" ")}else{t.space()}e.right.print(t)}));DEFPRINT(et,(function(e,t){e.condition.print(t);t.space();t.print("?");t.space();e.consequent.print(t);t.space();t.colon();e.alternative.print(t)}));DEFPRINT(it,(function(e,t){t.with_square((function(){var n=e.elements,i=n.length;if(i>0)t.space();n.forEach((function(e,n){if(n)t.comma();e.print(t);if(n===i-1&&e instanceof Zt)t.comma()}));if(i>0)t.space()}))}));DEFPRINT(rt,(function(e,t){if(e.properties.length>0)t.with_block((function(){e.properties.forEach((function(e,n){if(n){t.print(",");t.newline()}t.indent();e.print(t)}));t.newline()}));else print_braced_empty(e,t)}));DEFPRINT(pt,(function(e,t){t.print("class");t.space();if(e.name){e.name.print(t);t.space()}if(e.extends){var n=!(e.extends instanceof Pt)&&!(e.extends instanceof Xe)&&!(e.extends instanceof Et)&&!(e.extends instanceof le);t.print("extends");if(n){t.print("(")}else{t.space()}e.extends.print(t);if(n){t.print(")")}else{t.space()}}if(e.properties.length>0)t.with_block((function(){e.properties.forEach((function(e,n){if(n){t.newline()}t.indent();e.print(t)}));t.newline()}));else t.print("{}")}));DEFPRINT(vt,(function(e,t){t.print("new.target")}));function print_property_name(e,t,n){if(n.option("quote_keys")){return n.print_string(e)}if(""+ +e==e&&e>=0){if(n.option("keep_numbers")){return n.print(e)}return n.print(make_num(e))}var i=f.has(e)?n.option("ie8"):n.option("ecma")<2015||n.option("safari10")?!is_basic_identifier_string(e):!is_identifier_string(e,true);if(i||t&&n.option("keep_quoted_props")){return n.print_string(e,t)}return n.print_name(e)}DEFPRINT(at,(function(e,t){function get_name(e){var t=e.definition();return t?t.mangled_name||t.name:e.name}var n=t.option("shorthand");if(n&&e.value instanceof gt&&is_identifier_string(e.key,t.option("ecma")>=2015||t.option("safari10"))&&get_name(e.value)===e.key&&!f.has(e.key)){print_property_name(e.key,e.quote,t)}else if(n&&e.value instanceof nt&&e.value.left instanceof gt&&is_identifier_string(e.key,t.option("ecma")>=2015||t.option("safari10"))&&get_name(e.value.left)===e.key){print_property_name(e.key,e.quote,t);t.space();t.print("=");t.space();e.value.right.print(t)}else{if(!(e.key instanceof z)){print_property_name(e.key,e.quote,t)}else{t.with_square((function(){e.key.print(t)}))}t.colon();e.value.print(t)}}));DEFPRINT(mt,((e,t)=>{if(e.static){t.print("static");t.space()}t.print("#");print_property_name(e.key.name,e.quote,t);if(e.value){t.print("=");e.value.print(t)}t.semicolon()}));DEFPRINT(dt,((e,t)=>{if(e.static){t.print("static");t.space()}if(e.key instanceof Rt){print_property_name(e.key.name,e.quote,t)}else{t.print("[");e.key.print(t);t.print("]")}if(e.value){t.print("=");e.value.print(t)}t.semicolon()}));ot.DEFMETHOD("_print_getter_setter",(function(e,t,n){var i=this;if(i.static){n.print("static");n.space()}if(e){n.print(e);n.space()}if(i.key instanceof Ct){if(t)n.print("#");print_property_name(i.key.name,i.quote,n)}else{n.with_square((function(){i.key.print(n)}))}i.value._do_print(n,true)}));DEFPRINT(lt,(function(e,t){e._print_getter_setter("set",false,t)}));DEFPRINT(ct,(function(e,t){e._print_getter_setter("get",false,t)}));DEFPRINT(st,(function(e,t){e._print_getter_setter("set",true,t)}));DEFPRINT(ut,(function(e,t){e._print_getter_setter("get",true,t)}));DEFPRINT(_t,(function(e,t){var n;if(e.is_generator&&e.async){n="async*"}else if(e.is_generator){n="*"}else if(e.async){n="async"}e._print_getter_setter(n,true,t)}));DEFPRINT(ft,(function(e,t){var n;if(e.is_generator&&e.async){n="async*"}else if(e.is_generator){n="*"}else if(e.async){n="async"}e._print_getter_setter(n,false,t)}));gt.DEFMETHOD("_do_print",(function(e){var t=this.definition();e.print_name(t?t.mangled_name||t.name:this.name)}));DEFPRINT(gt,(function(e,t){e._do_print(t)}));DEFPRINT(Zt,noop);DEFPRINT(zt,(function(e,t){t.print("this")}));DEFPRINT(Ut,(function(e,t){t.print("super")}));DEFPRINT(Kt,(function(e,t){t.print(e.getValue())}));DEFPRINT(Gt,(function(e,t){t.print_string(e.getValue(),e.quote,t.in_directive)}));DEFPRINT(Ht,(function(e,t){if((t.option("keep_numbers")||t.use_asm)&&e.raw){t.print(e.raw)}else{t.print(make_num(e.getValue()))}}));DEFPRINT(Xt,(function(e,t){t.print(e.getValue()+"n")}));const e=/(<\s*\/\s*script)/i;const slash_script_replace=(e,t)=>t.replace("/","\\/");DEFPRINT(Wt,(function(t,n){let{source:i,flags:r}=t.getValue();i=regexp_source_fix(i);r=r?sort_regexp_flags(r):"";i=i.replace(e,slash_script_replace);n.print(n.to_utf8(`/${i}/${r}`,false,true));const o=n.parent();if(o instanceof Je&&/^\w/.test(o.operator)&&o.left===t){n.print(" ")}}));function force_statement(e,t){if(t.option("braces")){make_block(e,t)}else{if(!e||e instanceof q)t.force_semicolon();else e.print(t)}}function best_of(e){var t=e[0],n=t.length;for(var i=1;i<e.length;++i){if(e[i].length<n){t=e[i];n=t.length}}return t}function make_num(e){var t=e.toString(10).replace(/^0\./,".").replace("e+","e");var n=[t];if(Math.floor(e)===e){if(e<0){n.push("-0x"+(-e).toString(16).toLowerCase())}else{n.push("0x"+e.toString(16).toLowerCase())}}var i,r,o;if(i=/^\.0+/.exec(t)){r=i[0].length;o=t.slice(r);n.push(o+"e-"+(o.length+r-1))}else if(i=/0+$/.exec(t)){r=i[0].length;n.push(t.slice(0,-r)+"e"+r)}else if(i=/^(\d)\.(\d+)e(-?\d+)$/.exec(t)){n.push(i[1]+i[2]+"e"+(i[3]-i[2].length))}return best_of(n)}function make_block(e,t){if(!e||e instanceof q)t.print("{}");else if(e instanceof W)e.print(t);else t.with_block((function(){t.indent();e.print(t);t.newline()}))}function DEFMAP(e,t){e.forEach((function(e){e.DEFMETHOD("add_source_map",t)}))}DEFMAP([z,j,oe],noop);DEFMAP([it,W,xe,pt,Kt,K,we,G,Me,he,se,Ge,rt,Y,gt,ke,Ce,de,me,Oe],(function(e){e.add_mapping(this.start)}));DEFMAP([ct,lt,ut,st],(function(e){e.add_mapping(this.key.end,this.key.name)}));DEFMAP([ot],(function(e){e.add_mapping(this.start,this.key)}))})();const shallow_cmp=(e,t)=>e===null&&t===null||e.TYPE===t.TYPE&&e.shallow_cmp(t);const equivalent_to=(e,t)=>{if(!shallow_cmp(e,t))return false;const n=[e];const i=[t];const r=n.push.bind(n);const o=i.push.bind(i);while(n.length&&i.length){const e=n.pop();const t=i.pop();if(!shallow_cmp(e,t))return false;e._children_backwards(r);t._children_backwards(o);if(n.length!==i.length){return false}}return n.length==0&&i.length==0};const pass_through=()=>true;z.prototype.shallow_cmp=function(){throw new Error("did not find a shallow_cmp function for "+this.constructor.name)};K.prototype.shallow_cmp=pass_through;G.prototype.shallow_cmp=function(e){return this.value===e.value};H.prototype.shallow_cmp=pass_through;X.prototype.shallow_cmp=pass_through;q.prototype.shallow_cmp=pass_through;j.prototype.shallow_cmp=function(e){return this.label.name===e.label.name};Q.prototype.shallow_cmp=pass_through;J.prototype.shallow_cmp=pass_through;ee.prototype.shallow_cmp=function(e){return(this.init==null?e.init==null:this.init===e.init)&&(this.condition==null?e.condition==null:this.condition===e.condition)&&(this.step==null?e.step==null:this.step===e.step)};te.prototype.shallow_cmp=pass_through;ne.prototype.shallow_cmp=pass_through;ie.prototype.shallow_cmp=pass_through;oe.prototype.shallow_cmp=pass_through;ae.prototype.shallow_cmp=pass_through;se.prototype.shallow_cmp=function(e){return this.is_generator===e.is_generator&&this.async===e.async};_e.prototype.shallow_cmp=function(e){return this.is_array===e.is_array};pe.prototype.shallow_cmp=pass_through;de.prototype.shallow_cmp=pass_through;me.prototype.shallow_cmp=function(e){return this.value===e.value};he.prototype.shallow_cmp=pass_through;De.prototype.shallow_cmp=pass_through;Se.prototype.shallow_cmp=pass_through;Ae.prototype.shallow_cmp=function(e){return this.is_star===e.is_star};Te.prototype.shallow_cmp=function(e){return this.alternative==null?e.alternative==null:this.alternative===e.alternative};ke.prototype.shallow_cmp=pass_through;Ce.prototype.shallow_cmp=pass_through;Oe.prototype.shallow_cmp=function(e){return(this.bcatch==null?e.bcatch==null:this.bcatch===e.bcatch)&&(this.bfinally==null?e.bfinally==null:this.bfinally===e.bfinally)};xe.prototype.shallow_cmp=function(e){return this.argname==null?e.argname==null:this.argname===e.argname};Me.prototype.shallow_cmp=pass_through;we.prototype.shallow_cmp=pass_through;Be.prototype.shallow_cmp=function(e){return this.value==null?e.value==null:this.value===e.value};Le.prototype.shallow_cmp=pass_through;Ve.prototype.shallow_cmp=function(e){return(this.imported_name==null?e.imported_name==null:this.imported_name===e.imported_name)&&(this.imported_names==null?e.imported_names==null:this.imported_names===e.imported_names)};ze.prototype.shallow_cmp=pass_through;Ue.prototype.shallow_cmp=function(e){return(this.exported_definition==null?e.exported_definition==null:this.exported_definition===e.exported_definition)&&(this.exported_value==null?e.exported_value==null:this.exported_value===e.exported_value)&&(this.exported_names==null?e.exported_names==null:this.exported_names===e.exported_names)&&this.module_name===e.module_name&&this.is_default===e.is_default};Ke.prototype.shallow_cmp=pass_through;He.prototype.shallow_cmp=pass_through;Xe.prototype.shallow_cmp=pass_through;je.prototype.shallow_cmp=pass_through;We.prototype.shallow_cmp=function(e){return this.property===e.property};qe.prototype.shallow_cmp=function(e){return this.property===e.property};$e.prototype.shallow_cmp=function(e){return this.operator===e.operator};Je.prototype.shallow_cmp=function(e){return this.operator===e.operator};et.prototype.shallow_cmp=pass_through;it.prototype.shallow_cmp=pass_through;rt.prototype.shallow_cmp=pass_through;ot.prototype.shallow_cmp=pass_through;at.prototype.shallow_cmp=function(e){return this.key===e.key};lt.prototype.shallow_cmp=function(e){return this.static===e.static};ct.prototype.shallow_cmp=function(e){return this.static===e.static};ft.prototype.shallow_cmp=function(e){return this.static===e.static&&this.is_generator===e.is_generator&&this.async===e.async};pt.prototype.shallow_cmp=function(e){return(this.name==null?e.name==null:this.name===e.name)&&(this.extends==null?e.extends==null:this.extends===e.extends)};dt.prototype.shallow_cmp=function(e){return this.static===e.static};gt.prototype.shallow_cmp=function(e){return this.name===e.name};vt.prototype.shallow_cmp=pass_through;zt.prototype.shallow_cmp=pass_through;Ut.prototype.shallow_cmp=pass_through;Gt.prototype.shallow_cmp=function(e){return this.value===e.value};Ht.prototype.shallow_cmp=function(e){return this.value===e.value};Xt.prototype.shallow_cmp=function(e){return this.value===e.value};Wt.prototype.shallow_cmp=function(e){return this.value.flags===e.value.flags&&this.value.source===e.value.source};qt.prototype.shallow_cmp=pass_through;const _n=1<<0;const pn=1<<1;let dn=null;let mn=null;let hn=null;class SymbolDef{constructor(e,t,n){this.name=t.name;this.orig=[t];this.init=n;this.eliminated=0;this.assignments=0;this.scope=e;this.replaced=0;this.global=false;this.export=0;this.mangled_name=null;this.undeclared=false;this.id=SymbolDef.next_id++;this.chained=false;this.direct_access=false;this.escaped=0;this.recursive_refs=0;this.references=[];this.should_replace=undefined;this.single_use=false;this.fixed=false;Object.seal(this)}fixed_value(){if(!this.fixed||this.fixed instanceof z)return this.fixed;return this.fixed()}unmangleable(e){if(!e)e={};if(dn&&dn.has(this.id)&&keep_name(e.keep_fnames,this.orig[0].name))return true;return this.global&&!e.toplevel||this.export&_n||this.undeclared||!e.eval&&this.scope.pinned()||(this.orig[0]instanceof Ft||this.orig[0]instanceof kt)&&keep_name(e.keep_fnames,this.orig[0].name)||this.orig[0]instanceof Ct||(this.orig[0]instanceof xt||this.orig[0]instanceof Ot)&&keep_name(e.keep_classnames,this.orig[0].name)}mangle(e){const t=e.cache&&e.cache.props;if(this.global&&t&&t.has(this.name)){this.mangled_name=t.get(this.name)}else if(!this.mangled_name&&!this.unmangleable(e)){var n=this.scope;var i=this.orig[0];if(e.ie8&&i instanceof Ft)n=n.parent_scope;const r=redefined_catch_def(this);this.mangled_name=r?r.mangled_name||r.name:n.next_mangled(e,this);if(this.global&&t){t.set(this.name,this.mangled_name)}}}}SymbolDef.next_id=1;function redefined_catch_def(e){if(e.orig[0]instanceof Mt&&e.scope.is_block_scope()){return e.scope.get_defun_scope().variables.get(e.name)}}re.DEFMETHOD("figure_out_scope",(function(e,{parent_scope:t=null,toplevel:n=this}={}){e=defaults(e,{cache:null,ie8:false,safari10:false});if(!(n instanceof oe)){throw new Error("Invalid toplevel scope")}var i=this.parent_scope=t;var r=new Map;var o=null;var a=null;var s=[];var u=new TreeWalker(((t,n)=>{if(t.is_block_scope()){const r=i;t.block_scope=i=new re(t);i._block_scope=true;const o=t instanceof xe?r.parent_scope:r;i.init_scope_vars(o);i.uses_with=r.uses_with;i.uses_eval=r.uses_eval;if(e.safari10){if(t instanceof ee||t instanceof te){s.push(i)}}if(t instanceof ke){const e=i;i=r;t.expression.walk(u);i=e;for(let e=0;e<t.body.length;e++){t.body[e].walk(u)}}else{n()}i=r;return true}if(t instanceof _e){const e=a;a=t;n();a=e;return true}if(t instanceof re){t.init_scope_vars(i);var l=i;var c=o;var f=r;o=i=t;r=new Map;n();i=l;o=c;r=f;return true}if(t instanceof j){var _=t.label;if(r.has(_.name)){throw new Error(string_template("Label {name} defined twice",_))}r.set(_.name,_);n();r.delete(_.name);return true}if(t instanceof ie){for(var p=i;p;p=p.parent_scope)p.uses_with=true;return}if(t instanceof gt){t.scope=i}if(t instanceof It){t.thedef=t;t.references=[]}if(t instanceof Ft){o.def_function(t,t.name=="arguments"?undefined:o)}else if(t instanceof kt){const e=o.parent_scope;t.scope=u.directives["use strict"]?e:e.get_defun_scope();mark_export(t.scope.def_function(t,o),1)}else if(t instanceof xt){mark_export(o.def_variable(t,o),1)}else if(t instanceof wt){i.def_variable(t)}else if(t instanceof Ot){mark_export((t.scope=o.parent_scope).def_function(t,o),1)}else if(t instanceof bt||t instanceof At||t instanceof St||t instanceof Mt){var d;if(t instanceof yt){d=i.def_variable(t,null)}else{d=o.def_variable(t,t.TYPE=="SymbolVar"?null:undefined)}if(!d.orig.every((e=>{if(e===t)return true;if(t instanceof yt){return e instanceof Ft}return!(e instanceof At||e instanceof St)}))){js_error(`"${t.name}" is redeclared`,t.start.file,t.start.line,t.start.col,t.start.pos)}if(!(t instanceof Tt))mark_export(d,2);if(o!==i){t.mark_enclosed();var d=i.find_variable(t);if(t.thedef!==d){t.thedef=d;t.reference()}}}else if(t instanceof Vt){var m=r.get(t.name);if(!m)throw new Error(string_template("Undefined label {name} [{line},{col}]",{name:t.name,line:t.start.line,col:t.start.col}));t.thedef=m}if(!(i instanceof oe)&&(t instanceof Ue||t instanceof Ve)){js_error(`"${t.TYPE}" statement may only appear at the top level`,t.start.file,t.start.line,t.start.col,t.start.pos)}}));this.walk(u);function mark_export(e,t){if(a){var n=0;do{t++}while(u.parent(n++)!==a)}var i=u.parent(t);if(e.export=i instanceof Ue?_n:0){var r=i.exported_definition;if((r instanceof fe||r instanceof ht)&&i.is_default){e.export=pn}}}const l=this instanceof oe;if(l){this.globals=new Map}var u=new TreeWalker((e=>{if(e instanceof De&&e.label){e.label.thedef.references.push(e);return true}if(e instanceof Pt){var t=e.name;if(t=="eval"&&u.parent()instanceof Ke){for(var i=e.scope;i&&!i.uses_eval;i=i.parent_scope){i.uses_eval=true}}var r;if(u.parent()instanceof Le&&u.parent(1).module_name||!(r=e.scope.find_variable(t))){r=n.def_global(e);if(e instanceof Bt)r.export=_n}else if(r.scope instanceof se&&t=="arguments"){r.scope.uses_arguments=true}e.thedef=r;e.reference();if(e.scope.is_block_scope()&&!(r.orig[0]instanceof yt)){e.scope=e.scope.get_defun_scope()}return true}var o;if(e instanceof Mt&&(o=redefined_catch_def(e.definition()))){var i=e.scope;while(i){push_uniq(i.enclosed,o);if(i===o.scope)break;i=i.parent_scope}}}));this.walk(u);if(e.ie8||e.safari10){walk(this,(e=>{if(e instanceof Mt){var t=e.name;var i=e.thedef.references;var r=e.scope.get_defun_scope();var o=r.find_variable(t)||n.globals.get(t)||r.def_variable(e);i.forEach((function(e){e.thedef=o;e.reference()}));e.thedef=o;e.reference();return true}}))}if(e.safari10){for(const e of s){e.parent_scope.variables.forEach((function(t){push_uniq(e.enclosed,t)}))}}}));oe.DEFMETHOD("def_global",(function(e){var t=this.globals,n=e.name;if(t.has(n)){return t.get(n)}else{var i=new SymbolDef(this,e);i.undeclared=true;i.global=true;t.set(n,i);return i}}));re.DEFMETHOD("init_scope_vars",(function(e){this.variables=new Map;this.uses_with=false;this.uses_eval=false;this.parent_scope=e;this.enclosed=[];this.cname=-1}));re.DEFMETHOD("conflicting_def",(function(e){return this.enclosed.find((t=>t.name===e))||this.variables.has(e)||this.parent_scope&&this.parent_scope.conflicting_def(e)}));re.DEFMETHOD("conflicting_def_shallow",(function(e){return this.enclosed.find((t=>t.name===e))||this.variables.has(e)}));re.DEFMETHOD("add_child_scope",(function(e){if(e.parent_scope===this)return;e.parent_scope=this;const t=(()=>{const e=[];let t=this;do{e.push(t)}while(t=t.parent_scope);e.reverse();return e})();const n=new Set(e.enclosed);const i=[];for(const e of t){i.forEach((t=>push_uniq(e.enclosed,t)));for(const t of e.variables.values()){if(n.has(t)){push_uniq(i,t);push_uniq(e.enclosed,t)}}}}));function find_scopes_visible_from(e){const t=new Set;for(const n of new Set(e)){(function bubble_up(e){if(e==null||t.has(e))return;t.add(e);bubble_up(e.parent_scope)})(n)}return[...t]}re.DEFMETHOD("create_symbol",(function(e,{source:t,tentative_name:n,scope:i,conflict_scopes:r=[i],init:o=null}={}){let a;r=find_scopes_visible_from(r);if(n){n=a=n.replace(/(?:^[^a-z_$]|[^a-z0-9_$])/gi,"_");let e=0;while(r.find((e=>e.conflicting_def_shallow(a)))){a=n+"$"+e++}}if(!a){throw new Error("No symbol name could be generated in create_symbol()")}const s=make_node(e,t,{name:a,scope:i});this.def_variable(s,o||null);s.mark_enclosed();return s}));z.DEFMETHOD("is_block_scope",return_false);pt.DEFMETHOD("is_block_scope",return_false);se.DEFMETHOD("is_block_scope",return_false);oe.DEFMETHOD("is_block_scope",return_false);Ce.DEFMETHOD("is_block_scope",return_false);X.DEFMETHOD("is_block_scope",return_true);re.DEFMETHOD("is_block_scope",(function(){return this._block_scope||false}));$.DEFMETHOD("is_block_scope",return_true);se.DEFMETHOD("init_scope_vars",(function(){re.prototype.init_scope_vars.apply(this,arguments);this.uses_arguments=false;this.def_variable(new Tt({name:"arguments",start:this.start,end:this.end}))}));ce.DEFMETHOD("init_scope_vars",(function(){re.prototype.init_scope_vars.apply(this,arguments);this.uses_arguments=false}));gt.DEFMETHOD("mark_enclosed",(function(){var e=this.definition();var t=this.scope;while(t){push_uniq(t.enclosed,e);if(t===e.scope)break;t=t.parent_scope}}));gt.DEFMETHOD("reference",(function(){this.definition().references.push(this);this.mark_enclosed()}));re.DEFMETHOD("find_variable",(function(e){if(e instanceof gt)e=e.name;return this.variables.get(e)||this.parent_scope&&this.parent_scope.find_variable(e)}));re.DEFMETHOD("def_function",(function(e,t){var n=this.def_variable(e,t);if(!n.init||n.init instanceof fe)n.init=t;return n}));re.DEFMETHOD("def_variable",(function(e,t){var n=this.variables.get(e.name);if(n){n.orig.push(e);if(n.init&&(n.scope!==e.scope||n.init instanceof le)){n.init=t}}else{n=new SymbolDef(this,e,t);this.variables.set(e.name,n);n.global=!this.parent_scope}return e.thedef=n}));function next_mangled(e,t){let n;if(hn&&(n=e.get_defun_scope())&&hn.has(n)){e=n}var i=e.enclosed;var r=t.nth_identifier;e:while(true){var o=r.get(++e.cname);if(f.has(o))continue;if(t.reserved.has(o))continue;if(mn&&mn.has(o))continue e;for(let e=i.length;--e>=0;){const n=i[e];const r=n.mangled_name||n.unmangleable(t)&&n.name;if(o==r)continue e}return o}}re.DEFMETHOD("next_mangled",(function(e){return next_mangled(this,e)}));oe.DEFMETHOD("next_mangled",(function(e){let t;const n=this.mangled_names;do{t=next_mangled(this,e)}while(n.has(t));return t}));le.DEFMETHOD("next_mangled",(function(e,t){var n=t.orig[0]instanceof Tt&&this.name&&this.name.definition();var i=n?n.mangled_name||n.name:null;while(true){var r=next_mangled(this,e);if(!i||i!=r)return r}}));gt.DEFMETHOD("unmangleable",(function(e){var t=this.definition();return!t||t.unmangleable(e)}));It.DEFMETHOD("unmangleable",return_false);gt.DEFMETHOD("unreferenced",(function(){return!this.definition().references.length&&!this.scope.pinned()}));gt.DEFMETHOD("definition",(function(){return this.thedef}));gt.DEFMETHOD("global",(function(){return this.thedef.global}));oe.DEFMETHOD("_default_mangler_options",(function(e){e=defaults(e,{eval:false,nth_identifier:En,ie8:false,keep_classnames:false,keep_fnames:false,module:false,reserved:[],toplevel:false});if(e.module)e.toplevel=true;if(!Array.isArray(e.reserved)&&!(e.reserved instanceof Set)){e.reserved=[]}e.reserved=new Set(e.reserved);e.reserved.add("arguments");return e}));oe.DEFMETHOD("mangle_names",(function(e){e=this._default_mangler_options(e);var t=e.nth_identifier;var n=-1;var i=[];if(e.keep_fnames){dn=new Set}const r=this.mangled_names=new Set;mn=new Set;if(e.cache){this.globals.forEach(collect);if(e.cache.props){e.cache.props.forEach((function(e){r.add(e)}))}}var o=new TreeWalker((function(r,a){if(r instanceof j){var s=n;a();n=s;return true}if(r instanceof fe&&!(o.parent()instanceof re)){hn=hn||new Set;hn.add(r.parent_scope.get_defun_scope())}if(r instanceof re){r.variables.forEach(collect);return}if(r.is_block_scope()){r.block_scope.variables.forEach(collect);return}if(dn&&r instanceof Be&&r.value instanceof se&&!r.value.name&&keep_name(e.keep_fnames,r.name.name)){dn.add(r.name.definition().id);return}if(r instanceof It){let e;do{e=t.get(++n)}while(f.has(e));r.mangled_name=e;return true}if(!(e.ie8||e.safari10)&&r instanceof Mt){i.push(r.definition());return}}));this.walk(o);if(e.keep_fnames||e.keep_classnames){i.forEach((t=>{if(t.name.length<6&&t.unmangleable(e)){mn.add(t.name)}}))}i.forEach((t=>{t.mangle(e)}));dn=null;mn=null;hn=null;function collect(t){if(t.export&_n){mn.add(t.name)}else if(!e.reserved.has(t.name)){i.push(t)}}}));oe.DEFMETHOD("find_colliding_names",(function(e){const t=e.cache&&e.cache.props;const n=new Set;e.reserved.forEach(to_avoid);this.globals.forEach(add_def);this.walk(new TreeWalker((function(e){if(e instanceof re)e.variables.forEach(add_def);if(e instanceof Mt)add_def(e.definition())})));return n;function to_avoid(e){n.add(e)}function add_def(n){var i=n.name;if(n.global&&t&&t.has(i))i=t.get(i);else if(!n.unmangleable(e))return;to_avoid(i)}}));oe.DEFMETHOD("expand_names",(function(e){e=this._default_mangler_options(e);var t=e.nth_identifier;if(t.reset&&t.sort){t.reset();t.sort()}var n=this.find_colliding_names(e);var i=0;this.globals.forEach(rename);this.walk(new TreeWalker((function(e){if(e instanceof re)e.variables.forEach(rename);if(e instanceof Mt)rename(e.definition())})));function next_name(){var e;do{e=t.get(i++)}while(n.has(e)||f.has(e));return e}function rename(t){if(t.global&&e.cache)return;if(t.unmangleable(e))return;if(e.reserved.has(t.name))return;const n=redefined_catch_def(t);const i=t.name=n?n.name:next_name();t.orig.forEach((function(e){e.name=i}));t.references.forEach((function(e){e.name=i}))}}));z.DEFMETHOD("tail_node",return_this);He.DEFMETHOD("tail_node",(function(){return this.expressions[this.expressions.length-1]}));oe.DEFMETHOD("compute_char_frequency",(function(e){e=this._default_mangler_options(e);var t=e.nth_identifier;if(!t.reset||!t.consider||!t.sort){return}t.reset();try{z.prototype.print=function(n,i){this._print(n,i);if(this instanceof gt&&!this.unmangleable(e)){t.consider(this.name,-1)}else if(e.properties){if(this instanceof qe){t.consider("#"+this.property,-1)}else if(this instanceof We){t.consider(this.property,-1)}else if(this instanceof Ye){skip_string(this.property)}}};t.consider(this.print_to_string(),1)}finally{z.prototype.print=z.prototype._print}t.sort();function skip_string(e){if(e instanceof Gt){t.consider(e.value,-1)}else if(e instanceof et){skip_string(e.consequent);skip_string(e.alternative)}else if(e instanceof He){skip_string(e.tail_node())}}}));const En=(()=>{const e="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_".split("");const t="0123456789".split("");let n;let i;function reset(){i=new Map;e.forEach((function(e){i.set(e,0)}));t.forEach((function(e){i.set(e,0)}))}function consider(e,t){for(var n=e.length;--n>=0;){i.set(e[n],i.get(e[n])+t)}}function compare(e,t){return i.get(t)-i.get(e)}function sort(){n=mergeSort(e,compare).concat(mergeSort(t,compare))}reset();sort();function base54(e){var t="",i=54;e++;do{e--;t+=n[e%i];e=Math.floor(e/i);i=64}while(e>0);return t}return{get:base54,consider:consider,reset:reset,sort:sort}})();let gn=undefined;z.prototype.size=function(e,t){gn=e&&e.mangle_options;let n=0;walk_parent(this,((e,t)=>{n+=e._size(t);if(e instanceof ce&&e.is_braceless()){n+=e.body[0].value._size(t);return true}}),t||e&&e.stack);gn=undefined;return n};z.prototype._size=()=>0;K.prototype._size=()=>8;G.prototype._size=function(){return 2+this.value.length};const list_overhead=e=>e.length&&e.length-1;X.prototype._size=function(){return 2+list_overhead(this.body)};oe.prototype._size=function(){return list_overhead(this.body)};q.prototype._size=()=>1;j.prototype._size=()=>2;Q.prototype._size=()=>9;J.prototype._size=()=>7;ee.prototype._size=()=>8;te.prototype._size=()=>8;ie.prototype._size=()=>6;ae.prototype._size=()=>3;const lambda_modifiers=e=>(e.is_generator?1:0)+(e.async?6:0);ue.prototype._size=function(){return lambda_modifiers(this)+4+list_overhead(this.argnames)+list_overhead(this.body)};le.prototype._size=function(e){const t=!!first_in_statement(e);return t*2+lambda_modifiers(this)+12+list_overhead(this.argnames)+list_overhead(this.body)};fe.prototype._size=function(){return lambda_modifiers(this)+13+list_overhead(this.argnames)+list_overhead(this.body)};ce.prototype._size=function(){let e=2+list_overhead(this.argnames);if(!(this.argnames.length===1&&this.argnames[0]instanceof gt)){e+=2}const t=this.is_braceless()?0:list_overhead(this.body)+2;return lambda_modifiers(this)+e+t};_e.prototype._size=()=>2;de.prototype._size=function(){return 2+Math.floor(this.segments.length/2)*3};me.prototype._size=function(){return this.value.length};ge.prototype._size=function(){return this.value?7:6};ve.prototype._size=()=>6;be.prototype._size=function(){return this.label?6:5};ye.prototype._size=function(){return this.label?9:8};Te.prototype._size=()=>4;ke.prototype._size=function(){return 8+list_overhead(this.body)};Fe.prototype._size=function(){return 5+list_overhead(this.body)};Re.prototype._size=function(){return 8+list_overhead(this.body)};Oe.prototype._size=function(){return 3+list_overhead(this.body)};xe.prototype._size=function(){let e=7+list_overhead(this.body);if(this.argname){e+=2}return e};Me.prototype._size=function(){return 7+list_overhead(this.body)};Ne.prototype._size=function(){return 4+list_overhead(this.definitions)};Ie.prototype._size=function(){return 4+list_overhead(this.definitions)};Pe.prototype._size=function(){return 6+list_overhead(this.definitions)};Be.prototype._size=function(){return this.value?1:0};Le.prototype._size=function(){return this.name?4:0};Ve.prototype._size=function(){let e=6;if(this.imported_name)e+=1;if(this.imported_name||this.imported_names)e+=5;if(this.imported_names){e+=2+list_overhead(this.imported_names)}return e};ze.prototype._size=()=>11;Ue.prototype._size=function(){let e=7+(this.is_default?8:0);if(this.exported_value){e+=this.exported_value._size()}if(this.exported_names){e+=2+list_overhead(this.exported_names)}if(this.module_name){e+=5}return e};Ke.prototype._size=function(){if(this.optional){return 4+list_overhead(this.args)}return 2+list_overhead(this.args)};Ge.prototype._size=function(){return 6+list_overhead(this.args)};He.prototype._size=function(){return list_overhead(this.expressions)};We.prototype._size=function(){if(this.optional){return this.property.length+2}return this.property.length+1};qe.prototype._size=function(){if(this.optional){return this.property.length+3}return this.property.length+2};Ye.prototype._size=function(){return this.optional?4:2};$e.prototype._size=function(){if(this.operator==="typeof")return 7;if(this.operator==="void")return 5;return this.operator.length};Je.prototype._size=function(e){if(this.operator==="in")return 4;let t=this.operator.length;if((this.operator==="+"||this.operator==="-")&&this.right instanceof $e&&this.right.operator===this.operator){t+=1}if(this.needs_parens(e)){t+=2}return t};et.prototype._size=()=>3;it.prototype._size=function(){return 2+list_overhead(this.elements)};rt.prototype._size=function(e){let t=2;if(first_in_statement(e)){t+=2}return t+list_overhead(this.properties)};const key_size=e=>typeof e==="string"?e.length:0;at.prototype._size=function(){return key_size(this.key)+1};const static_size=e=>e?7:0;ct.prototype._size=function(){return 5+static_size(this.static)+key_size(this.key)};lt.prototype._size=function(){return 5+static_size(this.static)+key_size(this.key)};ft.prototype._size=function(){return static_size(this.static)+key_size(this.key)+lambda_modifiers(this)};_t.prototype._size=function(){return ft.prototype._size.call(this)+1};ut.prototype._size=st.prototype._size=function(){return ft.prototype._size.call(this)+4};pt.prototype._size=function(){return(this.name?8:7)+(this.extends?8:0)};dt.prototype._size=function(){return static_size(this.static)+(typeof this.key==="string"?this.key.length+2:0)+(this.value?1:0)};mt.prototype._size=function(){return dt.prototype._size.call(this)+1};gt.prototype._size=function(){return!gn||this.definition().unmangleable(gn)?this.name.length:1};Rt.prototype._size=function(){return this.name.length};Pt.prototype._size=Dt.prototype._size=function(){const{name:e,thedef:t}=this;if(t&&t.global)return e.length;if(e==="arguments")return 9;return gt.prototype._size.call(this)};vt.prototype._size=()=>10;Nt.prototype._size=function(){return this.name.length};Lt.prototype._size=function(){return this.name.length};zt.prototype._size=()=>4;Ut.prototype._size=()=>5;Gt.prototype._size=function(){return this.value.length+2};Ht.prototype._size=function(){const{value:e}=this;if(e===0)return 1;if(e>0&&Math.floor(e)===e){return Math.floor(Math.log10(e)+1)}return e.toString().length};Xt.prototype._size=function(){return this.value.length};Wt.prototype._size=function(){return this.value.toString().length};Yt.prototype._size=()=>4;jt.prototype._size=()=>3;$t.prototype._size=()=>6;Zt.prototype._size=()=>0;Qt.prototype._size=()=>8;tn.prototype._size=()=>4;en.prototype._size=()=>5;Se.prototype._size=()=>6;Ae.prototype._size=()=>6;const vn=1;const Dn=2;const bn=4;const yn=8;const Sn=16;const An=32;const Tn=256;const kn=512;const Cn=1024;const Rn=Tn|kn|Cn;const has_flag=(e,t)=>e.flags&t;const set_flag=(e,t)=>{e.flags|=t};const clear_flag=(e,t)=>{e.flags&=~t};function merge_sequence(e,t){if(t instanceof He){e.push(...t.expressions)}else{e.push(t)}return e}function make_sequence(e,t){if(t.length==1)return t[0];if(t.length==0)throw new Error("trying to create a sequence with length zero!");return make_node(He,e,{expressions:t.reduce(merge_sequence,[])})}function make_node_from_constant(e,t){switch(typeof e){case"string":return make_node(Gt,t,{value:e});case"number":if(isNaN(e))return make_node(jt,t);if(isFinite(e)){return 1/e<0?make_node(Ze,t,{operator:"-",expression:make_node(Ht,t,{value:-e})}):make_node(Ht,t,{value:e})}return e<0?make_node(Ze,t,{operator:"-",expression:make_node(Qt,t)}):make_node(Qt,t);case"boolean":return make_node(e?tn:en,t);case"undefined":return make_node($t,t);default:if(e===null){return make_node(Yt,t,{value:null})}if(e instanceof RegExp){return make_node(Wt,t,{value:{source:regexp_source_fix(e.source),flags:e.flags}})}throw new Error(string_template("Can't handle constant of type: {type}",{type:typeof e}))}}function best_of_expression(e,t){return e.size()>t.size()?t:e}function best_of_statement(e,t){return best_of_expression(make_node(H,e,{body:e}),make_node(H,t,{body:t})).body}function best_of(e,t,n){if(first_in_statement(e)){return best_of_statement(t,n)}else{return best_of_expression(t,n)}}function get_simple_key(e){if(e instanceof Kt){return e.getValue()}if(e instanceof Ze&&e.operator=="void"&&e.expression instanceof Kt){return}return e}function read_property(e,t){t=get_simple_key(t);if(t instanceof z)return;var n;if(e instanceof it){var i=e.elements;if(t=="length")return make_node_from_constant(i.length,e);if(typeof t=="number"&&t in i)n=i[t]}else if(e instanceof rt){t=""+t;var r=e.properties;for(var o=r.length;--o>=0;){var a=r[o];if(!(a instanceof at))return;if(!n&&r[o].key===t)n=r[o].value}}return n instanceof Pt&&n.fixed_value()||n}function has_break_or_continue(e,t){var n=false;var i=new TreeWalker((function(t){if(n||t instanceof re)return true;if(t instanceof De&&i.loopcontrol_target(t)===e){return n=true}}));if(t instanceof j)i.push(t);i.push(e);e.body.walk(i);return n}function maintain_this_binding(e,t,n){if(e instanceof Ze&&e.operator=="delete"||e instanceof Ke&&e.expression===t&&(n instanceof Xe||n instanceof Pt&&n.name=="eval")){const e=make_node(Ht,t,{value:0});return make_sequence(t,[e,n])}else{return n}}function is_func_expr(e){return e instanceof ce||e instanceof le}function is_iife_call(e){if(e.TYPE!="Call")return false;return e.expression instanceof le||is_iife_call(e.expression)}function is_empty(e){if(e===null)return true;if(e instanceof q)return true;if(e instanceof W)return e.body.length==0;return false}const Fn=makePredicate("Infinity NaN undefined");function is_identifier_atom(e){return e instanceof Qt||e instanceof jt||e instanceof $t}function is_ref_of(e,t){if(!(e instanceof Pt))return false;var n=e.definition().orig;for(var i=n.length;--i>=0;){if(n[i]instanceof t)return true}}function can_be_evicted_from_block(e){return!(e instanceof ht||e instanceof fe||e instanceof Ie||e instanceof Pe||e instanceof Ue||e instanceof Ve)}function as_statement_array(e){if(e===null)return[];if(e instanceof W)return e.body;if(e instanceof q)return[];if(e instanceof U)return[e];throw new Error("Can't convert thing to statement array")}function is_reachable(e,t){const find_ref=e=>{if(e instanceof Pt&&t.includes(e.definition())){return nn}};return walk_parent(e,((t,n)=>{if(t instanceof re&&t!==e){var i=n.parent();if(i instanceof Ke&&i.expression===t&&!(t.async||t.is_generator)){return}if(walk(t,find_ref))return nn;return true}}))}function is_recursive_ref(e,t){var n;for(var i=0;n=e.parent(i);i++){if(n instanceof se||n instanceof pt){var r=n.name;if(r&&r.definition()===t){return true}}}return false}function retain_top_func(e,t){return t.top_retain&&e instanceof fe&&has_flag(e,Cn)&&e.name&&t.top_retain(e.name)}function make_nested_lookup(e){const t=new Map;for(var n of Object.keys(e)){t.set(n,makePredicate(e[n]))}const does_have=(e,n)=>{const i=t.get(e);return i!=null&&i.has(n)};return does_have}const On=new Set(["Number","String","Array","Object","Function","Promise"]);const xn=["constructor","toString","valueOf"];const Mn=make_nested_lookup({Array:["indexOf","join","lastIndexOf","slice",...xn],Boolean:xn,Function:xn,Number:["toExponential","toFixed","toPrecision",...xn],Object:xn,RegExp:["test",...xn],String:["charAt","charCodeAt","concat","indexOf","italics","lastIndexOf","match","replace","search","slice","split","substr","substring","toLowerCase","toUpperCase","trim",...xn]});const wn=make_nested_lookup({Array:["isArray"],Math:["abs","acos","asin","atan","ceil","cos","exp","floor","log","round","sin","sqrt","tan","atan2","pow","max","min"],Number:["isFinite","isNaN"],Object:["create","getOwnPropertyDescriptor","getOwnPropertyNames","getPrototypeOf","isExtensible","isFrozen","isSealed","hasOwn","keys"],String:["fromCharCode"]});const Nn=make_nested_lookup({Math:["E","LN10","LN2","LOG2E","LOG10E","PI","SQRT1_2","SQRT2"],Number:["MAX_VALUE","MIN_VALUE","NaN","NEGATIVE_INFINITY","POSITIVE_INFINITY"]});const is_undeclared_ref=e=>e instanceof Pt&&e.definition().undeclared;const In=makePredicate("&& || ??");const Pn=makePredicate("delete ++ --");(function(e){const t=makePredicate("! delete");const n=makePredicate("in instanceof == != === !== < <= >= >");e(z,return_false);e(Ze,(function(){return t.has(this.operator)}));e(Je,(function(){return n.has(this.operator)||In.has(this.operator)&&this.left.is_boolean()&&this.right.is_boolean()}));e(et,(function(){return this.consequent.is_boolean()&&this.alternative.is_boolean()}));e(tt,(function(){return this.operator=="="&&this.right.is_boolean()}));e(He,(function(){return this.tail_node().is_boolean()}));e(tn,return_true);e(en,return_true)})((function(e,t){e.DEFMETHOD("is_boolean",t)}));(function(e){e(z,return_false);e(Ht,return_true);const t=makePredicate("+ - ~ ++ --");e($e,(function(){return t.has(this.operator)}));const n=makePredicate("- * / % & | ^ << >> >>>");e(Je,(function(e){return n.has(this.operator)||this.operator=="+"&&this.left.is_number(e)&&this.right.is_number(e)}));e(tt,(function(e){return n.has(this.operator.slice(0,-1))||this.operator=="="&&this.right.is_number(e)}));e(He,(function(e){return this.tail_node().is_number(e)}));e(et,(function(e){return this.consequent.is_number(e)&&this.alternative.is_number(e)}))})((function(e,t){e.DEFMETHOD("is_number",t)}));(function(e){e(z,return_false);e(Gt,return_true);e(de,return_true);e(Ze,(function(){return this.operator=="typeof"}));e(Je,(function(e){return this.operator=="+"&&(this.left.is_string(e)||this.right.is_string(e))}));e(tt,(function(e){return(this.operator=="="||this.operator=="+=")&&this.right.is_string(e)}));e(He,(function(e){return this.tail_node().is_string(e)}));e(et,(function(e){return this.consequent.is_string(e)&&this.alternative.is_string(e)}))})((function(e,t){e.DEFMETHOD("is_string",t)}));function is_undefined(e,t){return has_flag(e,yn)||e instanceof $t||e instanceof Ze&&e.operator=="void"&&!e.expression.has_side_effects(t)}function is_null_or_undefined(e,t){let n;return e instanceof Yt||is_undefined(e,t)||e instanceof Pt&&(n=e.definition().fixed)instanceof z&&is_nullish(n,t)}function is_nullish_shortcircuited(e,t){if(e instanceof Xe||e instanceof Ke){return e.optional&&is_null_or_undefined(e.expression,t)||is_nullish_shortcircuited(e.expression,t)}if(e instanceof je)return is_nullish_shortcircuited(e.expression,t);return false}function is_nullish(e,t){if(is_null_or_undefined(e,t))return true;return is_nullish_shortcircuited(e,t)}(function(e){e(z,return_true);e(q,return_false);e(Kt,return_false);e(zt,return_false);function any(e,t){for(var n=e.length;--n>=0;)if(e[n].has_side_effects(t))return true;return false}e(X,(function(e){return any(this.body,e)}));e(Ke,(function(e){if(!this.is_callee_pure(e)&&(!this.expression.is_call_pure(e)||this.expression.has_side_effects(e))){return true}return any(this.args,e)}));e(ke,(function(e){return this.expression.has_side_effects(e)||any(this.body,e)}));e(Fe,(function(e){return this.expression.has_side_effects(e)||any(this.body,e)}));e(Oe,(function(e){return any(this.body,e)||this.bcatch&&this.bcatch.has_side_effects(e)||this.bfinally&&this.bfinally.has_side_effects(e)}));e(Te,(function(e){return this.condition.has_side_effects(e)||this.body&&this.body.has_side_effects(e)||this.alternative&&this.alternative.has_side_effects(e)}));e(j,(function(e){return this.body.has_side_effects(e)}));e(H,(function(e){return this.body.has_side_effects(e)}));e(se,return_false);e(pt,(function(e){if(this.extends&&this.extends.has_side_effects(e)){return true}return any(this.properties,e)}));e(Je,(function(e){return this.left.has_side_effects(e)||this.right.has_side_effects(e)}));e(tt,return_true);e(et,(function(e){return this.condition.has_side_effects(e)||this.consequent.has_side_effects(e)||this.alternative.has_side_effects(e)}));e($e,(function(e){return Pn.has(this.operator)||this.expression.has_side_effects(e)}));e(Pt,(function(e){return!this.is_declared(e)&&!On.has(this.name)}));e(Rt,return_false);e(Dt,return_false);e(rt,(function(e){return any(this.properties,e)}));e(ot,(function(e){return this.computed_key()&&this.key.has_side_effects(e)||this.value&&this.value.has_side_effects(e)}));e(dt,(function(e){return this.computed_key()&&this.key.has_side_effects(e)||this.static&&this.value&&this.value.has_side_effects(e)}));e(ft,(function(e){return this.computed_key()&&this.key.has_side_effects(e)}));e(ct,(function(e){return this.computed_key()&&this.key.has_side_effects(e)}));e(lt,(function(e){return this.computed_key()&&this.key.has_side_effects(e)}));e(it,(function(e){return any(this.elements,e)}));e(We,(function(e){if(is_nullish(this,e))return false;return!this.optional&&this.expression.may_throw_on_access(e)||this.expression.has_side_effects(e)}));e(Ye,(function(e){if(is_nullish(this,e))return false;return!this.optional&&this.expression.may_throw_on_access(e)||this.expression.has_side_effects(e)||this.property.has_side_effects(e)}));e(je,(function(e){return this.expression.has_side_effects(e)}));e(He,(function(e){return any(this.expressions,e)}));e(we,(function(e){return any(this.definitions,e)}));e(Be,(function(){return this.value}));e(me,return_false);e(de,(function(e){return any(this.segments,e)}))})((function(e,t){e.DEFMETHOD("has_side_effects",t)}));(function(e){e(z,return_true);e(Kt,return_false);e(q,return_false);e(se,return_false);e(Dt,return_false);e(zt,return_false);function any(e,t){for(var n=e.length;--n>=0;)if(e[n].may_throw(t))return true;return false}e(pt,(function(e){if(this.extends&&this.extends.may_throw(e))return true;return any(this.properties,e)}));e(it,(function(e){return any(this.elements,e)}));e(tt,(function(e){if(this.right.may_throw(e))return true;if(!e.has_directive("use strict")&&this.operator=="="&&this.left instanceof Pt){return false}return this.left.may_throw(e)}));e(Je,(function(e){return this.left.may_throw(e)||this.right.may_throw(e)}));e(X,(function(e){return any(this.body,e)}));e(Ke,(function(e){if(is_nullish(this,e))return false;if(any(this.args,e))return true;if(this.is_callee_pure(e))return false;if(this.expression.may_throw(e))return true;return!(this.expression instanceof se)||any(this.expression.body,e)}));e(Fe,(function(e){return this.expression.may_throw(e)||any(this.body,e)}));e(et,(function(e){return this.condition.may_throw(e)||this.consequent.may_throw(e)||this.alternative.may_throw(e)}));e(we,(function(e){return any(this.definitions,e)}));e(Te,(function(e){return this.condition.may_throw(e)||this.body&&this.body.may_throw(e)||this.alternative&&this.alternative.may_throw(e)}));e(j,(function(e){return this.body.may_throw(e)}));e(rt,(function(e){return any(this.properties,e)}));e(ot,(function(e){return this.value?this.value.may_throw(e):false}));e(dt,(function(e){return this.computed_key()&&this.key.may_throw(e)||this.static&&this.value&&this.value.may_throw(e)}));e(ft,(function(e){return this.computed_key()&&this.key.may_throw(e)}));e(ct,(function(e){return this.computed_key()&&this.key.may_throw(e)}));e(lt,(function(e){return this.computed_key()&&this.key.may_throw(e)}));e(ge,(function(e){return this.value&&this.value.may_throw(e)}));e(He,(function(e){return any(this.expressions,e)}));e(H,(function(e){return this.body.may_throw(e)}));e(We,(function(e){if(is_nullish(this,e))return false;return!this.optional&&this.expression.may_throw_on_access(e)||this.expression.may_throw(e)}));e(Ye,(function(e){if(is_nullish(this,e))return false;return!this.optional&&this.expression.may_throw_on_access(e)||this.expression.may_throw(e)||this.property.may_throw(e)}));e(je,(function(e){return this.expression.may_throw(e)}));e(ke,(function(e){return this.expression.may_throw(e)||any(this.body,e)}));e(Pt,(function(e){return!this.is_declared(e)&&!On.has(this.name)}));e(Rt,return_false);e(Oe,(function(e){return this.bcatch?this.bcatch.may_throw(e):any(this.body,e)||this.bfinally&&this.bfinally.may_throw(e)}));e($e,(function(e){if(this.operator=="typeof"&&this.expression instanceof Pt)return false;return this.expression.may_throw(e)}));e(Be,(function(e){if(!this.value)return false;return this.value.may_throw(e)}))})((function(e,t){e.DEFMETHOD("may_throw",t)}));(function(e){function all_refs_local(e){let t=true;walk(this,(n=>{if(n instanceof Pt){if(has_flag(this,Sn)){t=false;return nn}var i=n.definition();if(member(i,this.enclosed)&&!this.variables.has(i.name)){if(e){var r=e.find_variable(n);if(i.undeclared?!r:r===i){t="f";return true}}t=false;return nn}return true}if(n instanceof zt&&this instanceof ce){t=false;return nn}}));return t}e(z,return_false);e(Kt,return_true);e(pt,(function(e){if(this.extends&&!this.extends.is_constant_expression(e)){return false}for(const t of this.properties){if(t.computed_key()&&!t.key.is_constant_expression(e)){return false}if(t.static&&t.value&&!t.value.is_constant_expression(e)){return false}}return all_refs_local.call(this,e)}));e(se,all_refs_local);e($e,(function(){return this.expression.is_constant_expression()}));e(Je,(function(){return this.left.is_constant_expression()&&this.right.is_constant_expression()}));e(it,(function(){return this.elements.every((e=>e.is_constant_expression()))}));e(rt,(function(){return this.properties.every((e=>e.is_constant_expression()))}));e(ot,(function(){return!!(!(this.key instanceof z)&&this.value&&this.value.is_constant_expression())}))})((function(e,t){e.DEFMETHOD("is_constant_expression",t)}));(function(e){z.DEFMETHOD("may_throw_on_access",(function(e){return!e.option("pure_getters")||this._dot_throw(e)}));function is_strict(e){return/strict/.test(e.option("pure_getters"))}e(z,is_strict);e(Yt,return_true);e($t,return_true);e(Kt,return_false);e(it,return_false);e(rt,(function(e){if(!is_strict(e))return false;for(var t=this.properties.length;--t>=0;)if(this.properties[t]._dot_throw(e))return true;return false}));e(pt,return_false);e(ot,return_false);e(ct,return_true);e(ae,(function(e){return this.expression._dot_throw(e)}));e(le,return_false);e(ce,return_false);e(Qe,return_false);e(Ze,(function(){return this.operator=="void"}));e(Je,(function(e){return(this.operator=="&&"||this.operator=="||"||this.operator=="??")&&(this.left._dot_throw(e)||this.right._dot_throw(e))}));e(tt,(function(e){if(this.logical)return true;return this.operator=="="&&this.right._dot_throw(e)}));e(et,(function(e){return this.consequent._dot_throw(e)||this.alternative._dot_throw(e)}));e(We,(function(e){if(!is_strict(e))return false;if(this.property=="prototype"){return!(this.expression instanceof le||this.expression instanceof pt)}return true}));e(je,(function(e){return this.expression._dot_throw(e)}));e(He,(function(e){return this.tail_node()._dot_throw(e)}));e(Pt,(function(e){if(this.name==="arguments")return false;if(has_flag(this,yn))return true;if(!is_strict(e))return false;if(is_undeclared_ref(this)&&this.is_declared(e))return false;if(this.is_immutable())return false;var t=this.fixed_value();return!t||t._dot_throw(e)}))})((function(e,t){e.DEFMETHOD("_dot_throw",t)}));function is_lhs(e,t){if(t instanceof $e&&Pn.has(t.operator))return t.expression;if(t instanceof tt&&t.left===e)return e}(function(e){function to_node(e,t){if(e instanceof z){if(!(e instanceof Kt)){e=e.clone(true)}return make_node(e.CTOR,t,e)}if(Array.isArray(e))return make_node(it,t,{elements:e.map((function(e){return to_node(e,t)}))});if(e&&typeof e=="object"){var n=[];for(var i in e)if(HOP(e,i)){n.push(make_node(at,t,{key:i,value:to_node(e[i],t)}))}return make_node(rt,t,{properties:n})}return make_node_from_constant(e,t)}oe.DEFMETHOD("resolve_defines",(function(e){if(!e.option("global_defs"))return this;this.figure_out_scope({ie8:e.option("ie8")});return this.transform(new TreeTransformer((function(t){var n=t._find_defs(e,"");if(!n)return;var i=0,r=t,o;while(o=this.parent(i++)){if(!(o instanceof Xe))break;if(o.expression!==r)break;r=o}if(is_lhs(r,o)){return}return n})))}));e(z,noop);e(je,(function(e,t){return this.expression._find_defs(e,t)}));e(We,(function(e,t){return this.expression._find_defs(e,"."+this.property+t)}));e(Dt,(function(){if(!this.global())return}));e(Pt,(function(e,t){if(!this.global())return;var n=e.option("global_defs");var i=this.name+t;if(HOP(n,i))return to_node(n[i],this)}))})((function(e,t){e.DEFMETHOD("_find_defs",t)}));(function(e){function basic_negation(e){return make_node(Ze,e,{operator:"!",expression:e})}function best(e,t,n){var i=basic_negation(e);if(n){var r=make_node(H,t,{body:t});return best_of_expression(i,r)===r?t:i}return best_of_expression(i,t)}e(z,(function(){return basic_negation(this)}));e(U,(function(){throw new Error("Cannot negate a statement")}));e(le,(function(){return basic_negation(this)}));e(ce,(function(){return basic_negation(this)}));e(Ze,(function(){if(this.operator=="!")return this.expression;return basic_negation(this)}));e(He,(function(e){var t=this.expressions.slice();t.push(t.pop().negate(e));return make_sequence(this,t)}));e(et,(function(e,t){var n=this.clone();n.consequent=n.consequent.negate(e);n.alternative=n.alternative.negate(e);return best(this,n,t)}));e(Je,(function(e,t){var n=this.clone(),i=this.operator;if(e.option("unsafe_comps")){switch(i){case"<=":n.operator=">";return n;case"<":n.operator=">=";return n;case">=":n.operator="<";return n;case">":n.operator="<=";return n}}switch(i){case"==":n.operator="!=";return n;case"!=":n.operator="==";return n;case"===":n.operator="!==";return n;case"!==":n.operator="===";return n;case"&&":n.operator="||";n.left=n.left.negate(e,t);n.right=n.right.negate(e);return best(this,n,t);case"||":n.operator="&&";n.left=n.left.negate(e,t);n.right=n.right.negate(e);return best(this,n,t)}return basic_negation(this)}))})((function(e,t){e.DEFMETHOD("negate",(function(e,n){return t.call(this,e,n)}))}));var Bn=makePredicate("Boolean decodeURI decodeURIComponent Date encodeURI encodeURIComponent Error escape EvalError isFinite isNaN Number Object parseFloat parseInt RangeError ReferenceError String SyntaxError TypeError unescape URIError");Ke.DEFMETHOD("is_callee_pure",(function(e){if(e.option("unsafe")){var t=this.expression;var n=this.args&&this.args[0]&&this.args[0].evaluate(e);if(t.expression&&t.expression.name==="hasOwnProperty"&&(n==null||n.thedef&&n.thedef.undeclared)){return false}if(is_undeclared_ref(t)&&Bn.has(t.name))return true;if(t instanceof We&&is_undeclared_ref(t.expression)&&wn(t.expression.name,t.property)){return true}}return!!has_annotation(this,rn)||!e.pure_funcs(this)}));z.DEFMETHOD("is_call_pure",return_false);We.DEFMETHOD("is_call_pure",(function(e){if(!e.option("unsafe"))return;const t=this.expression;let n;if(t instanceof it){n="Array"}else if(t.is_boolean()){n="Boolean"}else if(t.is_number(e)){n="Number"}else if(t instanceof Wt){n="RegExp"}else if(t.is_string(e)){n="String"}else if(!this.may_throw_on_access(e)){n="Object"}return n!=null&&Mn(n,this.property)}));const aborts=e=>e&&e.aborts();(function(e){e(U,return_null);e(he,return_this);function block_aborts(){for(var e=0;e<this.body.length;e++){if(aborts(this.body[e])){return this.body[e]}}return null}e(Ve,(function(){return null}));e(W,block_aborts);e(Ce,block_aborts);e(Te,(function(){return this.alternative&&aborts(this.body)&&aborts(this.alternative)&&this}))})((function(e,t){e.DEFMETHOD("aborts",t)}));function is_modified(e,t,n,i,r,o){var a=t.parent(r);var s=is_lhs(n,a);if(s)return s;if(!o&&a instanceof Ke&&a.expression===n&&!(i instanceof ce)&&!(i instanceof pt)&&!a.is_callee_pure(e)&&(!(i instanceof le)||!(a instanceof Ge)&&i.contains_this())){return true}if(a instanceof it){return is_modified(e,t,a,a,r+1)}if(a instanceof at&&n===a.value){var u=t.parent(r+1);return is_modified(e,t,u,u,r+2)}if(a instanceof Xe&&a.expression===n){var l=read_property(i,a.property);return!o&&is_modified(e,t,a,l,r+1)}}function def_eval(e,t){e.DEFMETHOD("_eval",t)}const Ln=Symbol("This AST_Chain is nullish");z.DEFMETHOD("evaluate",(function(e){if(!e.option("evaluate"))return this;var t=this._eval(e,1);if(!t||t instanceof RegExp)return t;if(typeof t=="function"||typeof t=="object"||t==Ln)return this;return t}));var Vn=makePredicate("! ~ - + void");z.DEFMETHOD("is_constant",(function(){if(this instanceof Kt){return!(this instanceof Wt)}else{return this instanceof Ze&&this.expression instanceof Kt&&Vn.has(this.operator)}}));def_eval(U,(function(){throw new Error(string_template("Cannot evaluate a statement [{file}:{line},{col}]",this.start))}));def_eval(se,return_this);def_eval(pt,return_this);def_eval(z,return_this);def_eval(Kt,(function(){return this.getValue()}));def_eval(Xt,return_this);def_eval(Wt,(function(e){let t=e.evaluated_regexps.get(this.value);if(t===undefined){try{const{source:e,flags:n}=this.value;t=new RegExp(e,n)}catch(e){t=null}e.evaluated_regexps.set(this.value,t)}return t||this}));def_eval(de,(function(){if(this.segments.length!==1)return this;return this.segments[0].value}));def_eval(le,(function(e){if(e.option("unsafe")){var fn=function(){};fn.node=this;fn.toString=()=>this.print_to_string();return fn}return this}));def_eval(it,(function(e,t){if(e.option("unsafe")){var n=[];for(var i=0,r=this.elements.length;i<r;i++){var o=this.elements[i];var a=o._eval(e,t);if(o===a)return this;n.push(a)}return n}return this}));def_eval(rt,(function(e,t){if(e.option("unsafe")){var n={};for(var i=0,r=this.properties.length;i<r;i++){var o=this.properties[i];if(o instanceof ae)return this;var a=o.key;if(a instanceof gt){a=a.name}else if(a instanceof z){a=a._eval(e,t);if(a===o.key)return this}if(typeof Object.prototype[a]==="function"){return this}if(o.value instanceof le)continue;n[a]=o.value._eval(e,t);if(n[a]===o.value)return this}return n}return this}));var zn=makePredicate("! typeof void");def_eval(Ze,(function(e,t){var n=this.expression;if(e.option("typeofs")&&this.operator=="typeof"&&(n instanceof se||n instanceof Pt&&n.fixed_value()instanceof se)){return typeof function(){}}if(!zn.has(this.operator))t++;n=n._eval(e,t);if(n===this.expression)return this;switch(this.operator){case"!":return!n;case"typeof":if(n instanceof RegExp)return this;return typeof n;case"void":return void n;case"~":return~n;case"-":return-n;case"+":return+n}return this}));var Un=makePredicate("&& || ?? === !==");const Kn=makePredicate("== != === !==");const has_identity=e=>typeof e==="object"||typeof e==="function"||typeof e==="symbol";def_eval(Je,(function(e,t){if(!Un.has(this.operator))t++;var n=this.left._eval(e,t);if(n===this.left)return this;var i=this.right._eval(e,t);if(i===this.right)return this;var r;if(n!=null&&i!=null&&Kn.has(this.operator)&&has_identity(n)&&has_identity(i)&&typeof n===typeof i){return this}switch(this.operator){case"&&":r=n&&i;break;case"||":r=n||i;break;case"??":r=n!=null?n:i;break;case"|":r=n|i;break;case"&":r=n&i;break;case"^":r=n^i;break;case"+":r=n+i;break;case"*":r=n*i;break;case"**":r=Math.pow(n,i);break;case"/":r=n/i;break;case"%":r=n%i;break;case"-":r=n-i;break;case"<<":r=n<<i;break;case">>":r=n>>i;break;case">>>":r=n>>>i;break;case"==":r=n==i;break;case"===":r=n===i;break;case"!=":r=n!=i;break;case"!==":r=n!==i;break;case"<":r=n<i;break;case"<=":r=n<=i;break;case">":r=n>i;break;case">=":r=n>=i;break;default:return this}if(isNaN(r)&&e.find_parent(ie)){return this}return r}));def_eval(et,(function(e,t){var n=this.condition._eval(e,t);if(n===this.condition)return this;var i=n?this.consequent:this.alternative;var r=i._eval(e,t);return r===i?this:r}));const Gn=new Set;def_eval(Pt,(function(e,t){if(Gn.has(this))return this;var n=this.fixed_value();if(!n)return this;Gn.add(this);const i=n._eval(e,t);Gn.delete(this);if(i===n)return this;if(i&&typeof i=="object"){var r=this.definition().escaped;if(r&&t>r)return this}return i}));const Hn={Array:Array,Math:Math,Number:Number,Object:Object,String:String};const Xn=new Set(["dotAll","global","ignoreCase","multiline","sticky","unicode"]);def_eval(Xe,(function(e,t){const n=this.expression._eval(e,t);if(n===Ln||this.optional&&n==null)return Ln;if(e.option("unsafe")){var i=this.property;if(i instanceof z){i=i._eval(e,t);if(i===this.property)return this}var r=this.expression;var o;if(is_undeclared_ref(r)){var a;var s=r.name==="hasOwnProperty"&&i==="call"&&(a=e.parent()&&e.parent().args)&&(a&&a[0]&&a[0].evaluate(e));s=s instanceof We?s.expression:s;if(s==null||s.thedef&&s.thedef.undeclared){return this.clone()}if(!Nn(r.name,i))return this;o=Hn[r.name]}else{o=r._eval(e,t+1);if(o instanceof RegExp){if(i=="source"){return regexp_source_fix(o.source)}else if(i=="flags"||Xn.has(i)){return o[i]}}if(!o||o===r||!HOP(o,i))return this;if(typeof o=="function")switch(i){case"name":return o.node.name?o.node.name.name:"";case"length":return o.node.length_property();default:return this}}return o[i]}return this}));def_eval(je,(function(e,t){const n=this.expression._eval(e,t);return n===Ln?undefined:n===this.expression?this:n}));def_eval(Ke,(function(e,t){var n=this.expression;const i=n._eval(e,t);if(i===Ln||this.optional&&i==null)return Ln;if(e.option("unsafe")&&n instanceof Xe){var r=n.property;if(r instanceof z){r=r._eval(e,t);if(r===n.property)return this}var o;var a=n.expression;if(is_undeclared_ref(a)){var s=a.name==="hasOwnProperty"&&r==="call"&&(this.args[0]&&this.args[0].evaluate(e));s=s instanceof We?s.expression:s;if(s==null||s.thedef&&s.thedef.undeclared){return this.clone()}if(!wn(a.name,r))return this;o=Hn[a.name]}else{o=a._eval(e,t+1);if(o===a||!o)return this;if(!Mn(o.constructor.name,r))return this}var u=[];for(var l=0,c=this.args.length;l<c;l++){var f=this.args[l];var _=f._eval(e,t);if(f===_)return this;if(f instanceof se)return this;u.push(_)}try{return o[r].apply(o,u)}catch(e){}}return this}));def_eval(Ge,return_this);function def_drop_side_effect_free(e,t){e.DEFMETHOD("drop_side_effect_free",t)}function trim(e,t,n){var i=e.length;if(!i)return null;var r=[],o=false;for(var a=0;a<i;a++){var s=e[a].drop_side_effect_free(t,n);o|=s!==e[a];if(s){r.push(s);n=false}}return o?r.length?r:null:e}def_drop_side_effect_free(z,return_this);def_drop_side_effect_free(Kt,return_null);def_drop_side_effect_free(zt,return_null);def_drop_side_effect_free(Ke,(function(e,t){if(is_nullish_shortcircuited(this,e)){return this.expression.drop_side_effect_free(e,t)}if(!this.is_callee_pure(e)){if(this.expression.is_call_pure(e)){var n=this.args.slice();n.unshift(this.expression.expression);n=trim(n,e,t);return n&&make_sequence(this,n)}if(is_func_expr(this.expression)&&(!this.expression.name||!this.expression.name.definition().references.length)){var i=this.clone();i.expression.process_expression(false,e);return i}return this}var r=trim(this.args,e,t);return r&&make_sequence(this,r)}));def_drop_side_effect_free(ue,return_null);def_drop_side_effect_free(le,return_null);def_drop_side_effect_free(ce,return_null);def_drop_side_effect_free(pt,(function(e){const t=[];const n=this.extends&&this.extends.drop_side_effect_free(e);if(n)t.push(n);for(const n of this.properties){const i=n.drop_side_effect_free(e);if(i)t.push(i)}if(!t.length)return null;return make_sequence(this,t)}));def_drop_side_effect_free(Je,(function(e,t){var n=this.right.drop_side_effect_free(e);if(!n)return this.left.drop_side_effect_free(e,t);if(In.has(this.operator)){if(n===this.right)return this;var i=this.clone();i.right=n;return i}else{var r=this.left.drop_side_effect_free(e,t);if(!r)return this.right.drop_side_effect_free(e,t);return make_sequence(this,[r,n])}}));def_drop_side_effect_free(tt,(function(e){if(this.logical)return this;var t=this.left;if(t.has_side_effects(e)||e.has_directive("use strict")&&t instanceof Xe&&t.expression.is_constant()){return this}set_flag(this,An);while(t instanceof Xe){t=t.expression}if(t.is_constant_expression(e.find_parent(re))){return this.right.drop_side_effect_free(e)}return this}));def_drop_side_effect_free(et,(function(e){var t=this.consequent.drop_side_effect_free(e);var n=this.alternative.drop_side_effect_free(e);if(t===this.consequent&&n===this.alternative)return this;if(!t)return n?make_node(Je,this,{operator:"||",left:this.condition,right:n}):this.condition.drop_side_effect_free(e);if(!n)return make_node(Je,this,{operator:"&&",left:this.condition,right:t});var i=this.clone();i.consequent=t;i.alternative=n;return i}));def_drop_side_effect_free($e,(function(e,t){if(Pn.has(this.operator)){if(!this.expression.has_side_effects(e)){set_flag(this,An)}else{clear_flag(this,An)}return this}if(this.operator=="typeof"&&this.expression instanceof Pt)return null;var n=this.expression.drop_side_effect_free(e,t);if(t&&n&&is_iife_call(n)){if(n===this.expression&&this.operator=="!")return this;return n.negate(e,t)}return n}));def_drop_side_effect_free(Pt,(function(e){const t=this.is_declared(e)||On.has(this.name);return t?null:this}));def_drop_side_effect_free(rt,(function(e,t){var n=trim(this.properties,e,t);return n&&make_sequence(this,n)}));def_drop_side_effect_free(ot,(function(e,t){const n=this instanceof at&&this.key instanceof z;const i=n&&this.key.drop_side_effect_free(e,t);const r=this.value&&this.value.drop_side_effect_free(e,t);if(i&&r){return make_sequence(this,[i,r])}return i||r}));def_drop_side_effect_free(dt,(function(e){const t=this.computed_key()&&this.key.drop_side_effect_free(e);const n=this.static&&this.value&&this.value.drop_side_effect_free(e);if(t&&n)return make_sequence(this,[t,n]);return t||n||null}));def_drop_side_effect_free(ft,(function(){return this.computed_key()?this.key:null}));def_drop_side_effect_free(ct,(function(){return this.computed_key()?this.key:null}));def_drop_side_effect_free(lt,(function(){return this.computed_key()?this.key:null}));def_drop_side_effect_free(it,(function(e,t){var n=trim(this.elements,e,t);return n&&make_sequence(this,n)}));def_drop_side_effect_free(We,(function(e,t){if(is_nullish_shortcircuited(this,e)){return this.expression.drop_side_effect_free(e,t)}if(this.expression.may_throw_on_access(e))return this;return this.expression.drop_side_effect_free(e,t)}));def_drop_side_effect_free(Ye,(function(e,t){if(is_nullish_shortcircuited(this,e)){return this.expression.drop_side_effect_free(e,t)}if(this.expression.may_throw_on_access(e))return this;var n=this.expression.drop_side_effect_free(e,t);if(!n)return this.property.drop_side_effect_free(e,t);var i=this.property.drop_side_effect_free(e);if(!i)return n;return make_sequence(this,[n,i])}));def_drop_side_effect_free(je,(function(e,t){return this.expression.drop_side_effect_free(e,t)}));def_drop_side_effect_free(He,(function(e){var t=this.tail_node();var n=t.drop_side_effect_free(e);if(n===t)return this;var i=this.expressions.slice(0,-1);if(n)i.push(n);if(!i.length){return make_node(Ht,this,{value:0})}return make_sequence(this,i)}));def_drop_side_effect_free(ae,(function(e,t){return this.expression.drop_side_effect_free(e,t)}));def_drop_side_effect_free(me,return_null);def_drop_side_effect_free(de,(function(e){var t=trim(this.segments,e,first_in_statement);return t&&make_sequence(this,t)}));function def_reduce_vars(e,t){e.DEFMETHOD("reduce_vars",t)}def_reduce_vars(z,noop);function reset_def(e,t){t.assignments=0;t.chained=false;t.direct_access=false;t.escaped=0;t.recursive_refs=0;t.references=[];t.single_use=undefined;if(t.scope.pinned()){t.fixed=false}else if(t.orig[0]instanceof St||!e.exposed(t)){t.fixed=t.init}else{t.fixed=false}}function reset_variables(e,t,n){n.variables.forEach((function(n){reset_def(t,n);if(n.fixed===null){e.defs_to_safe_ids.set(n.id,e.safe_ids);mark(e,n,true)}else if(n.fixed){e.loop_ids.set(n.id,e.in_loop);mark(e,n,true)}}))}function reset_block_variables(e,t){if(t.block_scope)t.block_scope.variables.forEach((t=>{reset_def(e,t)}))}function push(e){e.safe_ids=Object.create(e.safe_ids)}function pop(e){e.safe_ids=Object.getPrototypeOf(e.safe_ids)}function mark(e,t,n){e.safe_ids[t.id]=n}function safe_to_read(e,t){if(t.single_use=="m")return false;if(e.safe_ids[t.id]){if(t.fixed==null){var n=t.orig[0];if(n instanceof Tt||n.name=="arguments")return false;t.fixed=make_node($t,n)}return true}return t.fixed instanceof fe}function safe_to_assign(e,t,n,i){if(t.fixed===undefined)return true;let r;if(t.fixed===null&&(r=e.defs_to_safe_ids.get(t.id))){r[t.id]=false;e.defs_to_safe_ids.delete(t.id);return true}if(!HOP(e.safe_ids,t.id))return false;if(!safe_to_read(e,t))return false;if(t.fixed===false)return false;if(t.fixed!=null&&(!i||t.references.length>t.assignments))return false;if(t.fixed instanceof fe){return i instanceof z&&t.fixed.parent_scope===n}return t.orig.every((e=>!(e instanceof St||e instanceof kt||e instanceof Ft)))}function ref_once(e,t,n){return t.option("unused")&&!n.scope.pinned()&&n.references.length-n.recursive_refs==1&&e.loop_ids.get(n.id)===e.in_loop}function is_immutable(e){if(!e)return false;return e.is_constant()||e instanceof se||e instanceof zt}function mark_escaped(e,t,n,i,r,o=0,a=1){var s=e.parent(o);if(r){if(r.is_constant())return;if(r instanceof Et)return}if(s instanceof tt&&(s.operator==="="||s.logical)&&i===s.right||s instanceof Ke&&(i!==s.expression||s instanceof Ge)||s instanceof Ee&&i===s.value&&i.scope!==t.scope||s instanceof Be&&i===s.value||s instanceof Ae&&i===s.value&&i.scope!==t.scope){if(a>1&&!(r&&r.is_constant_expression(n)))a=1;if(!t.escaped||t.escaped>a)t.escaped=a;return}else if(s instanceof it||s instanceof Se||s instanceof Je&&In.has(s.operator)||s instanceof et&&i!==s.condition||s instanceof ae||s instanceof He&&i===s.tail_node()){mark_escaped(e,t,n,s,s,o+1,a)}else if(s instanceof at&&i===s.value){var u=e.parent(o+1);mark_escaped(e,t,n,u,u,o+2,a)}else if(s instanceof Xe&&i===s.expression){r=read_property(r,s.property);mark_escaped(e,t,n,s,r,o+1,a+1);if(r)return}if(o>0)return;if(s instanceof He&&i!==s.tail_node())return;if(s instanceof H)return;t.direct_access=true}const suppress=e=>walk(e,(e=>{if(!(e instanceof gt))return;var t=e.definition();if(!t)return;if(e instanceof Pt)t.references.push(e);t.fixed=false}));def_reduce_vars(ue,(function(e,t,n){push(e);reset_variables(e,n,this);t();pop(e);return true}));def_reduce_vars(tt,(function(e,t,n){var i=this;if(i.left instanceof _e){suppress(i.left);return}const finish_walk=()=>{if(i.logical){i.left.walk(e);push(e);i.right.walk(e);pop(e);return true}};var r=i.left;if(!(r instanceof Pt))return finish_walk();var o=r.definition();var a=safe_to_assign(e,o,r.scope,i.right);o.assignments++;if(!a)return finish_walk();var s=o.fixed;if(!s&&i.operator!="="&&!i.logical)return finish_walk();var u=i.operator=="=";var l=u?i.right:i;if(is_modified(n,e,i,l,0))return finish_walk();o.references.push(r);if(!i.logical){if(!u)o.chained=true;o.fixed=u?function(){return i.right}:function(){return make_node(Je,i,{operator:i.operator.slice(0,-1),left:s instanceof z?s:s(),right:i.right})}}if(i.logical){mark(e,o,false);push(e);i.right.walk(e);pop(e);return true}mark(e,o,false);i.right.walk(e);mark(e,o,true);mark_escaped(e,o,r.scope,i,l,0,1);return true}));def_reduce_vars(Je,(function(e){if(!In.has(this.operator))return;this.left.walk(e);push(e);this.right.walk(e);pop(e);return true}));def_reduce_vars(X,(function(e,t,n){reset_block_variables(n,this)}));def_reduce_vars(Fe,(function(e){push(e);this.expression.walk(e);pop(e);push(e);walk_body(this,e);pop(e);return true}));def_reduce_vars(pt,(function(e,t){clear_flag(this,Sn);push(e);t();pop(e);return true}));def_reduce_vars(et,(function(e){this.condition.walk(e);push(e);this.consequent.walk(e);pop(e);push(e);this.alternative.walk(e);pop(e);return true}));def_reduce_vars(je,(function(e,t){const n=e.safe_ids;t();e.safe_ids=n;return true}));def_reduce_vars(Ke,(function(e){this.expression.walk(e);if(this.optional){push(e)}for(const t of this.args)t.walk(e);return true}));def_reduce_vars(Xe,(function(e){if(!this.optional)return;this.expression.walk(e);push(e);if(this.property instanceof z)this.property.walk(e);return true}));def_reduce_vars(Re,(function(e,t){push(e);t();pop(e);return true}));function mark_lambda(e,t,n){clear_flag(this,Sn);push(e);reset_variables(e,n,this);if(this.uses_arguments){t();pop(e);return}var i;if(!this.name&&(i=e.parent())instanceof Ke&&i.expression===this&&!i.args.some((e=>e instanceof ae))&&this.argnames.every((e=>e instanceof gt))){this.argnames.forEach(((t,n)=>{if(!t.definition)return;var r=t.definition();if(r.orig.length>1)return;if(r.fixed===undefined&&(!this.uses_arguments||e.has_directive("use strict"))){r.fixed=function(){return i.args[n]||make_node($t,i)};e.loop_ids.set(r.id,e.in_loop);mark(e,r,true)}else{r.fixed=false}}))}t();pop(e);return true}def_reduce_vars(se,mark_lambda);def_reduce_vars(Q,(function(e,t,n){reset_block_variables(n,this);const i=e.in_loop;e.in_loop=this;push(e);this.body.walk(e);if(has_break_or_continue(this)){pop(e);push(e)}this.condition.walk(e);pop(e);e.in_loop=i;return true}));def_reduce_vars(ee,(function(e,t,n){reset_block_variables(n,this);if(this.init)this.init.walk(e);const i=e.in_loop;e.in_loop=this;push(e);if(this.condition)this.condition.walk(e);this.body.walk(e);if(this.step){if(has_break_or_continue(this)){pop(e);push(e)}this.step.walk(e)}pop(e);e.in_loop=i;return true}));def_reduce_vars(te,(function(e,t,n){reset_block_variables(n,this);suppress(this.init);this.object.walk(e);const i=e.in_loop;e.in_loop=this;push(e);this.body.walk(e);pop(e);e.in_loop=i;return true}));def_reduce_vars(Te,(function(e){this.condition.walk(e);push(e);this.body.walk(e);pop(e);if(this.alternative){push(e);this.alternative.walk(e);pop(e)}return true}));def_reduce_vars(j,(function(e){push(e);this.body.walk(e);pop(e);return true}));def_reduce_vars(Mt,(function(){this.definition().fixed=false}));def_reduce_vars(Pt,(function(e,t,n){var i=this.definition();i.references.push(this);if(i.references.length==1&&!i.fixed&&i.orig[0]instanceof kt){e.loop_ids.set(i.id,e.in_loop)}var r;if(i.fixed===undefined||!safe_to_read(e,i)){i.fixed=false}else if(i.fixed){r=this.fixed_value();if(r instanceof se&&is_recursive_ref(e,i)){i.recursive_refs++}else if(r&&!n.exposed(i)&&ref_once(e,n,i)){i.single_use=r instanceof se&&!r.pinned()||r instanceof pt||i.scope===this.scope&&r.is_constant_expression()}else{i.single_use=false}if(is_modified(n,e,this,r,0,is_immutable(r))){if(i.single_use){i.single_use="m"}else{i.fixed=false}}}mark_escaped(e,i,this.scope,this,r,0,1)}));def_reduce_vars(oe,(function(e,t,n){this.globals.forEach((function(e){reset_def(n,e)}));reset_variables(e,n,this)}));def_reduce_vars(Oe,(function(e,t,n){reset_block_variables(n,this);push(e);walk_body(this,e);pop(e);if(this.bcatch){push(e);this.bcatch.walk(e);pop(e)}if(this.bfinally)this.bfinally.walk(e);return true}));def_reduce_vars($e,(function(e){var t=this;if(t.operator!=="++"&&t.operator!=="--")return;var n=t.expression;if(!(n instanceof Pt))return;var i=n.definition();var r=safe_to_assign(e,i,n.scope,true);i.assignments++;if(!r)return;var o=i.fixed;if(!o)return;i.references.push(n);i.chained=true;i.fixed=function(){return make_node(Je,t,{operator:t.operator.slice(0,-1),left:make_node(Ze,t,{operator:"+",expression:o instanceof z?o:o()}),right:make_node(Ht,t,{value:1})})};mark(e,i,true);return true}));def_reduce_vars(Be,(function(e,t){var n=this;if(n.name instanceof _e){suppress(n.name);return}var i=n.name.definition();if(n.value){if(safe_to_assign(e,i,n.name.scope,n.value)){i.fixed=function(){return n.value};e.loop_ids.set(i.id,e.in_loop);mark(e,i,false);t();mark(e,i,true);return true}else{i.fixed=false}}}));def_reduce_vars(J,(function(e,t,n){reset_block_variables(n,this);const i=e.in_loop;e.in_loop=this;push(e);t();pop(e);e.in_loop=i;return true}));function loop_body(e){if(e instanceof $){return e.body instanceof W?e.body:e}return e}function is_lhs_read_only(e){if(e instanceof zt)return true;if(e instanceof Pt)return e.definition().orig[0]instanceof Ft;if(e instanceof Xe){e=e.expression;if(e instanceof Pt){if(e.is_immutable())return false;e=e.fixed_value()}if(!e)return true;if(e instanceof Wt)return false;if(e instanceof Kt)return true;return is_lhs_read_only(e)}return false}function trim_unreachable_code(e,t,n){walk(t,(i=>{if(i instanceof Ne){i.remove_initializers();n.push(i);return true}if(i instanceof fe&&(i===t||!e.has_directive("use strict"))){n.push(i===t?i:make_node(Ne,i,{definitions:[make_node(Be,i,{name:make_node(bt,i.name,i.name),value:null})]}));return true}if(i instanceof Ue||i instanceof Ve){n.push(i);return true}if(i instanceof re){return true}}))}function tighten_body(e,t){var n,r;var o=t.find_parent(re).get_defun_scope();find_loop_scope_try();var a,s=10;do{a=false;eliminate_spurious_blocks(e);if(t.option("dead_code")){eliminate_dead_code(e,t)}if(t.option("if_return")){handle_if_return(e,t)}if(t.sequences_limit>0){sequencesize(e,t);sequencesize_2(e,t)}if(t.option("join_vars")){join_consecutive_vars(e)}if(t.option("collapse_vars")){collapse(e,t)}}while(a&&s-- >0);function find_loop_scope_try(){var e=t.self(),i=0;do{if(e instanceof xe||e instanceof Me){i++}else if(e instanceof $){n=true}else if(e instanceof re){o=e;break}else if(e instanceof Oe){r=true}}while(e=t.parent(i++))}function collapse(e,t){if(o.pinned())return e;var s;var u=[];var l=e.length;var c=new TreeTransformer((function(e){if(k)return e;if(!T){if(e!==_[p])return e;p++;if(p<_.length)return handle_custom_scan_order(e);T=true;h=find_stop(e,0);if(h===e)k=true;return e}var n=c.parent();if(e instanceof tt&&(e.logical||e.operator!="="&&g.equivalent_to(e.left))||e instanceof Se||e instanceof Ke&&g instanceof Xe&&g.equivalent_to(e.expression)||e instanceof K||e instanceof _e||e instanceof ae&&e.expression instanceof gt&&(e.expression instanceof zt||e.expression.definition().references.length>1)||e instanceof $&&!(e instanceof ee)||e instanceof De||e instanceof Oe||e instanceof ie||e instanceof Ae||e instanceof Ue||e instanceof pt||n instanceof ee&&e!==n.init||!y&&(e instanceof Pt&&!e.is_declared(t)&&!On.has(e))||e instanceof Pt&&n instanceof Ke&&has_annotation(n,an)){k=true;return e}if(!E&&(!D||!y)&&(n instanceof Je&&In.has(n.operator)&&n.left!==e||n instanceof et&&n.condition!==e||n instanceof Te&&n.condition!==e)){E=n}if(R&&!(e instanceof Dt)&&g.equivalent_to(e)&&!shadows(e.scope,v)){if(E){k=true;return e}if(is_lhs(e,n)){if(m)C++;return e}else{C++;if(m&&d instanceof Be)return e}a=k=true;if(d instanceof Qe){return make_node(Ze,d,d)}if(d instanceof Be){var i=d.name.definition();var o=d.value;if(i.references.length-i.replaced==1&&!t.exposed(i)){i.replaced++;if(A&&is_identifier_atom(o)){return o.transform(t)}else{return maintain_this_binding(n,e,o)}}return make_node(tt,d,{operator:"=",logical:false,left:make_node(Pt,d.name,d.name),right:o})}clear_flag(d,An);return d}var s;if(e instanceof Ke||e instanceof Ee&&(b||g instanceof Xe||may_modify(g))||e instanceof Xe&&(b||e.expression.may_throw_on_access(t))||e instanceof Pt&&(v.has(e.name)&&v.get(e.name).modified||b&&may_modify(e))||e instanceof Be&&e.value&&(v.has(e.name.name)||b&&may_modify(e.name))||(s=is_lhs(e.left,e))&&(s instanceof Xe||v.has(s.name))||S&&(r?e.has_side_effects(t):side_effects_external(e))){h=e;if(e instanceof re)k=true}return handle_custom_scan_order(e)}),(function(e){if(k)return;if(h===e)k=true;if(E===e)E=null}));var f=new TreeTransformer((function(e){if(k)return e;if(!T){if(e!==_[p])return e;p++;if(p<_.length)return;T=true;return e}if(e instanceof Pt&&e.name==x.name){if(!--C)k=true;if(is_lhs(e,f.parent()))return e;x.replaced++;m.replaced--;return d.value}if(e instanceof Re||e instanceof re)return e}));while(--l>=0){if(l==0&&t.option("unused"))extract_args();var _=[];extract_candidates(e[l]);while(u.length>0){_=u.pop();var p=0;var d=_[_.length-1];var m=null;var h=null;var E=null;var g=get_lhs(d);if(!g||is_lhs_read_only(g)||g.has_side_effects(t))continue;var v=get_lvalues(d);var D=is_lhs_local(g);if(g instanceof Pt){v.set(g.name,{def:g.definition(),modified:false})}var b=value_has_side_effects(d);var y=replace_all_symbols();var S=d.may_throw(t);var A=d.name instanceof Tt;var T=A;var k=false,C=0,R=!s||!T;if(!R){for(var F=t.self().argnames.lastIndexOf(d.name)+1;!k&&F<s.length;F++){s[F].transform(c)}R=true}for(var O=l;!k&&O<e.length;O++){e[O].transform(c)}if(m){var x=d.name.definition();if(k&&x.references.length-x.replaced>C)C=false;else{k=false;p=0;T=A;for(var O=l;!k&&O<e.length;O++){e[O].transform(f)}m.single_use=false}}if(C&&!remove_candidate(d))e.splice(l,1)}}function handle_custom_scan_order(e){if(e instanceof re)return e;if(e instanceof ke){e.expression=e.expression.transform(c);for(var t=0,n=e.body.length;!k&&t<n;t++){var i=e.body[t];if(i instanceof Fe){if(!T){if(i!==_[p])continue;p++}i.expression=i.expression.transform(c);if(!y)break}}k=true;return e}}function redefined_within_scope(e,t){if(e.global)return false;let n=e.scope;while(n&&n!==t){if(n.variables.has(e.name)){return true}n=n.parent_scope}return false}function has_overlapping_symbol(e,t,n){var i=false,r=!(e instanceof ce);t.walk(new TreeWalker((function(t,a){if(i)return true;if(t instanceof Pt&&(e.variables.has(t.name)||redefined_within_scope(t.definition(),e))){var s=t.definition().scope;if(s!==o)while(s=s.parent_scope){if(s===o)return true}return i=true}if((n||r)&&t instanceof zt){return i=true}if(t instanceof re&&!(t instanceof ce)){var u=r;r=false;a();r=u;return true}})));return i}function extract_args(){var e,n=t.self();if(is_func_expr(n)&&!n.name&&!n.uses_arguments&&!n.pinned()&&(e=t.parent())instanceof Ke&&e.expression===n&&e.args.every((e=>!(e instanceof ae)))){var i=t.has_directive("use strict");if(i&&!member(i,n.body))i=false;var r=n.argnames.length;s=e.args.slice(r);var o=new Set;for(var a=r;--a>=0;){var l=n.argnames[a];var c=e.args[a];const r=l.definition&&l.definition();const _=r&&r.orig.length>1;if(_)continue;s.unshift(make_node(Be,l,{name:l,value:c}));if(o.has(l.name))continue;o.add(l.name);if(l instanceof ae){var f=e.args.slice(a);if(f.every((e=>!has_overlapping_symbol(n,e,i)))){u.unshift([make_node(Be,l,{name:l.expression,value:make_node(it,e,{elements:f})})])}}else{if(!c){c=make_node($t,l).transform(t)}else if(c instanceof se&&c.pinned()||has_overlapping_symbol(n,c,i)){c=null}if(c)u.unshift([make_node(Be,l,{name:l,value:c})])}}}}function extract_candidates(e){_.push(e);if(e instanceof tt){if(!e.left.has_side_effects(t)&&!(e.right instanceof je)){u.push(_.slice())}extract_candidates(e.right)}else if(e instanceof Je){extract_candidates(e.left);extract_candidates(e.right)}else if(e instanceof Ke&&!has_annotation(e,an)){extract_candidates(e.expression);e.args.forEach(extract_candidates)}else if(e instanceof Fe){extract_candidates(e.expression)}else if(e instanceof et){extract_candidates(e.condition);extract_candidates(e.consequent);extract_candidates(e.alternative)}else if(e instanceof we){var n=e.definitions.length;var i=n-200;if(i<0)i=0;for(;i<n;i++){extract_candidates(e.definitions[i])}}else if(e instanceof Z){extract_candidates(e.condition);if(!(e.body instanceof X)){extract_candidates(e.body)}}else if(e instanceof Ee){if(e.value)extract_candidates(e.value)}else if(e instanceof ee){if(e.init)extract_candidates(e.init);if(e.condition)extract_candidates(e.condition);if(e.step)extract_candidates(e.step);if(!(e.body instanceof X)){extract_candidates(e.body)}}else if(e instanceof te){extract_candidates(e.object);if(!(e.body instanceof X)){extract_candidates(e.body)}}else if(e instanceof Te){extract_candidates(e.condition);if(!(e.body instanceof X)){extract_candidates(e.body)}if(e.alternative&&!(e.alternative instanceof X)){extract_candidates(e.alternative)}}else if(e instanceof He){e.expressions.forEach(extract_candidates)}else if(e instanceof H){extract_candidates(e.body)}else if(e instanceof ke){extract_candidates(e.expression);e.body.forEach(extract_candidates)}else if(e instanceof $e){if(e.operator=="++"||e.operator=="--"){u.push(_.slice())}}else if(e instanceof Be){if(e.value&&!(e.value instanceof je)){u.push(_.slice());extract_candidates(e.value)}}_.pop()}function find_stop(e,t,n){var i=c.parent(t);if(i instanceof tt){if(n&&!i.logical&&!(i.left instanceof Xe||v.has(i.left.name))){return find_stop(i,t+1,n)}return e}if(i instanceof Je){if(n&&(!In.has(i.operator)||i.left===e)){return find_stop(i,t+1,n)}return e}if(i instanceof Ke)return e;if(i instanceof Fe)return e;if(i instanceof et){if(n&&i.condition===e){return find_stop(i,t+1,n)}return e}if(i instanceof we){return find_stop(i,t+1,true)}if(i instanceof Ee){return n?find_stop(i,t+1,n):e}if(i instanceof Te){if(n&&i.condition===e){return find_stop(i,t+1,n)}return e}if(i instanceof $)return e;if(i instanceof He){return find_stop(i,t+1,i.tail_node()!==e)}if(i instanceof H){return find_stop(i,t+1,true)}if(i instanceof ke)return e;if(i instanceof Be)return e;return null}function mangleable_var(e){var t=e.value;if(!(t instanceof Pt))return;if(t.name=="arguments")return;var n=t.definition();if(n.undeclared)return;return m=n}function get_lhs(e){if(e instanceof tt&&e.logical){return false}else if(e instanceof Be&&e.name instanceof Dt){var n=e.name.definition();if(!member(e.name,n.orig))return;var i=n.references.length-n.replaced;if(!i)return;var r=n.orig.length-n.eliminated;if(r>1&&!(e.name instanceof Tt)||(i>1?mangleable_var(e):!t.exposed(n))){return make_node(Pt,e.name,e.name)}}else{const t=e instanceof tt?e.left:e.expression;return!is_ref_of(t,St)&&!is_ref_of(t,At)&&t}}function get_rvalue(e){if(e instanceof tt){return e.right}else{return e.value}}function get_lvalues(e){var n=new Map;if(e instanceof $e)return n;var i=new TreeWalker((function(e){var r=e;while(r instanceof Xe)r=r.expression;if(r instanceof Pt){const o=n.get(r.name);if(!o||!o.modified){n.set(r.name,{def:r.definition(),modified:is_modified(t,i,e,e,0)})}}}));get_rvalue(e).walk(i);return n}function remove_candidate(n){if(n.name instanceof Tt){var r=t.parent(),o=t.self().argnames;var a=o.indexOf(n.name);if(a<0){r.args.length=Math.min(r.args.length,o.length-1)}else{var s=r.args;if(s[a])s[a]=make_node(Ht,s[a],{value:0})}return true}var u=false;return e[l].transform(new TreeTransformer((function(e,t,r){if(u)return e;if(e===n||e.body===n){u=true;if(e instanceof Be){e.value=e.name instanceof St?make_node($t,e.value):null;return e}return r?i.skip:null}}),(function(e){if(e instanceof He)switch(e.expressions.length){case 0:return null;case 1:return e.expressions[0]}})))}function is_lhs_local(e){while(e instanceof Xe)e=e.expression;return e instanceof Pt&&e.definition().scope===o&&!(n&&(v.has(e.name)||d instanceof $e||d instanceof tt&&!d.logical&&d.operator!="="))}function value_has_side_effects(e){if(e instanceof $e)return Pn.has(e.operator);return get_rvalue(e).has_side_effects(t)}function replace_all_symbols(){if(b)return false;if(m)return true;if(g instanceof Pt){var e=g.definition();if(e.references.length-e.replaced==(d instanceof Be?1:2)){return true}}return false}function may_modify(e){if(!e.definition)return true;var t=e.definition();if(t.orig.length==1&&t.orig[0]instanceof kt)return false;if(t.scope.get_defun_scope()!==o)return true;return!t.references.every((e=>{var t=e.scope.get_defun_scope();if(t.TYPE=="Scope")t=t.parent_scope;return t===o}))}function side_effects_external(e,t){if(e instanceof tt)return side_effects_external(e.left,true);if(e instanceof $e)return side_effects_external(e.expression,true);if(e instanceof Be)return e.value&&side_effects_external(e.value);if(t){if(e instanceof We)return side_effects_external(e.expression,true);if(e instanceof Ye)return side_effects_external(e.expression,true);if(e instanceof Pt)return e.definition().scope!==o}return false}function shadows(e,t){for(const{def:n}of t.values()){let t=e;while(t&&t!==n.scope){let e=t.variables.get(n.name);if(e&&e!==n)return true;t=t.parent_scope}}return false}}function eliminate_spurious_blocks(e){var t=[];for(var n=0;n<e.length;){var i=e[n];if(i instanceof W&&i.body.every(can_be_evicted_from_block)){a=true;eliminate_spurious_blocks(i.body);e.splice(n,1,...i.body);n+=i.body.length}else if(i instanceof q){a=true;e.splice(n,1)}else if(i instanceof G){if(t.indexOf(i.value)<0){n++;t.push(i.value)}else{a=true;e.splice(n,1)}}else n++}}function handle_if_return(e,t){var n=t.self();var i=has_multiple_if_returns(e);var r=n instanceof se;for(var o=e.length;--o>=0;){var s=e[o];var u=next_index(o);var l=e[u];if(r&&!l&&s instanceof ge){if(!s.value){a=true;e.splice(o,1);continue}if(s.value instanceof Ze&&s.value.operator=="void"){a=true;e[o]=make_node(H,s,{body:s.value.expression});continue}}if(s instanceof Te){var c=aborts(s.body);if(can_merge_flow(c)){if(c.label){remove(c.label.thedef.references,c)}a=true;s=s.clone();s.condition=s.condition.negate(t);var f=as_statement_array_with_return(s.body,c);s.body=make_node(W,s,{body:as_statement_array(s.alternative).concat(extract_functions())});s.alternative=make_node(W,s,{body:f});e[o]=s.transform(t);continue}var c=aborts(s.alternative);if(can_merge_flow(c)){if(c.label){remove(c.label.thedef.references,c)}a=true;s=s.clone();s.body=make_node(W,s.body,{body:as_statement_array(s.body).concat(extract_functions())});var f=as_statement_array_with_return(s.alternative,c);s.alternative=make_node(W,s.alternative,{body:f});e[o]=s.transform(t);continue}}if(s instanceof Te&&s.body instanceof ge){var _=s.body.value;if(!_&&!s.alternative&&(r&&!l||l instanceof ge&&!l.value)){a=true;e[o]=make_node(H,s.condition,{body:s.condition});continue}if(_&&!s.alternative&&l instanceof ge&&l.value){a=true;s=s.clone();s.alternative=l;e[o]=s.transform(t);e.splice(u,1);continue}if(_&&!s.alternative&&(!l&&r&&i||l instanceof ge)){a=true;s=s.clone();s.alternative=l||make_node(ge,s,{value:null});e[o]=s.transform(t);if(l)e.splice(u,1);continue}var p=e[prev_index(o)];if(t.option("sequences")&&r&&!s.alternative&&p instanceof Te&&p.body instanceof ge&&next_index(u)==e.length&&l instanceof H){a=true;s=s.clone();s.alternative=make_node(W,l,{body:[l,make_node(ge,l,{value:null})]});e[o]=s.transform(t);e.splice(u,1);continue}}}function has_multiple_if_returns(e){var t=0;for(var n=e.length;--n>=0;){var i=e[n];if(i instanceof Te&&i.body instanceof ge){if(++t>1)return true}}return false}function is_return_void(e){return!e||e instanceof Ze&&e.operator=="void"}function can_merge_flow(i){if(!i)return false;for(var a=o+1,s=e.length;a<s;a++){var u=e[a];if(u instanceof Pe||u instanceof Ie)return false}var l=i instanceof De?t.loopcontrol_target(i):null;return i instanceof ge&&r&&is_return_void(i.value)||i instanceof ye&&n===loop_body(l)||i instanceof be&&l instanceof W&&n===l}function extract_functions(){var t=e.slice(o+1);e.length=o+1;return t.filter((function(t){if(t instanceof fe){e.push(t);return false}return true}))}function as_statement_array_with_return(e,t){var n=as_statement_array(e).slice(0,-1);if(t.value){n.push(make_node(H,t.value,{body:t.value.expression}))}return n}function next_index(t){for(var n=t+1,i=e.length;n<i;n++){var r=e[n];if(!(r instanceof Ne&&declarations_only(r))){break}}return n}function prev_index(t){for(var n=t;--n>=0;){var i=e[n];if(!(i instanceof Ne&&declarations_only(i))){break}}return n}}function eliminate_dead_code(e,t){var n;var i=t.self();for(var r=0,o=0,s=e.length;r<s;r++){var u=e[r];if(u instanceof De){var l=t.loopcontrol_target(u);if(u instanceof be&&!(l instanceof $)&&loop_body(l)===i||u instanceof ye&&loop_body(l)===i){if(u.label){remove(u.label.thedef.references,u)}}else{e[o++]=u}}else{e[o++]=u}if(aborts(u)){n=e.slice(r+1);break}}e.length=o;a=o!=s;if(n)n.forEach((function(n){trim_unreachable_code(t,n,e)}))}function declarations_only(e){return e.definitions.every((e=>!e.value))}function sequencesize(e,t){if(e.length<2)return;var n=[],i=0;function push_seq(){if(!n.length)return;var t=make_sequence(n[0],n);e[i++]=make_node(H,t,{body:t});n=[]}for(var r=0,o=e.length;r<o;r++){var s=e[r];if(s instanceof H){if(n.length>=t.sequences_limit)push_seq();var u=s.body;if(n.length>0)u=u.drop_side_effect_free(t);if(u)merge_sequence(n,u)}else if(s instanceof we&&declarations_only(s)||s instanceof fe){e[i++]=s}else{push_seq();e[i++]=s}}push_seq();e.length=i;if(i!=o)a=true}function to_simple_statement(e,t){if(!(e instanceof W))return e;var n=null;for(var i=0,r=e.body.length;i<r;i++){var o=e.body[i];if(o instanceof Ne&&declarations_only(o)){t.push(o)}else if(n){return false}else{n=o}}return n}function sequencesize_2(e,t){function cons_seq(e){n--;a=true;var r=i.body;return make_sequence(r,[r,e]).transform(t)}var n=0,i;for(var r=0;r<e.length;r++){var o=e[r];if(i){if(o instanceof Ee){o.value=cons_seq(o.value||make_node($t,o).transform(t))}else if(o instanceof ee){if(!(o.init instanceof we)){const e=walk(i.body,(e=>{if(e instanceof re)return true;if(e instanceof Je&&e.operator==="in"){return nn}}));if(!e){if(o.init)o.init=cons_seq(o.init);else{o.init=i.body;n--;a=true}}}}else if(o instanceof te){if(!(o.init instanceof Pe)&&!(o.init instanceof Ie)){o.object=cons_seq(o.object)}}else if(o instanceof Te){o.condition=cons_seq(o.condition)}else if(o instanceof ke){o.expression=cons_seq(o.expression)}else if(o instanceof ie){o.expression=cons_seq(o.expression)}}if(t.option("conditionals")&&o instanceof Te){var s=[];var u=to_simple_statement(o.body,s);var l=to_simple_statement(o.alternative,s);if(u!==false&&l!==false&&s.length>0){var c=s.length;s.push(make_node(Te,o,{condition:o.condition,body:u||make_node(q,o.body),alternative:l}));s.unshift(n,1);[].splice.apply(e,s);r+=c;n+=c+1;i=null;a=true;continue}}e[n++]=o;i=o instanceof H?o:null}e.length=n}function join_object_assignments(e,n){if(!(e instanceof we))return;var i=e.definitions[e.definitions.length-1];if(!(i.value instanceof rt))return;var r;if(n instanceof tt&&!n.logical){r=[n]}else if(n instanceof He){r=n.expressions.slice()}if(!r)return;var a=false;do{var s=r[0];if(!(s instanceof tt))break;if(s.operator!="=")break;if(!(s.left instanceof Xe))break;var u=s.left.expression;if(!(u instanceof Pt))break;if(i.name.name!=u.name)break;if(!s.right.is_constant_expression(o))break;var l=s.left.property;if(l instanceof z){l=l.evaluate(t)}if(l instanceof z)break;l=""+l;var c=t.option("ecma")<2015&&t.has_directive("use strict")?function(e){return e.key!=l&&(e.key&&e.key.name!=l)}:function(e){return e.key&&e.key.name!=l};if(!i.value.properties.every(c))break;var f=i.value.properties.filter((function(e){return e.key===l}))[0];if(!f){i.value.properties.push(make_node(at,s,{key:l,value:s.right}))}else{f.value=new He({start:f.start,expressions:[f.value.clone(),s.right.clone()],end:f.end})}r.shift();a=true}while(r.length);return a&&r}function join_consecutive_vars(e){var t;for(var n=0,i=-1,r=e.length;n<r;n++){var o=e[n];var s=e[i];if(o instanceof we){if(s&&s.TYPE==o.TYPE){s.definitions=s.definitions.concat(o.definitions);a=true}else if(t&&t.TYPE==o.TYPE&&declarations_only(o)){t.definitions=t.definitions.concat(o.definitions);a=true}else{e[++i]=o;t=o}}else if(o instanceof Ee){o.value=extract_object_assignments(o.value)}else if(o instanceof ee){var u=join_object_assignments(s,o.init);if(u){a=true;o.init=u.length?make_sequence(o.init,u):null;e[++i]=o}else if(s instanceof Ne&&(!o.init||o.init.TYPE==s.TYPE)){if(o.init){s.definitions=s.definitions.concat(o.init.definitions)}o.init=s;e[i]=o;a=true}else if(t&&o.init&&t.TYPE==o.init.TYPE&&declarations_only(o.init)){t.definitions=t.definitions.concat(o.init.definitions);o.init=null;e[++i]=o;a=true}else{e[++i]=o}}else if(o instanceof te){o.object=extract_object_assignments(o.object)}else if(o instanceof Te){o.condition=extract_object_assignments(o.condition)}else if(o instanceof H){var u=join_object_assignments(s,o.body);if(u){a=true;if(!u.length)continue;o.body=make_sequence(o.body,u)}e[++i]=o}else if(o instanceof ke){o.expression=extract_object_assignments(o.expression)}else if(o instanceof ie){o.expression=extract_object_assignments(o.expression)}else{e[++i]=o}}e.length=i+1;function extract_object_assignments(t){e[++i]=o;var n=join_object_assignments(s,t);if(n){a=true;if(n.length){return make_sequence(t,n)}else if(t instanceof He){return t.tail_node().left}else{return t.left}}return t}}}function within_array_or_object_literal(e){var t,n=0;while(t=e.parent(n++)){if(t instanceof U)return false;if(t instanceof it||t instanceof at||t instanceof rt){return true}}return false}function scope_encloses_variables_in_this_scope(e,t){for(const n of t.enclosed){if(t.variables.has(n.name)){continue}const i=e.find_variable(n.name);if(i){if(i===n)continue;return true}}return false}function inline_into_symbolref(e,t){if(!t.option("ie8")&&is_undeclared_ref(e)&&!t.find_parent(ie)){switch(e.name){case"undefined":return make_node($t,e).optimize(t);case"NaN":return make_node(jt,e).optimize(t);case"Infinity":return make_node(Qt,e).optimize(t)}}const n=t.parent();if(t.option("reduce_vars")&&is_lhs(e,n)!==e){const o=e.definition();const a=t.find_scope();if(t.top_retain&&o.global&&t.top_retain(o)){o.fixed=false;o.single_use=false;return e}let s=e.fixed_value();let u=o.single_use&&!(n instanceof Ke&&n.is_callee_pure(t)||has_annotation(n,an))&&!(n instanceof Ue&&s instanceof se&&s.name);if(u&&s instanceof z){u=!s.has_side_effects(t)&&!s.may_throw(t)}if(u&&(s instanceof se||s instanceof pt)){if(retain_top_func(s,t)){u=false}else if(o.scope!==e.scope&&(o.escaped==1||has_flag(s,Sn)||within_array_or_object_literal(t)||!t.option("reduce_funcs"))){u=false}else if(is_recursive_ref(t,o)){u=false}else if(o.scope!==e.scope||o.orig[0]instanceof Tt){u=s.is_constant_expression(e.scope);if(u=="f"){var i=e.scope;do{if(i instanceof fe||is_func_expr(i)){set_flag(i,Sn)}}while(i=i.parent_scope)}}}if(u&&s instanceof se){u=o.scope===e.scope&&!scope_encloses_variables_in_this_scope(a,s)||n instanceof Ke&&n.expression===e&&!scope_encloses_variables_in_this_scope(a,s)&&!(s.name&&s.name.definition().recursive_refs>0)}if(u&&s){if(s instanceof ht){set_flag(s,Tn);s=make_node(Et,s,s)}if(s instanceof fe){set_flag(s,Tn);s=make_node(le,s,s)}if(o.recursive_refs>0&&s.name instanceof kt){const e=s.name.definition();let t=s.variables.get(s.name.name);let n=t&&t.orig[0];if(!(n instanceof Ft)){n=make_node(Ft,s.name,s.name);n.scope=s;s.name=n;t=s.def_function(n)}walk(s,(n=>{if(n instanceof Pt&&n.definition()===e){n.thedef=t;t.references.push(n)}}))}if((s instanceof se||s instanceof pt)&&s.parent_scope!==a){s=s.clone(true,t.get_toplevel());a.add_child_scope(s)}return s.optimize(t)}if(s){let n;if(s instanceof zt){if(!(o.orig[0]instanceof Tt)&&o.references.every((e=>o.scope===e.scope))){n=s}}else{var r=s.evaluate(t);if(r!==s&&(t.option("unsafe_regexp")||!(r instanceof RegExp))){n=make_node_from_constant(r,s)}}if(n){const i=e.size(t);const r=n.size(t);let a=0;if(t.option("unused")&&!t.exposed(o)){a=(i+2+r)/(o.references.length-o.assignments)}if(r<=i+a){return n}}}}return e}function inline_into_call(e,t,n){var i=e.expression;var r=e.args.every((e=>!(e instanceof ae)));if(n.option("reduce_vars")&&t instanceof Pt&&!has_annotation(e,an)){const e=t.fixed_value();if(!retain_top_func(e,n)){t=e}}var o=t instanceof se;var a=o&&t.body[0];var s=o&&!t.is_generator&&!t.async;var u=s&&n.option("inline")&&!e.is_callee_pure(n);if(u&&a instanceof ge){let i=a.value;if(!i||i.is_constant_expression()){if(i){i=i.clone(true)}else{i=make_node($t,e)}const t=e.args.concat(i);return make_sequence(e,t).optimize(n)}if(t.argnames.length===1&&t.argnames[0]instanceof Tt&&e.args.length<2&&i instanceof Pt&&i.name===t.argnames[0].name){const t=(e.args[0]||make_node($t)).optimize(n);let i;if(t instanceof Xe&&(i=n.parent())instanceof Ke&&i.expression===e){return make_sequence(e,[make_node(Ht,e,{value:0}),t])}return t}}if(u){var l,c,f=-1;let o;let s;let u;if(r&&!t.uses_arguments&&!(n.parent()instanceof pt)&&!(t.name&&t instanceof le)&&(s=can_flatten_body(a))&&(i===t||has_annotation(e,on)||n.option("unused")&&(o=i.definition()).references.length==1&&!is_recursive_ref(n,o)&&t.is_constant_expression(i.scope))&&!has_annotation(e,rn|an)&&!t.contains_this()&&can_inject_symbols()&&(u=n.find_scope())&&!scope_encloses_variables_in_this_scope(u,t)&&!function in_default_assign(){let e=0;let t;while(t=n.parent(e++)){if(t instanceof nt)return true;if(t instanceof X)break}return false}()&&!(l instanceof pt)){set_flag(t,Tn);u.add_child_scope(t);return make_sequence(e,flatten_fn(s)).optimize(n)}}if(u&&has_annotation(e,on)){set_flag(t,Tn);t=make_node(t.CTOR===fe?le:t.CTOR,t,t);t=t.clone(true);t.figure_out_scope({},{parent_scope:n.find_scope(),toplevel:n.get_toplevel()});return make_node(Ke,e,{expression:t,args:e.args}).optimize(n)}const _=s&&n.option("side_effects")&&t.body.every(is_empty);if(_){var p=e.args.concat(make_node($t,e));return make_sequence(e,p).optimize(n)}if(n.option("negate_iife")&&n.parent()instanceof H&&is_iife_call(e)){return e.negate(n,true)}var d=e.evaluate(n);if(d!==e){d=make_node_from_constant(d,e).optimize(n);return best_of(n,d,e)}return e;function return_value(t){if(!t)return make_node($t,e);if(t instanceof ge){if(!t.value)return make_node($t,e);return t.value.clone(true)}if(t instanceof H){return make_node(Ze,t,{operator:"void",expression:t.body.clone(true)})}}function can_flatten_body(e){var i=t.body;var r=i.length;if(n.option("inline")<3){return r==1&&return_value(e)}e=null;for(var o=0;o<r;o++){var a=i[o];if(a instanceof Ne){if(e&&!a.definitions.every((e=>!e.value))){return false}}else if(e){return false}else if(!(a instanceof q)){e=a}}return return_value(e)}function can_inject_args(e,n){for(var i=0,r=t.argnames.length;i<r;i++){var o=t.argnames[i];if(o instanceof nt){if(has_flag(o.left,vn))continue;return false}if(o instanceof _e)return false;if(o instanceof ae){if(has_flag(o.expression,vn))continue;return false}if(has_flag(o,vn))continue;if(!n||e.has(o.name)||Fn.has(o.name)||l.conflicting_def(o.name)){return false}if(c)c.push(o.definition())}return true}function can_inject_vars(e,n){var i=t.body.length;for(var r=0;r<i;r++){var o=t.body[r];if(!(o instanceof Ne))continue;if(!n)return false;for(var a=o.definitions.length;--a>=0;){var s=o.definitions[a].name;if(s instanceof _e||e.has(s.name)||Fn.has(s.name)||l.conflicting_def(s.name)){return false}if(c)c.push(s.definition())}}return true}function can_inject_symbols(){var e=new Set;do{l=n.parent(++f);if(l.is_block_scope()&&l.block_scope){l.block_scope.variables.forEach((function(t){e.add(t.name)}))}if(l instanceof xe){if(l.argname){e.add(l.argname.name)}}else if(l instanceof $){c=[]}else if(l instanceof Pt){if(l.fixed_value()instanceof re)return false}}while(!(l instanceof re));var i=!(l instanceof oe)||n.toplevel.vars;var r=n.option("inline");if(!can_inject_vars(e,r>=3&&i))return false;if(!can_inject_args(e,r>=2&&i))return false;return!c||c.length==0||!is_reachable(t,c)}function append_var(t,n,i,r){var o=i.definition();const a=l.variables.has(i.name);if(!a){l.variables.set(i.name,o);l.enclosed.push(o);t.push(make_node(Be,i,{name:i,value:null}))}var s=make_node(Pt,i,i);o.references.push(s);if(r)n.push(make_node(tt,e,{operator:"=",logical:false,left:s,right:r.clone()}))}function flatten_args(n,i){var r=t.argnames.length;for(var o=e.args.length;--o>=r;){i.push(e.args[o])}for(o=r;--o>=0;){var a=t.argnames[o];var s=e.args[o];if(has_flag(a,vn)||!a.name||l.conflicting_def(a.name)){if(s)i.push(s)}else{var u=make_node(bt,a,a);a.definition().orig.push(u);if(!s&&c)s=make_node($t,e);append_var(n,i,u,s)}}n.reverse();i.reverse()}function flatten_vars(e,n){var i=n.length;for(var r=0,o=t.body.length;r<o;r++){var a=t.body[r];if(!(a instanceof Ne))continue;for(var s=0,u=a.definitions.length;s<u;s++){var l=a.definitions[s];var f=l.name;append_var(e,n,f,l.value);if(c&&t.argnames.every((e=>e.name!=f.name))){var _=t.variables.get(f.name);var p=make_node(Pt,f,f);_.references.push(p);n.splice(i++,0,make_node(tt,l,{operator:"=",logical:false,left:p,right:make_node($t,f)}))}}}}function flatten_fn(e){var i=[];var r=[];flatten_args(i,r);flatten_vars(i,r);r.push(e);if(i.length){const e=l.body.indexOf(n.parent(f-1))+1;l.body.splice(e,0,make_node(Ne,t,{definitions:i}))}return r.map((e=>e.clone(true)))}}class Compressor extends TreeWalker{constructor(e,{false_by_default:t=false,mangle_options:n=false}){super();if(e.defaults!==undefined&&!e.defaults)t=true;this.options=defaults(e,{arguments:false,arrows:!t,booleans:!t,booleans_as_integers:false,collapse_vars:!t,comparisons:!t,computed_props:!t,conditionals:!t,dead_code:!t,defaults:true,directives:!t,drop_console:false,drop_debugger:!t,ecma:5,evaluate:!t,expression:false,global_defs:false,hoist_funs:false,hoist_props:!t,hoist_vars:false,ie8:false,if_return:!t,inline:!t,join_vars:!t,keep_classnames:false,keep_fargs:true,keep_fnames:false,keep_infinity:false,loops:!t,module:false,negate_iife:!t,passes:1,properties:!t,pure_getters:!t&&"strict",pure_funcs:null,reduce_funcs:!t,reduce_vars:!t,sequences:!t,side_effects:!t,switches:!t,top_retain:null,toplevel:!!(e&&e["top_retain"]),typeofs:!t,unsafe:false,unsafe_arrows:false,unsafe_comps:false,unsafe_Function:false,unsafe_math:false,unsafe_symbols:false,unsafe_methods:false,unsafe_proto:false,unsafe_regexp:false,unsafe_undefined:false,unused:!t,warnings:false},true);var i=this.options["global_defs"];if(typeof i=="object")for(var r in i){if(r[0]==="@"&&HOP(i,r)){i[r.slice(1)]=parse(i[r],{expression:true})}}if(this.options["inline"]===true)this.options["inline"]=3;var o=this.options["pure_funcs"];if(typeof o=="function"){this.pure_funcs=o}else{this.pure_funcs=o?function(e){return!o.includes(e.expression.print_to_string())}:return_true}var a=this.options["top_retain"];if(a instanceof RegExp){this.top_retain=function(e){return a.test(e.name)}}else if(typeof a=="function"){this.top_retain=a}else if(a){if(typeof a=="string"){a=a.split(/,/)}this.top_retain=function(e){return a.includes(e.name)}}if(this.options["module"]){this.directives["use strict"]=true;this.options["toplevel"]=true}var s=this.options["toplevel"];this.toplevel=typeof s=="string"?{funcs:/funcs/.test(s),vars:/vars/.test(s)}:{funcs:s,vars:s};var u=this.options["sequences"];this.sequences_limit=u==1?800:u|0;this.evaluated_regexps=new Map;this._toplevel=undefined;this.mangle_options=n}option(e){return this.options[e]}exposed(e){if(e.export)return true;if(e.global)for(var t=0,n=e.orig.length;t<n;t++)if(!this.toplevel[e.orig[t]instanceof kt?"funcs":"vars"])return true;return false}in_boolean_context(){if(!this.option("booleans"))return false;var e=this.self();for(var t=0,n;n=this.parent(t);t++){if(n instanceof H||n instanceof et&&n.condition===e||n instanceof Z&&n.condition===e||n instanceof ee&&n.condition===e||n instanceof Te&&n.condition===e||n instanceof Ze&&n.operator=="!"&&n.expression===e){return true}if(n instanceof Je&&(n.operator=="&&"||n.operator=="||"||n.operator=="??")||n instanceof et||n.tail_node()===e){e=n}else{return false}}}get_toplevel(){return this._toplevel}compress(e){e=e.resolve_defines(this);this._toplevel=e;if(this.option("expression")){this._toplevel.process_expression(true)}var t=+this.options.passes||1;var n=1/0;var i=false;var r=this.mangle_options&&this.mangle_options.nth_identifier||En;var o={ie8:this.option("ie8"),nth_identifier:r};for(var a=0;a<t;a++){this._toplevel.figure_out_scope(o);if(a===0&&this.option("drop_console")){this._toplevel=this._toplevel.drop_console()}if(a>0||this.option("reduce_vars")){this._toplevel.reset_opt_flags(this)}this._toplevel=this._toplevel.transform(this);if(t>1){let e=0;walk(this._toplevel,(()=>{e++}));if(e<n){n=e;i=false}else if(i){break}else{i=true}}}if(this.option("expression")){this._toplevel.process_expression(false)}e=this._toplevel;this._toplevel=undefined;return e}before(e,t){if(has_flag(e,Tn))return e;var n=false;if(e instanceof re){e=e.hoist_properties(this);e=e.hoist_declarations(this);n=true}t(e,this);t(e,this);var i=e.optimize(this);if(n&&i instanceof re){i.drop_unused(this);t(i,this)}if(i===e)set_flag(i,Tn);return i}}function def_optimize(e,t){e.DEFMETHOD("optimize",(function(e){var n=this;if(has_flag(n,kn))return n;if(e.has_directive("use asm"))return n;var i=t(n,e);set_flag(i,kn);return i}))}def_optimize(z,(function(e){return e}));oe.DEFMETHOD("drop_console",(function(){return this.transform(new TreeTransformer((function(e){if(e.TYPE=="Call"){var t=e.expression;if(t instanceof Xe){var n=t.expression;while(n.expression){n=n.expression}if(is_undeclared_ref(n)&&n.name=="console"){return make_node($t,e)}}}})))}));z.DEFMETHOD("equivalent_to",(function(e){return equivalent_to(this,e)}));re.DEFMETHOD("process_expression",(function(e,t){var n=this;var i=new TreeTransformer((function(r){if(e&&r instanceof H){return make_node(ge,r,{value:r.body})}if(!e&&r instanceof ge){if(t){var o=r.value&&r.value.drop_side_effect_free(t,true);return o?make_node(H,r,{body:o}):make_node(q,r)}return make_node(H,r,{body:r.value||make_node(Ze,r,{operator:"void",expression:make_node(Ht,r,{value:0})})})}if(r instanceof pt||r instanceof se&&r!==n){return r}if(r instanceof X){var a=r.body.length-1;if(a>=0){r.body[a]=r.body[a].transform(i)}}else if(r instanceof Te){r.body=r.body.transform(i);if(r.alternative){r.alternative=r.alternative.transform(i)}}else if(r instanceof ie){r.body=r.body.transform(i)}return r}));n.transform(i)}));oe.DEFMETHOD("reset_opt_flags",(function(e){const t=this;const n=e.option("reduce_vars");const i=new TreeWalker((function(r,o){clear_flag(r,Rn);if(n){if(e.top_retain&&r instanceof fe&&i.parent()===t){set_flag(r,Cn)}return r.reduce_vars(i,o,e)}}));i.safe_ids=Object.create(null);i.in_loop=null;i.loop_ids=new Map;i.defs_to_safe_ids=new Map;t.walk(i)}));gt.DEFMETHOD("fixed_value",(function(){var e=this.thedef.fixed;if(!e||e instanceof z)return e;return e()}));Pt.DEFMETHOD("is_immutable",(function(){var e=this.definition().orig;return e.length==1&&e[0]instanceof Ft}));function find_variable(e,t){var n,i=0;while(n=e.parent(i++)){if(n instanceof re)break;if(n instanceof xe&&n.argname){n=n.argname.definition().scope;break}}return n.find_variable(t)}var Wn=makePredicate("Array Boolean clearInterval clearTimeout console Date decodeURI decodeURIComponent encodeURI encodeURIComponent Error escape eval EvalError Function isFinite isNaN JSON Math Number parseFloat parseInt RangeError ReferenceError RegExp Object setInterval setTimeout String SyntaxError TypeError unescape URIError");Pt.DEFMETHOD("is_declared",(function(e){return!this.definition().undeclared||e.option("unsafe")&&Wn.has(this.name)}));var qn=new Set(["use asm","use strict"]);def_optimize(G,(function(e,t){if(t.option("directives")&&(!qn.has(e.value)||t.has_directive(e.value)!==e)){return make_node(q,e)}return e}));def_optimize(K,(function(e,t){if(t.option("drop_debugger"))return make_node(q,e);return e}));def_optimize(j,(function(e,t){if(e.body instanceof be&&t.loopcontrol_target(e.body)===e.body){return make_node(q,e)}return e.label.references.length==0?e.body:e}));def_optimize(X,(function(e,t){tighten_body(e.body,t);return e}));function can_be_extracted_from_if_block(e){return!(e instanceof Pe||e instanceof Ie||e instanceof pt)}def_optimize(W,(function(e,t){tighten_body(e.body,t);switch(e.body.length){case 1:if(!t.has_directive("use strict")&&t.parent()instanceof Te&&can_be_extracted_from_if_block(e.body[0])||can_be_evicted_from_block(e.body[0])){return e.body[0]}break;case 0:return make_node(q,e)}return e}));function opt_AST_Lambda(e,t){tighten_body(e.body,t);if(t.option("side_effects")&&e.body.length==1&&e.body[0]===t.has_directive("use strict")){e.body.length=0}return e}def_optimize(se,opt_AST_Lambda);const Yn=/keep_assign/;re.DEFMETHOD("drop_unused",(function(e){if(!e.option("unused"))return;if(e.has_directive("use asm"))return;var t=this;if(t.pinned())return;var n=!(t instanceof oe)||e.toplevel.funcs;var r=!(t instanceof oe)||e.toplevel.vars;const o=Yn.test(e.option("unused"))?return_false:function(e){if(e instanceof tt&&!e.logical&&(has_flag(e,An)||e.operator=="=")){return e.left}if(e instanceof $e&&has_flag(e,An)){return e.expression}};var a=new Map;var s=new Map;if(t instanceof oe&&e.top_retain){t.variables.forEach((function(t){if(e.top_retain(t)&&!a.has(t.id)){a.set(t.id,t)}}))}var u=new Map;var l=new Map;var c=this;var f=new TreeWalker((function(i,o){if(i instanceof se&&i.uses_arguments&&!f.has_directive("use strict")){i.argnames.forEach((function(e){if(!(e instanceof Dt))return;var t=e.definition();if(!a.has(t.id)){a.set(t.id,t)}}))}if(i===t)return;if(i instanceof fe||i instanceof ht){var _=i.name.definition();const r=f.parent()instanceof Ue;if(r||!n&&c===t){if(_.global&&!a.has(_.id)){a.set(_.id,_)}}if(i instanceof ht){if(i.extends&&(i.extends.has_side_effects(e)||i.extends.may_throw(e))){i.extends.walk(f)}for(const t of i.properties){if(t.has_side_effects(e)||t.may_throw(e)){t.walk(f)}}}map_add(l,_.id,i);return true}if(i instanceof Tt&&c===t){map_add(u,i.definition().id,i)}if(i instanceof we&&c===t){const t=f.parent()instanceof Ue;i.definitions.forEach((function(n){if(n.name instanceof bt){map_add(u,n.name.definition().id,n)}if(t||!r){walk(n.name,(e=>{if(e instanceof Dt){const n=e.definition();if((t||n.global)&&!a.has(n.id)){a.set(n.id,n)}}}))}if(n.value){if(n.name instanceof _e){n.walk(f)}else{var i=n.name.definition();map_add(l,i.id,n.value);if(!i.chained&&n.name.fixed_value()===n.value){s.set(i.id,n)}}if(n.value.has_side_effects(e)){n.value.walk(f)}}}));return true}return scan_ref_scoped(i,o)}));t.walk(f);f=new TreeWalker(scan_ref_scoped);a.forEach((function(e){var t=l.get(e.id);if(t)t.forEach((function(e){e.walk(f)}))}));var _=new TreeTransformer((function before(l,f,p){var d=_.parent();if(r){const e=o(l);if(e instanceof Pt){var m=e.definition();var h=a.has(m.id);if(l instanceof tt){if(!h||s.has(m.id)&&s.get(m.id)!==l){return maintain_this_binding(d,l,l.right.transform(_))}}else if(!h)return p?i.skip:make_node(Ht,l,{value:0})}}if(c!==t)return;var m;if(l.name&&(l instanceof Et&&!keep_name(e.option("keep_classnames"),(m=l.name.definition()).name)||l instanceof le&&!keep_name(e.option("keep_fnames"),(m=l.name.definition()).name))){if(!a.has(m.id)||m.orig.length>1)l.name=null}if(l instanceof se&&!(l instanceof ue)){var E=!e.option("keep_fargs");for(var g=l.argnames,v=g.length;--v>=0;){var D=g[v];if(D instanceof ae){D=D.expression}if(D instanceof nt){D=D.left}if(!(D instanceof _e)&&!a.has(D.definition().id)){set_flag(D,vn);if(E){g.pop()}}else{E=false}}}if((l instanceof fe||l instanceof ht)&&l!==t){const t=l.name.definition();let r=t.global&&!n||a.has(t.id);if(!r){t.eliminated++;if(l instanceof ht){const t=l.drop_side_effect_free(e);if(t){return make_node(H,l,{body:t})}}return p?i.skip:make_node(q,l)}}if(l instanceof we&&!(d instanceof te&&d.init===l)){var b=!(d instanceof oe)&&!(l instanceof Ne);var y=[],S=[],A=[];var T=[];l.definitions.forEach((function(t){if(t.value)t.value=t.value.transform(_);var n=t.name instanceof _e;var i=n?new SymbolDef(null,{name:"<destructure>"}):t.name.definition();if(b&&i.global)return A.push(t);if(!(r||b)||n&&(t.name.names.length||t.name.is_array||e.option("pure_getters")!=true)||a.has(i.id)){if(t.value&&s.has(i.id)&&s.get(i.id)!==t){t.value=t.value.drop_side_effect_free(e)}if(t.name instanceof bt){var o=u.get(i.id);if(o.length>1&&(!t.value||i.orig.indexOf(t.name)>i.eliminated)){if(t.value){var c=make_node(Pt,t.name,t.name);i.references.push(c);var f=make_node(tt,t,{operator:"=",logical:false,left:c,right:t.value});if(s.get(i.id)===t){s.set(i.id,f)}T.push(f.transform(_))}remove(o,t);i.eliminated++;return}}if(t.value){if(T.length>0){if(A.length>0){T.push(t.value);t.value=make_sequence(t.value,T)}else{y.push(make_node(H,l,{body:make_sequence(l,T)}))}T=[]}A.push(t)}else{S.push(t)}}else if(i.orig[0]instanceof Mt){var p=t.value&&t.value.drop_side_effect_free(e);if(p)T.push(p);t.value=null;S.push(t)}else{var p=t.value&&t.value.drop_side_effect_free(e);if(p){T.push(p)}i.eliminated++}}));if(S.length>0||A.length>0){l.definitions=S.concat(A);y.push(l)}if(T.length>0){y.push(make_node(H,l,{body:make_sequence(l,T)}))}switch(y.length){case 0:return p?i.skip:make_node(q,l);case 1:return y[0];default:return p?i.splice(y):make_node(W,l,{body:y})}}if(l instanceof ee){f(l,this);var k;if(l.init instanceof W){k=l.init;l.init=k.body.pop();k.body.push(l)}if(l.init instanceof H){l.init=l.init.body}else if(is_empty(l.init)){l.init=null}return!k?l:p?i.splice(k.body):k}if(l instanceof j&&l.body instanceof ee){f(l,this);if(l.body instanceof W){var k=l.body;l.body=k.body.pop();k.body.push(l);return p?i.splice(k.body):k}return l}if(l instanceof W){f(l,this);if(p&&l.body.every(can_be_evicted_from_block)){return i.splice(l.body)}return l}if(l instanceof re){const e=c;c=l;f(l,this);c=e;return l}}));t.transform(_);function scan_ref_scoped(e,n){var i;const r=o(e);if(r instanceof Pt&&!is_ref_of(e.left,yt)&&t.variables.get(r.name)===(i=r.definition())){if(e instanceof tt){e.right.walk(f);if(!i.chained&&e.left.fixed_value()===e.right){s.set(i.id,e)}}return true}if(e instanceof Pt){i=e.definition();if(!a.has(i.id)){a.set(i.id,i);if(i.orig[0]instanceof Mt){const e=i.scope.is_block_scope()&&i.scope.get_defun_scope().variables.get(i.name);if(e)a.set(e.id,e)}}return true}if(e instanceof re){var u=c;c=e;n();c=u;return true}}}));re.DEFMETHOD("hoist_declarations",(function(e){var t=this;if(e.has_directive("use asm"))return t;if(!Array.isArray(t.body))return t;var n=e.option("hoist_funs");var i=e.option("hoist_vars");if(n||i){var r=[];var o=[];var a=new Map,s=0,u=0;walk(t,(e=>{if(e instanceof re&&e!==t)return true;if(e instanceof Ne){++u;return true}}));i=i&&u>1;var l=new TreeTransformer((function before(u){if(u!==t){if(u instanceof G){r.push(u);return make_node(q,u)}if(n&&u instanceof fe&&!(l.parent()instanceof Ue)&&l.parent()===t){o.push(u);return make_node(q,u)}if(i&&u instanceof Ne&&!u.definitions.some((e=>e.name instanceof _e))){u.definitions.forEach((function(e){a.set(e.name.name,e);++s}));var c=u.to_assignments(e);var f=l.parent();if(f instanceof te&&f.init===u){if(c==null){var _=u.definitions[0].name;return make_node(Pt,_,_)}return c}if(f instanceof ee&&f.init===u){return c}if(!c)return make_node(q,u);return make_node(H,u,{body:c})}if(u instanceof re)return u}}));t=t.transform(l);if(s>0){var c=[];const e=t instanceof se;const n=e?t.args_as_names():null;a.forEach(((t,i)=>{if(e&&n.some((e=>e.name===t.name.name))){a.delete(i)}else{t=t.clone();t.value=null;c.push(t);a.set(i,t)}}));if(c.length>0){for(var f=0;f<t.body.length;){if(t.body[f]instanceof H){var _=t.body[f].body,p,d;if(_ instanceof tt&&_.operator=="="&&(p=_.left)instanceof gt&&a.has(p.name)){var m=a.get(p.name);if(m.value)break;m.value=_.right;remove(c,m);c.push(m);t.body.splice(f,1);continue}if(_ instanceof He&&(d=_.expressions[0])instanceof tt&&d.operator=="="&&(p=d.left)instanceof gt&&a.has(p.name)){var m=a.get(p.name);if(m.value)break;m.value=d.right;remove(c,m);c.push(m);t.body[f].body=make_sequence(_,_.expressions.slice(1));continue}}if(t.body[f]instanceof q){t.body.splice(f,1);continue}if(t.body[f]instanceof W){t.body.splice(f,1,...t.body[f].body);continue}break}c=make_node(Ne,t,{definitions:c});o.push(c)}}t.body=r.concat(o,t.body)}return t}));re.DEFMETHOD("hoist_properties",(function(e){var t=this;if(!e.option("hoist_props")||e.has_directive("use asm"))return t;var n=t instanceof oe&&e.top_retain||return_false;var r=new Map;var o=new TreeTransformer((function(a,s){if(a instanceof we&&o.parent()instanceof Ue)return a;if(a instanceof Be){const u=a.name;let l;let c;if(u.scope===t&&(l=u.definition()).escaped!=1&&!l.assignments&&!l.direct_access&&!l.single_use&&!e.exposed(l)&&!n(l)&&(c=u.fixed_value())===a.value&&c instanceof rt&&!c.properties.some((e=>e instanceof ae||e.computed_key()))){s(a,this);const e=new Map;const n=[];c.properties.forEach((({key:i,value:r})=>{const s=o.find_scope();const l=t.create_symbol(u.CTOR,{source:u,scope:s,conflict_scopes:new Set([s,...u.definition().references.map((e=>e.scope))]),tentative_name:u.name+"_"+i});e.set(String(i),l.definition());n.push(make_node(Be,a,{name:l,value:r}))}));r.set(l.id,e);return i.splice(n)}}else if(a instanceof Xe&&a.expression instanceof Pt){const e=r.get(a.expression.definition().id);if(e){const t=e.get(String(get_simple_key(a.property)));const n=make_node(Pt,a,{name:t.name,scope:a.expression.scope,thedef:t});n.reference({});return n}}}));return t.transform(o)}));def_optimize(H,(function(e,t){if(t.option("side_effects")){var n=e.body;var i=n.drop_side_effect_free(t,true);if(!i){return make_node(q,e)}if(i!==n){return make_node(H,e,{body:i})}}return e}));def_optimize(J,(function(e,t){return t.option("loops")?make_node(ee,e,e).optimize(t):e}));def_optimize(Q,(function(e,t){if(!t.option("loops"))return e;var n=e.condition.tail_node().evaluate(t);if(!(n instanceof z)){if(n)return make_node(ee,e,{body:make_node(W,e.body,{body:[e.body,make_node(H,e.condition,{body:e.condition})]})}).optimize(t);if(!has_break_or_continue(e,t.parent())){return make_node(W,e.body,{body:[e.body,make_node(H,e.condition,{body:e.condition})]}).optimize(t)}}return e}));function if_break_in_loop(e,t){var n=e.body instanceof W?e.body.body[0]:e.body;if(t.option("dead_code")&&is_break(n)){var i=[];if(e.init instanceof U){i.push(e.init)}else if(e.init){i.push(make_node(H,e.init,{body:e.init}))}if(e.condition){i.push(make_node(H,e.condition,{body:e.condition}))}trim_unreachable_code(t,e.body,i);return make_node(W,e,{body:i})}if(n instanceof Te){if(is_break(n.body)){if(e.condition){e.condition=make_node(Je,e.condition,{left:e.condition,operator:"&&",right:n.condition.negate(t)})}else{e.condition=n.condition.negate(t)}drop_it(n.alternative)}else if(is_break(n.alternative)){if(e.condition){e.condition=make_node(Je,e.condition,{left:e.condition,operator:"&&",right:n.condition})}else{e.condition=n.condition}drop_it(n.body)}}return e;function is_break(e){return e instanceof be&&t.loopcontrol_target(e)===t.self()}function drop_it(n){n=as_statement_array(n);if(e.body instanceof W){e.body=e.body.clone();e.body.body=n.concat(e.body.body.slice(1));e.body=e.body.transform(t)}else{e.body=make_node(W,e.body,{body:n}).transform(t)}e=if_break_in_loop(e,t)}}def_optimize(ee,(function(e,t){if(!t.option("loops"))return e;if(t.option("side_effects")&&e.init){e.init=e.init.drop_side_effect_free(t)}if(e.condition){var n=e.condition.evaluate(t);if(!(n instanceof z)){if(n)e.condition=null;else if(!t.option("dead_code")){var i=e.condition;e.condition=make_node_from_constant(n,e.condition);e.condition=best_of_expression(e.condition.transform(t),i)}}if(t.option("dead_code")){if(n instanceof z)n=e.condition.tail_node().evaluate(t);if(!n){var r=[];trim_unreachable_code(t,e.body,r);if(e.init instanceof U){r.push(e.init)}else if(e.init){r.push(make_node(H,e.init,{body:e.init}))}r.push(make_node(H,e.condition,{body:e.condition}));return make_node(W,e,{body:r}).optimize(t)}}}return if_break_in_loop(e,t)}));def_optimize(Te,(function(e,t){if(is_empty(e.alternative))e.alternative=null;if(!t.option("conditionals"))return e;var n=e.condition.evaluate(t);if(!t.option("dead_code")&&!(n instanceof z)){var i=e.condition;e.condition=make_node_from_constant(n,i);e.condition=best_of_expression(e.condition.transform(t),i)}if(t.option("dead_code")){if(n instanceof z)n=e.condition.tail_node().evaluate(t);if(!n){var r=[];trim_unreachable_code(t,e.body,r);r.push(make_node(H,e.condition,{body:e.condition}));if(e.alternative)r.push(e.alternative);return make_node(W,e,{body:r}).optimize(t)}else if(!(n instanceof z)){var r=[];r.push(make_node(H,e.condition,{body:e.condition}));r.push(e.body);if(e.alternative){trim_unreachable_code(t,e.alternative,r)}return make_node(W,e,{body:r}).optimize(t)}}var o=e.condition.negate(t);var a=e.condition.size();var s=o.size();var u=s<a;if(e.alternative&&u){u=false;e.condition=o;var l=e.body;e.body=e.alternative||make_node(q,e);e.alternative=l}if(is_empty(e.body)&&is_empty(e.alternative)){return make_node(H,e.condition,{body:e.condition.clone()}).optimize(t)}if(e.body instanceof H&&e.alternative instanceof H){return make_node(H,e,{body:make_node(et,e,{condition:e.condition,consequent:e.body.body,alternative:e.alternative.body})}).optimize(t)}if(is_empty(e.alternative)&&e.body instanceof H){if(a===s&&!u&&e.condition instanceof Je&&e.condition.operator=="||"){u=true}if(u)return make_node(H,e,{body:make_node(Je,e,{operator:"||",left:o,right:e.body.body})}).optimize(t);return make_node(H,e,{body:make_node(Je,e,{operator:"&&",left:e.condition,right:e.body.body})}).optimize(t)}if(e.body instanceof q&&e.alternative instanceof H){return make_node(H,e,{body:make_node(Je,e,{operator:"||",left:e.condition,right:e.alternative.body})}).optimize(t)}if(e.body instanceof Ee&&e.alternative instanceof Ee&&e.body.TYPE==e.alternative.TYPE){return make_node(e.body.CTOR,e,{value:make_node(et,e,{condition:e.condition,consequent:e.body.value||make_node($t,e.body),alternative:e.alternative.value||make_node($t,e.alternative)}).transform(t)}).optimize(t)}if(e.body instanceof Te&&!e.body.alternative&&!e.alternative){e=make_node(Te,e,{condition:make_node(Je,e.condition,{operator:"&&",left:e.condition,right:e.body.condition}),body:e.body.body,alternative:null})}if(aborts(e.body)){if(e.alternative){var c=e.alternative;e.alternative=null;return make_node(W,e,{body:[e,c]}).optimize(t)}}if(aborts(e.alternative)){var r=e.body;e.body=e.alternative;e.condition=u?o:e.condition.negate(t);e.alternative=null;return make_node(W,e,{body:[e,r]}).optimize(t)}return e}));def_optimize(ke,(function(e,t){if(!t.option("switches"))return e;var n;var i=e.expression.evaluate(t);if(!(i instanceof z)){var r=e.expression;e.expression=make_node_from_constant(i,r);e.expression=best_of_expression(e.expression.transform(t),r)}if(!t.option("dead_code"))return e;if(i instanceof z){i=e.expression.tail_node().evaluate(t)}var o=[];var a=[];var s;var u;for(var l=0,c=e.body.length;l<c&&!u;l++){n=e.body[l];if(n instanceof Re){if(!s){s=n}else{eliminate_branch(n,a[a.length-1])}}else if(!(i instanceof z)){var f=n.expression.evaluate(t);if(!(f instanceof z)&&f!==i){eliminate_branch(n,a[a.length-1]);continue}if(f instanceof z)f=n.expression.tail_node().evaluate(t);if(f===i){u=n;if(s){var _=a.indexOf(s);a.splice(_,1);eliminate_branch(s,a[_-1]);s=null}}}a.push(n)}while(l<c)eliminate_branch(e.body[l++],a[a.length-1]);e.body=a;let p=s||u;s=null;u=null;if(a.every(((e,t)=>(e===p||e.expression instanceof Kt)&&(e.body.length===0||aborts(e)||a.length-1===t)))){for(let e=0;e<a.length;e++){const t=a[e];for(let n=e+1;n<a.length;n++){const i=a[n];if(i.body.length===0)continue;const r=n===a.length-1;const o=branches_equivalent(i,t,false);if(o||r&&branches_equivalent(i,t,true)){if(!o&&r){i.body.push(make_node(be))}let t=n-1;let s=0;while(t>e){if(is_inert_body(a[t--])){s++}else{break}}const u=a.splice(n-s,1+s);a.splice(e+1,0,...u);e+=u.length}}}}for(let e=0;e<a.length;e++){let t=a[e];if(t.body.length===0)continue;if(!aborts(t))continue;for(let n=e+1;n<a.length;e++,n++){let e=a[n];if(e.body.length===0)continue;if(branches_equivalent(e,t,false)||n===a.length-1&&branches_equivalent(e,t,true)){t.body=[];t=e;continue}break}}{let e=a.length-1;for(;e>=0;e--){let n=a[e].body;if(is_break(n[n.length-1],t))n.pop();if(!is_inert_body(a[e]))break}e++;if(!p||a.indexOf(p)>=e){for(let n=a.length-1;n>=e;n--){let e=a[n];if(e===p){p=null;a.pop()}else if(!e.expression.has_side_effects(t)){a.pop()}else{break}}}}e:if(p){let e=a.indexOf(p);let n=e;for(;n<a.length-1;n++){if(!is_inert_body(a[n]))break}if(n<a.length-1){break e}let i=a.length-1;for(;i>=0;i--){let e=a[i];if(e===p)continue;if(e.expression.has_side_effects(t))break}if(n>i){let t=e-1;for(;t>=0;t--){if(!is_inert_body(a[t]))break}let r=Math.max(i,t)+1;let o=e;if(i>e){o=i;a[i].body=a[n].body}else{p.body=a[n].body}a.splice(o+1,n-o);a.splice(r,e-r)}}e:if(p){let n=a.findIndex((e=>!is_inert_body(e)));let i;if(n===a.length-1){let t=a[n];if(has_nested_break(e))break e;i=make_node(W,t,{body:t.body});t.body=[]}else if(n!==-1){break e}let r=a.find((e=>e!==p&&e.expression.has_side_effects(t)));if(!r){return make_node(W,e,{body:o.concat(statement(e.expression),p.expression?statement(p.expression):[],i||[])}).optimize(t)}const s=a.indexOf(p);a.splice(s,1);p=null;if(i){return make_node(W,e,{body:o.concat(e,i)}).optimize(t)}}if(a.length>0){a[0].body=o.concat(a[0].body)}if(a.length==0){return make_node(W,e,{body:o.concat(statement(e.expression))}).optimize(t)}if(a.length==1&&!has_nested_break(e)){let n=a[0];return make_node(Te,e,{condition:make_node(Je,e,{operator:"===",left:e.expression,right:n.expression}),body:make_node(W,n,{body:n.body}),alternative:null}).optimize(t)}if(a.length===2&&p&&!has_nested_break(e)){let n=a[0]===p?a[1]:a[0];let i=p.expression&&statement(p.expression);if(aborts(a[0])){let r=a[0];if(is_break(r.body[r.body.length-1],t)){r.body.pop()}return make_node(Te,e,{condition:make_node(Je,e,{operator:"===",left:e.expression,right:n.expression}),body:make_node(W,n,{body:n.body}),alternative:make_node(W,p,{body:[].concat(i||[],p.body)})}).optimize(t)}let r="===";let o=make_node(W,n,{body:n.body});let s=make_node(W,p,{body:[].concat(i||[],p.body)});if(a[0]===p){r="!==";let e=s;s=o;o=e}return make_node(W,e,{body:[make_node(Te,e,{condition:make_node(Je,e,{operator:r,left:e.expression,right:n.expression}),body:o,alternative:null})].concat(s)}).optimize(t)}return e;function eliminate_branch(e,n){if(n&&!aborts(n)){n.body=n.body.concat(e.body)}else{trim_unreachable_code(t,e,o)}}function branches_equivalent(e,t,n){let i=e.body;let r=t.body;if(n){i=i.concat(make_node(be))}if(i.length!==r.length)return false;let o=make_node(W,e,{body:i});let a=make_node(W,t,{body:r});return o.equivalent_to(a)}function statement(e){return make_node(H,e,{body:e})}function has_nested_break(e){let t=false;let n=new TreeWalker((e=>{if(t)return true;if(e instanceof se)return true;if(e instanceof H)return true;if(!is_break(e,n))return;let i=n.parent();if(i instanceof Ce&&i.body[i.body.length-1]===e){return}t=true}));e.walk(n);return t}function is_break(t,n){return t instanceof be&&n.loopcontrol_target(t)===e}function is_inert_body(e){return!aborts(e)&&!make_node(W,e,{body:e.body}).has_side_effects(t)}}));def_optimize(Oe,(function(e,t){tighten_body(e.body,t);if(e.bcatch&&e.bfinally&&e.bfinally.body.every(is_empty))e.bfinally=null;if(t.option("dead_code")&&e.body.every(is_empty)){var n=[];if(e.bcatch){trim_unreachable_code(t,e.bcatch,n)}if(e.bfinally)n.push(...e.bfinally.body);return make_node(W,e,{body:n}).optimize(t)}return e}));we.DEFMETHOD("remove_initializers",(function(){var e=[];this.definitions.forEach((function(t){if(t.name instanceof Dt){t.value=null;e.push(t)}else{walk(t.name,(n=>{if(n instanceof Dt){e.push(make_node(Be,t,{name:n,value:null}))}}))}}));this.definitions=e}));we.DEFMETHOD("to_assignments",(function(e){var t=e.option("reduce_vars");var n=[];for(const e of this.definitions){if(e.value){var i=make_node(Pt,e.name,e.name);n.push(make_node(tt,e,{operator:"=",logical:false,left:i,right:e.value}));if(t)i.definition().fixed=false}else if(e.value){var r=make_node(Be,e,{name:e.name,value:e.value});var o=make_node(Ne,e,{definitions:[r]});n.push(o)}const a=e.name.definition();a.eliminated++;a.replaced--}if(n.length==0)return null;return make_sequence(this,n)}));def_optimize(we,(function(e){if(e.definitions.length==0)return make_node(q,e);return e}));def_optimize(Be,(function(e,t){if(e.name instanceof At&&e.value!=null&&is_undefined(e.value,t)){e.value=null}return e}));def_optimize(Ve,(function(e){return e}));def_optimize(Ke,(function(e,t){var n=e.expression;var i=n;inline_array_like_spread(e.args);var r=e.args.every((e=>!(e instanceof ae)));if(t.option("reduce_vars")&&i instanceof Pt&&!has_annotation(e,an)){const e=i.fixed_value();if(!retain_top_func(e,t)){i=e}}var o=i instanceof se;if(o&&i.pinned())return e;if(t.option("unused")&&r&&o&&!i.uses_arguments){var a=0,s=0;for(var u=0,l=e.args.length;u<l;u++){if(i.argnames[u]instanceof ae){if(has_flag(i.argnames[u].expression,vn))while(u<l){var c=e.args[u++].drop_side_effect_free(t);if(c){e.args[a++]=c}}else while(u<l){e.args[a++]=e.args[u++]}s=a;break}var f=u>=i.argnames.length;if(f||has_flag(i.argnames[u],vn)){var c=e.args[u].drop_side_effect_free(t);if(c){e.args[a++]=c}else if(!f){e.args[a++]=make_node(Ht,e.args[u],{value:0});continue}}else{e.args[a++]=e.args[u]}s=a}e.args.length=s}if(t.option("unsafe")){if(n instanceof We&&n.start.value==="Array"&&n.property==="from"&&e.args.length===1){const[n]=e.args;if(n instanceof it){return make_node(it,n,{elements:n.elements}).optimize(t)}}if(is_undeclared_ref(n))switch(n.name){case"Array":if(e.args.length!=1){return make_node(it,e,{elements:e.args}).optimize(t)}else if(e.args[0]instanceof Ht&&e.args[0].value<=11){const t=[];for(let n=0;n<e.args[0].value;n++)t.push(new Zt);return new it({elements:t})}break;case"Object":if(e.args.length==0){return make_node(rt,e,{properties:[]})}break;case"String":if(e.args.length==0)return make_node(Gt,e,{value:""});if(e.args.length<=1)return make_node(Je,e,{left:e.args[0],operator:"+",right:make_node(Gt,e,{value:""})}).optimize(t);break;case"Number":if(e.args.length==0)return make_node(Ht,e,{value:0});if(e.args.length==1&&t.option("unsafe_math")){return make_node(Ze,e,{expression:e.args[0],operator:"+"}).optimize(t)}break;case"Symbol":if(e.args.length==1&&e.args[0]instanceof Gt&&t.option("unsafe_symbols"))e.args.length=0;break;case"Boolean":if(e.args.length==0)return make_node(en,e);if(e.args.length==1)return make_node(Ze,e,{expression:make_node(Ze,e,{expression:e.args[0],operator:"!"}),operator:"!"}).optimize(t);break;case"RegExp":var _=[];if(e.args.length>=1&&e.args.length<=2&&e.args.every((e=>{var n=e.evaluate(t);_.push(n);return e!==n}))){let[n,i]=_;n=regexp_source_fix(new RegExp(n).source);const r=make_node(Wt,e,{value:{source:n,flags:i}});if(r._eval(t)!==r){return r}}break}else if(n instanceof We)switch(n.property){case"toString":if(e.args.length==0&&!n.expression.may_throw_on_access(t)){return make_node(Je,e,{left:make_node(Gt,e,{value:""}),operator:"+",right:n.expression}).optimize(t)}break;case"join":if(n.expression instanceof it)e:{var p;if(e.args.length>0){p=e.args[0].evaluate(t);if(p===e.args[0])break e}var d=[];var m=[];for(var u=0,l=n.expression.elements.length;u<l;u++){var h=n.expression.elements[u];if(h instanceof ae)break e;var E=h.evaluate(t);if(E!==h){m.push(E)}else{if(m.length>0){d.push(make_node(Gt,e,{value:m.join(p)}));m.length=0}d.push(h)}}if(m.length>0){d.push(make_node(Gt,e,{value:m.join(p)}))}if(d.length==0)return make_node(Gt,e,{value:""});if(d.length==1){if(d[0].is_string(t)){return d[0]}return make_node(Je,d[0],{operator:"+",left:make_node(Gt,e,{value:""}),right:d[0]})}if(p==""){var g;if(d[0].is_string(t)||d[1].is_string(t)){g=d.shift()}else{g=make_node(Gt,e,{value:""})}return d.reduce((function(e,t){return make_node(Je,t,{operator:"+",left:e,right:t})}),g).optimize(t)}var c=e.clone();c.expression=c.expression.clone();c.expression.expression=c.expression.expression.clone();c.expression.expression.elements=d;return best_of(t,e,c)}break;case"charAt":if(n.expression.is_string(t)){var v=e.args[0];var D=v?v.evaluate(t):0;if(D!==v){return make_node(Ye,n,{expression:n.expression,property:make_node_from_constant(D|0,v||n)}).optimize(t)}}break;case"apply":if(e.args.length==2&&e.args[1]instanceof it){var b=e.args[1].elements.slice();b.unshift(e.args[0]);return make_node(Ke,e,{expression:make_node(We,n,{expression:n.expression,optional:false,property:"call"}),args:b}).optimize(t)}break;case"call":var y=n.expression;if(y instanceof Pt){y=y.fixed_value()}if(y instanceof se&&!y.contains_this()){return(e.args.length?make_sequence(this,[e.args[0],make_node(Ke,e,{expression:n.expression,args:e.args.slice(1)})]):make_node(Ke,e,{expression:n.expression,args:[]})).optimize(t)}break}}if(t.option("unsafe_Function")&&is_undeclared_ref(n)&&n.name=="Function"){if(e.args.length==0)return make_node(le,e,{argnames:[],body:[]}).optimize(t);var S=t.mangle_options&&t.mangle_options.nth_identifier||En;if(e.args.every((e=>e instanceof Gt))){try{var A="n(function("+e.args.slice(0,-1).map((function(e){return e.value})).join(",")+"){"+e.args[e.args.length-1].value+"})";var T=parse(A);var k={ie8:t.option("ie8"),nth_identifier:S};T.figure_out_scope(k);var C=new Compressor(t.options,{mangle_options:t.mangle_options});T=T.transform(C);T.figure_out_scope(k);T.compute_char_frequency(k);T.mangle_names(k);var R;walk(T,(e=>{if(is_func_expr(e)){R=e;return nn}}));var A=OutputStream();W.prototype._codegen.call(R,R,A);e.args=[make_node(Gt,e,{value:R.argnames.map((function(e){return e.print_to_string()})).join(",")}),make_node(Gt,e.args[e.args.length-1],{value:A.get().replace(/^{|}$/g,"")})];return e}catch(e){if(!(e instanceof JS_Parse_Error)){throw e}}}}return inline_into_call(e,i,t)}));def_optimize(Ge,(function(e,t){if(t.option("unsafe")&&is_undeclared_ref(e.expression)&&["Object","RegExp","Function","Error","Array"].includes(e.expression.name))return make_node(Ke,e,e).transform(t);return e}));def_optimize(He,(function(e,t){if(!t.option("side_effects"))return e;var n=[];filter_for_side_effects();var i=n.length-1;trim_right_for_undefined();if(i==0){e=maintain_this_binding(t.parent(),t.self(),n[0]);if(!(e instanceof He))e=e.optimize(t);return e}e.expressions=n;return e;function filter_for_side_effects(){var i=first_in_statement(t);var r=e.expressions.length-1;e.expressions.forEach((function(e,o){if(o<r)e=e.drop_side_effect_free(t,i);if(e){merge_sequence(n,e);i=false}}))}function trim_right_for_undefined(){while(i>0&&is_undefined(n[i],t))i--;if(i<n.length-1){n[i]=make_node(Ze,e,{operator:"void",expression:n[i]});n.length=i+1}}}));$e.DEFMETHOD("lift_sequences",(function(e){if(e.option("sequences")){if(this.expression instanceof He){var t=this.expression.expressions.slice();var n=this.clone();n.expression=t.pop();t.push(n);return make_sequence(this,t).optimize(e)}}return this}));def_optimize(Qe,(function(e,t){return e.lift_sequences(t)}));def_optimize(Ze,(function(e,t){var n=e.expression;if(e.operator=="delete"&&!(n instanceof Pt||n instanceof Xe||n instanceof je||is_identifier_atom(n))){return make_sequence(e,[n,make_node(tn,e)]).optimize(t)}var i=e.lift_sequences(t);if(i!==e){return i}if(t.option("side_effects")&&e.operator=="void"){n=n.drop_side_effect_free(t);if(n){e.expression=n;return e}else{return make_node($t,e).optimize(t)}}if(t.in_boolean_context()){switch(e.operator){case"!":if(n instanceof Ze&&n.operator=="!"){return n.expression}if(n instanceof Je){e=best_of(t,e,n.negate(t,first_in_statement(t)))}break;case"typeof":return(n instanceof Pt?make_node(tn,e):make_sequence(e,[n,make_node(tn,e)])).optimize(t)}}if(e.operator=="-"&&n instanceof Qt){n=n.transform(t)}if(n instanceof Je&&(e.operator=="+"||e.operator=="-")&&(n.operator=="*"||n.operator=="/"||n.operator=="%")){return make_node(Je,e,{operator:n.operator,left:make_node(Ze,n.left,{operator:e.operator,expression:n.left}),right:n.right})}if(e.operator!="-"||!(n instanceof Ht||n instanceof Qt||n instanceof Xt)){var r=e.evaluate(t);if(r!==e){r=make_node_from_constant(r,e).optimize(t);return best_of(t,r,e)}}return e}));Je.DEFMETHOD("lift_sequences",(function(e){if(e.option("sequences")){if(this.left instanceof He){var t=this.left.expressions.slice();var n=this.clone();n.left=t.pop();t.push(n);return make_sequence(this,t).optimize(e)}if(this.right instanceof He&&!this.left.has_side_effects(e)){var i=this.operator=="="&&this.left instanceof Pt;var t=this.right.expressions;var r=t.length-1;for(var o=0;o<r;o++){if(!i&&t[o].has_side_effects(e))break}if(o==r){t=t.slice();var n=this.clone();n.right=t.pop();t.push(n);return make_sequence(this,t).optimize(e)}else if(o>0){var n=this.clone();n.right=make_sequence(this.right,t.slice(o));t=t.slice(0,o);t.push(n);return make_sequence(this,t).optimize(e)}}}return this}));var jn=makePredicate("== === != !== * & | ^");function is_object(e){return e instanceof it||e instanceof se||e instanceof rt||e instanceof pt}def_optimize(Je,(function(e,t){function reversible(){return e.left.is_constant()||e.right.is_constant()||!e.left.has_side_effects(t)&&!e.right.has_side_effects(t)}function reverse(t){if(reversible()){if(t)e.operator=t;var n=e.left;e.left=e.right;e.right=n}}if(jn.has(e.operator)){if(e.right.is_constant()&&!e.left.is_constant()){if(!(e.left instanceof Je&&N[e.left.operator]>=N[e.operator])){reverse()}}}e=e.lift_sequences(t);if(t.option("comparisons"))switch(e.operator){case"===":case"!==":var n=true;if(e.left.is_string(t)&&e.right.is_string(t)||e.left.is_number(t)&&e.right.is_number(t)||e.left.is_boolean()&&e.right.is_boolean()||e.left.equivalent_to(e.right)){e.operator=e.operator.substr(0,2)}case"==":case"!=":if(!n&&is_undefined(e.left,t)){e.left=make_node(Yt,e.left)}else if(t.option("typeofs")&&e.left instanceof Gt&&e.left.value=="undefined"&&e.right instanceof Ze&&e.right.operator=="typeof"){var i=e.right.expression;if(i instanceof Pt?i.is_declared(t):!(i instanceof Xe&&t.option("ie8"))){e.right=i;e.left=make_node($t,e.left).optimize(t);if(e.operator.length==2)e.operator+="="}}else if(e.left instanceof Pt&&e.right instanceof Pt&&e.left.definition()===e.right.definition()&&is_object(e.left.fixed_value())){return make_node(e.operator[0]=="="?tn:en,e)}break;case"&&":case"||":var r=e.left;if(r.operator==e.operator){r=r.right}if(r instanceof Je&&r.operator==(e.operator=="&&"?"!==":"===")&&e.right instanceof Je&&r.operator==e.right.operator&&(is_undefined(r.left,t)&&e.right.left instanceof Yt||r.left instanceof Yt&&is_undefined(e.right.left,t))&&!r.right.has_side_effects(t)&&r.right.equivalent_to(e.right.right)){var o=make_node(Je,e,{operator:r.operator.slice(0,-1),left:make_node(Yt,e),right:r.right});if(r!==e.left){o=make_node(Je,e,{operator:e.operator,left:e.left.left,right:o})}return o}break}if(e.operator=="+"&&t.in_boolean_context()){var a=e.left.evaluate(t);var s=e.right.evaluate(t);if(a&&typeof a=="string"){return make_sequence(e,[e.right,make_node(tn,e)]).optimize(t)}if(s&&typeof s=="string"){return make_sequence(e,[e.left,make_node(tn,e)]).optimize(t)}}if(t.option("comparisons")&&e.is_boolean()){if(!(t.parent()instanceof Je)||t.parent()instanceof tt){var u=make_node(Ze,e,{operator:"!",expression:e.negate(t,first_in_statement(t))});e=best_of(t,e,u)}if(t.option("unsafe_comps")){switch(e.operator){case"<":reverse(">");break;case"<=":reverse(">=");break}}}if(e.operator=="+"){if(e.right instanceof Gt&&e.right.getValue()==""&&e.left.is_string(t)){return e.left}if(e.left instanceof Gt&&e.left.getValue()==""&&e.right.is_string(t)){return e.right}if(e.left instanceof Je&&e.left.operator=="+"&&e.left.left instanceof Gt&&e.left.left.getValue()==""&&e.right.is_string(t)){e.left=e.left.right;return e}}if(t.option("evaluate")){switch(e.operator){case"&&":var a=has_flag(e.left,Dn)?true:has_flag(e.left,bn)?false:e.left.evaluate(t);if(!a){return maintain_this_binding(t.parent(),t.self(),e.left).optimize(t)}else if(!(a instanceof z)){return make_sequence(e,[e.left,e.right]).optimize(t)}var s=e.right.evaluate(t);if(!s){if(t.in_boolean_context()){return make_sequence(e,[e.left,make_node(en,e)]).optimize(t)}else{set_flag(e,bn)}}else if(!(s instanceof z)){var l=t.parent();if(l.operator=="&&"&&l.left===t.self()||t.in_boolean_context()){return e.left.optimize(t)}}if(e.left.operator=="||"){var c=e.left.right.evaluate(t);if(!c)return make_node(et,e,{condition:e.left.left,consequent:e.right,alternative:e.left.right}).optimize(t)}break;case"||":var a=has_flag(e.left,Dn)?true:has_flag(e.left,bn)?false:e.left.evaluate(t);if(!a){return make_sequence(e,[e.left,e.right]).optimize(t)}else if(!(a instanceof z)){return maintain_this_binding(t.parent(),t.self(),e.left).optimize(t)}var s=e.right.evaluate(t);if(!s){var l=t.parent();if(l.operator=="||"&&l.left===t.self()||t.in_boolean_context()){return e.left.optimize(t)}}else if(!(s instanceof z)){if(t.in_boolean_context()){return make_sequence(e,[e.left,make_node(tn,e)]).optimize(t)}else{set_flag(e,Dn)}}if(e.left.operator=="&&"){var c=e.left.right.evaluate(t);if(c&&!(c instanceof z))return make_node(et,e,{condition:e.left.left,consequent:e.left.right,alternative:e.right}).optimize(t)}break;case"??":if(is_nullish(e.left,t)){return e.right}var a=e.left.evaluate(t);if(!(a instanceof z)){return a==null?e.right:e.left}if(t.in_boolean_context()){const n=e.right.evaluate(t);if(!(n instanceof z)&&!n){return e.left}}}var f=true;switch(e.operator){case"+":if(e.right instanceof Kt&&e.left instanceof Je&&e.left.operator=="+"&&e.left.is_string(t)){var _=make_node(Je,e,{operator:"+",left:e.left.right,right:e.right});var p=_.optimize(t);if(_!==p){e=make_node(Je,e,{operator:"+",left:e.left.left,right:p})}}if(e.left instanceof Je&&e.left.operator=="+"&&e.left.is_string(t)&&e.right instanceof Je&&e.right.operator=="+"&&e.right.is_string(t)){var _=make_node(Je,e,{operator:"+",left:e.left.right,right:e.right.left});var d=_.optimize(t);if(_!==d){e=make_node(Je,e,{operator:"+",left:make_node(Je,e.left,{operator:"+",left:e.left.left,right:d}),right:e.right.right})}}if(e.right instanceof Ze&&e.right.operator=="-"&&e.left.is_number(t)){e=make_node(Je,e,{operator:"-",left:e.left,right:e.right.expression});break}if(e.left instanceof Ze&&e.left.operator=="-"&&reversible()&&e.right.is_number(t)){e=make_node(Je,e,{operator:"-",left:e.right,right:e.left.expression});break}if(e.left instanceof de){var m=e.left;var p=e.right.evaluate(t);if(p!=e.right){m.segments[m.segments.length-1].value+=String(p);return m}}if(e.right instanceof de){var p=e.right;var m=e.left.evaluate(t);if(m!=e.left){p.segments[0].value=String(m)+p.segments[0].value;return p}}if(e.left instanceof de&&e.right instanceof de){var m=e.left;var h=m.segments;var p=e.right;h[h.length-1].value+=p.segments[0].value;for(var E=1;E<p.segments.length;E++){h.push(p.segments[E])}return m}case"*":f=t.option("unsafe_math");case"&":case"|":case"^":if(e.left.is_number(t)&&e.right.is_number(t)&&reversible()&&!(e.left instanceof Je&&e.left.operator!=e.operator&&N[e.left.operator]>=N[e.operator])){var g=make_node(Je,e,{operator:e.operator,left:e.right,right:e.left});if(e.right instanceof Kt&&!(e.left instanceof Kt)){e=best_of(t,g,e)}else{e=best_of(t,e,g)}}if(f&&e.is_number(t)){if(e.right instanceof Je&&e.right.operator==e.operator){e=make_node(Je,e,{operator:e.operator,left:make_node(Je,e.left,{operator:e.operator,left:e.left,right:e.right.left,start:e.left.start,end:e.right.left.end}),right:e.right.right})}if(e.right instanceof Kt&&e.left instanceof Je&&e.left.operator==e.operator){if(e.left.left instanceof Kt){e=make_node(Je,e,{operator:e.operator,left:make_node(Je,e.left,{operator:e.operator,left:e.left.left,right:e.right,start:e.left.left.start,end:e.right.end}),right:e.left.right})}else if(e.left.right instanceof Kt){e=make_node(Je,e,{operator:e.operator,left:make_node(Je,e.left,{operator:e.operator,left:e.left.right,right:e.right,start:e.left.right.start,end:e.right.end}),right:e.left.left})}}if(e.left instanceof Je&&e.left.operator==e.operator&&e.left.right instanceof Kt&&e.right instanceof Je&&e.right.operator==e.operator&&e.right.left instanceof Kt){e=make_node(Je,e,{operator:e.operator,left:make_node(Je,e.left,{operator:e.operator,left:make_node(Je,e.left.left,{operator:e.operator,left:e.left.right,right:e.right.left,start:e.left.right.start,end:e.right.left.end}),right:e.left.left}),right:e.right.right})}}}}if(e.right instanceof Je&&e.right.operator==e.operator&&(In.has(e.operator)||e.operator=="+"&&(e.right.left.is_string(t)||e.left.is_string(t)&&e.right.right.is_string(t)))){e.left=make_node(Je,e.left,{operator:e.operator,left:e.left.transform(t),right:e.right.left.transform(t)});e.right=e.right.right.transform(t);return e.transform(t)}var v=e.evaluate(t);if(v!==e){v=make_node_from_constant(v,e).optimize(t);return best_of(t,v,e)}return e}));def_optimize(Bt,(function(e){return e}));def_optimize(Pt,(function(e,t){if(!t.option("ie8")&&is_undeclared_ref(e)&&!t.find_parent(ie)){switch(e.name){case"undefined":return make_node($t,e).optimize(t);case"NaN":return make_node(jt,e).optimize(t);case"Infinity":return make_node(Qt,e).optimize(t)}}const n=t.parent();if(t.option("reduce_vars")&&is_lhs(e,n)!==e){return inline_into_symbolref(e,t)}else{return e}}));function is_atomic(e,t){return e instanceof Pt||e.TYPE===t.TYPE}def_optimize($t,(function(e,t){if(t.option("unsafe_undefined")){var n=find_variable(t,"undefined");if(n){var i=make_node(Pt,e,{name:"undefined",scope:n.scope,thedef:n});set_flag(i,yn);return i}}var r=is_lhs(t.self(),t.parent());if(r&&is_atomic(r,e))return e;return make_node(Ze,e,{operator:"void",expression:make_node(Ht,e,{value:0})})}));def_optimize(Qt,(function(e,t){var n=is_lhs(t.self(),t.parent());if(n&&is_atomic(n,e))return e;if(t.option("keep_infinity")&&!(n&&!is_atomic(n,e))&&!find_variable(t,"Infinity")){return e}return make_node(Je,e,{operator:"/",left:make_node(Ht,e,{value:1}),right:make_node(Ht,e,{value:0})})}));def_optimize(jt,(function(e,t){var n=is_lhs(t.self(),t.parent());if(n&&!is_atomic(n,e)||find_variable(t,"NaN")){return make_node(Je,e,{operator:"/",left:make_node(Ht,e,{value:0}),right:make_node(Ht,e,{value:0})})}return e}));const $n=makePredicate("+ - / * % >> << >>> | ^ &");const Zn=makePredicate("* | ^ &");def_optimize(tt,(function(e,t){if(e.logical){return e.lift_sequences(t)}var n;if(e.operator==="="&&e.left instanceof Pt&&e.left.name!=="arguments"&&!(n=e.left.definition()).undeclared&&e.right.equivalent_to(e.left)){return e.right}if(t.option("dead_code")&&e.left instanceof Pt&&(n=e.left.definition()).scope===t.find_parent(se)){var i=0,r,o=e;do{r=o;o=t.parent(i++);if(o instanceof Ee){if(in_try(i,o))break;if(is_reachable(n.scope,[n]))break;if(e.operator=="=")return e.right;n.fixed=false;return make_node(Je,e,{operator:e.operator.slice(0,-1),left:e.left,right:e.right}).optimize(t)}}while(o instanceof Je&&o.right===r||o instanceof He&&o.tail_node()===r)}e=e.lift_sequences(t);if(e.operator=="="&&e.left instanceof Pt&&e.right instanceof Je){if(e.right.left instanceof Pt&&e.right.left.name==e.left.name&&$n.has(e.right.operator)){e.operator=e.right.operator+"=";e.right=e.right.right}else if(e.right.right instanceof Pt&&e.right.right.name==e.left.name&&Zn.has(e.right.operator)&&!e.right.left.has_side_effects(t)){e.operator=e.right.operator+"=";e.right=e.right.left}}return e;function in_try(n,i){var r=e.right;e.right=make_node(Yt,r);var o=i.may_throw(t);e.right=r;var a=e.left.definition().scope;var s;while((s=t.parent(n++))!==a){if(s instanceof Oe){if(s.bfinally)return true;if(o&&s.bcatch)return true}}}}));def_optimize(nt,(function(e,t){if(!t.option("evaluate")){return e}var n=e.right.evaluate(t);if(n===undefined){e=e.left}else if(n!==e.right){n=make_node_from_constant(n,e.right);e.right=best_of_expression(n,e.right)}return e}));function is_nullish_check(e,t,n){if(t.may_throw(n))return false;let i;if(e instanceof Je&&e.operator==="=="&&((i=is_nullish(e.left,n)&&e.left)||(i=is_nullish(e.right,n)&&e.right))&&(i===e.left?e.right:e.left).equivalent_to(t)){return true}if(e instanceof Je&&e.operator==="||"){let i;let r;const find_comparison=e=>{if(!(e instanceof Je&&(e.operator==="==="||e.operator==="=="))){return false}let o=0;let a;if(e.left instanceof Yt){o++;i=e;a=e.right}if(e.right instanceof Yt){o++;i=e;a=e.left}if(is_undefined(e.left,n)){o++;r=e;a=e.right}if(is_undefined(e.right,n)){o++;r=e;a=e.left}if(o!==1){return false}if(!a.equivalent_to(t)){return false}return true};if(!find_comparison(e.left))return false;if(!find_comparison(e.right))return false;if(i&&r&&i!==r){return true}}return false}def_optimize(et,(function(e,t){if(!t.option("conditionals"))return e;if(e.condition instanceof He){var n=e.condition.expressions.slice();e.condition=n.pop();n.push(e);return make_sequence(e,n)}var i=e.condition.evaluate(t);if(i!==e.condition){if(i){return maintain_this_binding(t.parent(),t.self(),e.consequent)}else{return maintain_this_binding(t.parent(),t.self(),e.alternative)}}var r=i.negate(t,first_in_statement(t));if(best_of(t,i,r)===r){e=make_node(et,e,{condition:r,consequent:e.alternative,alternative:e.consequent})}var o=e.condition;var a=e.consequent;var s=e.alternative;if(o instanceof Pt&&a instanceof Pt&&o.definition()===a.definition()){return make_node(Je,e,{operator:"||",left:o,right:s})}if(a instanceof tt&&s instanceof tt&&a.operator===s.operator&&a.logical===s.logical&&a.left.equivalent_to(s.left)&&(!e.condition.has_side_effects(t)||a.operator=="="&&!a.left.has_side_effects(t))){return make_node(tt,e,{operator:a.operator,left:a.left,logical:a.logical,right:make_node(et,e,{condition:e.condition,consequent:a.right,alternative:s.right})})}var u;if(a instanceof Ke&&s.TYPE===a.TYPE&&a.args.length>0&&a.args.length==s.args.length&&a.expression.equivalent_to(s.expression)&&!e.condition.has_side_effects(t)&&!a.expression.has_side_effects(t)&&typeof(u=single_arg_diff())=="number"){var l=a.clone();l.args[u]=make_node(et,e,{condition:e.condition,consequent:a.args[u],alternative:s.args[u]});return l}if(s instanceof et&&a.equivalent_to(s.consequent)){return make_node(et,e,{condition:make_node(Je,e,{operator:"||",left:o,right:s.condition}),consequent:a,alternative:s.alternative}).optimize(t)}if(t.option("ecma")>=2020&&is_nullish_check(o,s,t)){return make_node(Je,e,{operator:"??",left:s,right:a}).optimize(t)}if(s instanceof He&&a.equivalent_to(s.expressions[s.expressions.length-1])){return make_sequence(e,[make_node(Je,e,{operator:"||",left:o,right:make_sequence(e,s.expressions.slice(0,-1))}),a]).optimize(t)}if(s instanceof Je&&s.operator=="&&"&&a.equivalent_to(s.right)){return make_node(Je,e,{operator:"&&",left:make_node(Je,e,{operator:"||",left:o,right:s.left}),right:a}).optimize(t)}if(a instanceof et&&a.alternative.equivalent_to(s)){return make_node(et,e,{condition:make_node(Je,e,{left:e.condition,operator:"&&",right:a.condition}),consequent:a.consequent,alternative:s})}if(a.equivalent_to(s)){return make_sequence(e,[e.condition,a]).optimize(t)}if(a instanceof Je&&a.operator=="||"&&a.right.equivalent_to(s)){return make_node(Je,e,{operator:"||",left:make_node(Je,e,{operator:"&&",left:e.condition,right:a.left}),right:s}).optimize(t)}const c=t.in_boolean_context();if(is_true(e.consequent)){if(is_false(e.alternative)){return booleanize(e.condition)}return make_node(Je,e,{operator:"||",left:booleanize(e.condition),right:e.alternative})}if(is_false(e.consequent)){if(is_true(e.alternative)){return booleanize(e.condition.negate(t))}return make_node(Je,e,{operator:"&&",left:booleanize(e.condition.negate(t)),right:e.alternative})}if(is_true(e.alternative)){return make_node(Je,e,{operator:"||",left:booleanize(e.condition.negate(t)),right:e.consequent})}if(is_false(e.alternative)){return make_node(Je,e,{operator:"&&",left:booleanize(e.condition),right:e.consequent})}return e;function booleanize(e){if(e.is_boolean())return e;return make_node(Ze,e,{operator:"!",expression:e.negate(t)})}function is_true(e){return e instanceof tn||c&&e instanceof Kt&&e.getValue()||e instanceof Ze&&e.operator=="!"&&e.expression instanceof Kt&&!e.expression.getValue()}function is_false(e){return e instanceof en||c&&e instanceof Kt&&!e.getValue()||e instanceof Ze&&e.operator=="!"&&e.expression instanceof Kt&&e.expression.getValue()}function single_arg_diff(){var e=a.args;var t=s.args;for(var n=0,i=e.length;n<i;n++){if(e[n]instanceof ae)return;if(!e[n].equivalent_to(t[n])){if(t[n]instanceof ae)return;for(var r=n+1;r<i;r++){if(e[r]instanceof ae)return;if(!e[r].equivalent_to(t[r]))return}return n}}}}));def_optimize(Jt,(function(e,t){if(t.in_boolean_context())return make_node(Ht,e,{value:+e.value});var n=t.parent();if(t.option("booleans_as_integers")){if(n instanceof Je&&(n.operator=="==="||n.operator=="!==")){n.operator=n.operator.replace(/=$/,"")}return make_node(Ht,e,{value:+e.value})}if(t.option("booleans")){if(n instanceof Je&&(n.operator=="=="||n.operator=="!=")){return make_node(Ht,e,{value:+e.value})}return make_node(Ze,e,{operator:"!",expression:make_node(Ht,e,{value:1-e.value})})}return e}));function safe_to_flatten(e,t){if(e instanceof Pt){e=e.fixed_value()}if(!e)return false;if(!(e instanceof se||e instanceof pt))return true;if(!(e instanceof se&&e.contains_this()))return true;return t.parent()instanceof Ge}Xe.DEFMETHOD("flatten_object",(function(e,t){if(!t.option("properties"))return;if(e==="__proto__")return;var n=t.option("unsafe_arrows")&&t.option("ecma")>=2015;var i=this.expression;if(i instanceof rt){var r=i.properties;for(var o=r.length;--o>=0;){var a=r[o];if(""+(a instanceof ft?a.key.name:a.key)==e){const e=r.every((e=>(e instanceof at||n&&e instanceof ft&&!e.is_generator)&&!e.computed_key()));if(!e)return;if(!safe_to_flatten(a.value,t))return;return make_node(Ye,this,{expression:make_node(it,i,{elements:r.map((function(e){var t=e.value;if(t instanceof ue){t=make_node(le,t,t)}var n=e.key;if(n instanceof z&&!(n instanceof Ct)){return make_sequence(e,[n,t])}return t}))}),property:make_node(Ht,this,{value:o})})}}}}));def_optimize(Ye,(function(e,t){var n=e.expression;var i=e.property;if(t.option("properties")){var r=i.evaluate(t);if(r!==i){if(typeof r=="string"){if(r=="undefined"){r=undefined}else{var o=parseFloat(r);if(o.toString()==r){r=o}}}i=e.property=best_of_expression(i,make_node_from_constant(r,i).transform(t));var a=""+r;if(is_basic_identifier_string(a)&&a.length<=i.size()+1){return make_node(We,e,{expression:n,optional:e.optional,property:a,quote:i.quote}).optimize(t)}}}var s;e:if(t.option("arguments")&&n instanceof Pt&&n.name=="arguments"&&n.definition().orig.length==1&&(s=n.scope)instanceof se&&s.uses_arguments&&!(s instanceof ce)&&i instanceof Ht){var u=i.getValue();var l=new Set;var c=s.argnames;for(var f=0;f<c.length;f++){if(!(c[f]instanceof Tt)){break e}var _=c[f].name;if(l.has(_)){break e}l.add(_)}var p=s.argnames[u];if(p&&t.has_directive("use strict")){var d=p.definition();if(!t.option("reduce_vars")||d.assignments||d.orig.length>1){p=null}}else if(!p&&!t.option("keep_fargs")&&u<s.argnames.length+5){while(u>=s.argnames.length){p=s.create_symbol(Tt,{source:s,scope:s,tentative_name:"argument_"+s.argnames.length});s.argnames.push(p)}}if(p){var m=make_node(Pt,e,p);m.reference({});clear_flag(p,vn);return m}}if(is_lhs(e,t.parent()))return e;if(r!==i){var h=e.flatten_object(a,t);if(h){n=e.expression=h.expression;i=e.property=h.property}}if(t.option("properties")&&t.option("side_effects")&&i instanceof Ht&&n instanceof it){var u=i.getValue();var E=n.elements;var g=E[u];e:if(safe_to_flatten(g,t)){var v=true;var D=[];for(var b=E.length;--b>u;){var o=E[b].drop_side_effect_free(t);if(o){D.unshift(o);if(v&&o.has_side_effects(t))v=false}}if(g instanceof ae)break e;g=g instanceof Zt?make_node($t,g):g;if(!v)D.unshift(g);while(--b>=0){var o=E[b];if(o instanceof ae)break e;o=o.drop_side_effect_free(t);if(o)D.unshift(o);else u--}if(v){D.push(g);return make_sequence(e,D).optimize(t)}else return make_node(Ye,e,{expression:make_node(it,n,{elements:D}),property:make_node(Ht,i,{value:u})})}}var y=e.evaluate(t);if(y!==e){y=make_node_from_constant(y,e).optimize(t);return best_of(t,y,e)}return e}));def_optimize(je,(function(e,t){if(is_nullish(e.expression,t)){let n=t.parent();if(n instanceof Ze&&n.operator==="delete"){return make_node_from_constant(0,e)}return make_node($t,e)}return e}));se.DEFMETHOD("contains_this",(function(){return walk(this,(e=>{if(e instanceof zt)return nn;if(e!==this&&e instanceof re&&!(e instanceof ce)){return true}}))}));def_optimize(We,(function(e,t){const n=t.parent();if(is_lhs(e,n))return e;if(t.option("unsafe_proto")&&e.expression instanceof We&&e.expression.property=="prototype"){var i=e.expression.expression;if(is_undeclared_ref(i))switch(i.name){case"Array":e.expression=make_node(it,e.expression,{elements:[]});break;case"Function":e.expression=make_node(le,e.expression,{argnames:[],body:[]});break;case"Number":e.expression=make_node(Ht,e.expression,{value:0});break;case"Object":e.expression=make_node(rt,e.expression,{properties:[]});break;case"RegExp":e.expression=make_node(Wt,e.expression,{value:{source:"t",flags:""}});break;case"String":e.expression=make_node(Gt,e.expression,{value:""});break}}if(!(n instanceof Ke)||!has_annotation(n,an)){const n=e.flatten_object(e.property,t);if(n)return n.optimize(t)}let r=e.evaluate(t);if(r!==e){r=make_node_from_constant(r,e).optimize(t);return best_of(t,r,e)}return e}));function literals_in_boolean_context(e,t){if(t.in_boolean_context()){return best_of(t,e,make_sequence(e,[e,make_node(tn,e)]).optimize(t))}return e}function inline_array_like_spread(e){for(var t=0;t<e.length;t++){var n=e[t];if(n instanceof ae){var i=n.expression;if(i instanceof it&&!i.elements.some((e=>e instanceof Zt))){e.splice(t,1,...i.elements);t--}}}}def_optimize(it,(function(e,t){var n=literals_in_boolean_context(e,t);if(n!==e){return n}inline_array_like_spread(e.elements);return e}));function inline_object_prop_spread(e,t){for(var n=0;n<e.length;n++){var i=e[n];if(i instanceof ae){const r=i.expression;if(r instanceof rt&&r.properties.every((e=>e instanceof at))){e.splice(n,1,...r.properties);n--}else if(r instanceof Kt&&!(r instanceof Gt)){e.splice(n,1);n--}else if(is_nullish(r,t)){e.splice(n,1);n--}}}}def_optimize(rt,(function(e,t){var n=literals_in_boolean_context(e,t);if(n!==e){return n}inline_object_prop_spread(e.properties,t);return e}));def_optimize(Wt,literals_in_boolean_context);def_optimize(ge,(function(e,t){if(e.value&&is_undefined(e.value,t)){e.value=null}return e}));def_optimize(ce,opt_AST_Lambda);def_optimize(le,(function(e,t){e=opt_AST_Lambda(e,t);if(t.option("unsafe_arrows")&&t.option("ecma")>=2015&&!e.name&&!e.is_generator&&!e.uses_arguments&&!e.pinned()){const n=walk(e,(e=>{if(e instanceof zt)return nn}));if(!n)return make_node(ce,e,e).optimize(t)}return e}));def_optimize(pt,(function(e){return e}));def_optimize(Ae,(function(e,t){if(e.expression&&!e.is_star&&is_undefined(e.expression,t)){e.expression=null}return e}));def_optimize(de,(function(e,t){if(!t.option("evaluate")||t.parent()instanceof pe){return e}var n=[];for(var i=0;i<e.segments.length;i++){var r=e.segments[i];if(r instanceof z){var o=r.evaluate(t);if(o!==r&&(o+"").length<=r.size()+"${}".length){n[n.length-1].value=n[n.length-1].value+o+e.segments[++i].value;continue}if(r instanceof de){var a=r.segments;n[n.length-1].value+=a[0].value;for(var s=1;s<a.length;s++){r=a[s];n.push(r)}continue}}n.push(r)}e.segments=n;if(n.length==1){return make_node(Gt,e,n[0])}if(n.length===3&&n[1]instanceof z&&(n[1].is_string(t)||n[1].is_number(t)||is_nullish(n[1],t)||t.option("unsafe"))){if(n[2].value===""){return make_node(Je,e,{operator:"+",left:make_node(Gt,e,{value:n[0].value}),right:n[1]})}if(n[0].value===""){return make_node(Je,e,{operator:"+",left:n[1],right:make_node(Gt,e,{value:n[2].value})})}}return e}));def_optimize(pe,(function(e){return e}));function lift_key(e,t){if(!t.option("computed_props"))return e;if(!(e.key instanceof Kt))return e;if(e.key instanceof Gt||e.key instanceof Ht){if(e.key.value==="__proto__")return e;if(e.key.value=="constructor"&&t.parent()instanceof pt)return e;if(e instanceof at){e.quote=e.key.quote;e.key=e.key.value}else if(e instanceof dt){e.quote=e.key.quote;e.key=make_node(Rt,e.key,{name:e.key.value})}else{e.quote=e.key.quote;e.key=make_node(Ct,e.key,{name:e.key.value})}}return e}def_optimize(ot,lift_key);def_optimize(ft,(function(e,t){lift_key(e,t);if(t.option("arrows")&&t.parent()instanceof rt&&!e.is_generator&&!e.value.uses_arguments&&!e.value.pinned()&&e.value.body.length==1&&e.value.body[0]instanceof ge&&e.value.body[0].value&&!e.value.contains_this()){var n=make_node(ce,e.value,e.value);n.async=e.async;n.is_generator=e.is_generator;return make_node(at,e,{key:e.key instanceof Ct?e.key.name:e.key,value:n,quote:e.quote})}return e}));def_optimize(at,(function(e,t){lift_key(e,t);var n=t.option("unsafe_methods");if(n&&t.option("ecma")>=2015&&(!(n instanceof RegExp)||n.test(e.key+""))){var i=e.key;var r=e.value;var o=r instanceof ce&&Array.isArray(r.body)&&!r.contains_this();if((o||r instanceof le)&&!r.name){return make_node(ft,e,{async:r.async,is_generator:r.is_generator,key:i instanceof z?i:make_node(Ct,e,{name:i}),value:make_node(ue,r,r),quote:e.quote})}}return e}));def_optimize(_e,(function(e,t){if(t.option("pure_getters")==true&&t.option("unused")&&!e.is_array&&Array.isArray(e.names)&&!is_destructuring_export_decl(t)&&!(e.names[e.names.length-1]instanceof ae)){var n=[];for(var i=0;i<e.names.length;i++){var r=e.names[i];if(!(r instanceof at&&typeof r.key=="string"&&r.value instanceof Dt&&!should_retain(t,r.value.definition()))){n.push(r)}}if(n.length!=e.names.length){e.names=n}}return e;function is_destructuring_export_decl(e){var t=[/^VarDef$/,/^(Const|Let|Var)$/,/^Export$/];for(var n=0,i=0,r=t.length;n<r;i++){var o=e.parent(i);if(!o)return false;if(n===0&&o.TYPE=="Destructuring")continue;if(!t[n].test(o.TYPE)){return false}n++}return true}function should_retain(e,t){if(t.references.length)return true;if(!t.global)return false;if(e.toplevel.vars){if(e.top_retain){return e.top_retain(t)}return false}return true}}));async function SourceMap(e){e=defaults(e,{file:null,root:null,orig:null,files:{}});var n;var i=new t.SourceMapGenerator({file:e.file,sourceRoot:e.root});let r={__proto__:null};let o=e.files;for(var a in o)if(HOP(o,a)){r[a]=o[a]}if(e.orig){n=await new t.SourceMapConsumer(e.orig);if(n.sourcesContent){n.sources.forEach((function(e,t){var i=n.sourcesContent[t];if(i){r[e]=i}}))}}function add(e,t,o,a,s,u){let l={line:t,column:o};if(n){var c=n.originalPositionFor({line:a,column:s});if(c.source===null){i.addMapping({generated:l,original:null,source:null,name:null});return}e=c.source;a=c.line;s=c.column;u=c.name||u}i.addMapping({generated:l,original:{line:a,column:s},source:e,name:u});i.setSourceContent(e,r[e])}function clean(e){const t=e.sourcesContent&&e.sourcesContent.every((e=>e==null));if(t)delete e.sourcesContent;if(e.file===undefined)delete e.file;if(e.sourceRoot===undefined)delete e.sourceRoot;return e}function getDecoded(){if(!i.toDecodedMap)return null;return clean(i.toDecodedMap())}function getEncoded(){return clean(i.toJSON())}function destroy(){if(n&&n.destroy)n.destroy()}return{add:add,getDecoded:getDecoded,getEncoded:getEncoded,destroy:destroy}}var Qn=["$&","$'","$*","$+","$1","$2","$3","$4","$5","$6","$7","$8","$9","$_","$`","$input","-moz-animation","-moz-animation-delay","-moz-animation-direction","-moz-animation-duration","-moz-animation-fill-mode","-moz-animation-iteration-count","-moz-animation-name","-moz-animation-play-state","-moz-animation-timing-function","-moz-appearance","-moz-backface-visibility","-moz-border-end","-moz-border-end-color","-moz-border-end-style","-moz-border-end-width","-moz-border-image","-moz-border-start","-moz-border-start-color","-moz-border-start-style","-moz-border-start-width","-moz-box-align","-moz-box-direction","-moz-box-flex","-moz-box-ordinal-group","-moz-box-orient","-moz-box-pack","-moz-box-sizing","-moz-float-edge","-moz-font-feature-settings","-moz-font-language-override","-moz-force-broken-image-icon","-moz-hyphens","-moz-image-region","-moz-margin-end","-moz-margin-start","-moz-orient","-moz-osx-font-smoothing","-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-moz-padding-end","-moz-padding-start","-moz-perspective","-moz-perspective-origin","-moz-tab-size","-moz-text-size-adjust","-moz-transform","-moz-transform-origin","-moz-transform-style","-moz-transition","-moz-transition-delay","-moz-transition-duration","-moz-transition-property","-moz-transition-timing-function","-moz-user-focus","-moz-user-input","-moz-user-modify","-moz-user-select","-moz-window-dragging","-webkit-align-content","-webkit-align-items","-webkit-align-self","-webkit-animation","-webkit-animation-delay","-webkit-animation-direction","-webkit-animation-duration","-webkit-animation-fill-mode","-webkit-animation-iteration-count","-webkit-animation-name","-webkit-animation-play-state","-webkit-animation-timing-function","-webkit-appearance","-webkit-backface-visibility","-webkit-background-clip","-webkit-background-origin","-webkit-background-size","-webkit-border-bottom-left-radius","-webkit-border-bottom-right-radius","-webkit-border-image","-webkit-border-radius","-webkit-border-top-left-radius","-webkit-border-top-right-radius","-webkit-box-align","-webkit-box-direction","-webkit-box-flex","-webkit-box-ordinal-group","-webkit-box-orient","-webkit-box-pack","-webkit-box-shadow","-webkit-box-sizing","-webkit-filter","-webkit-flex","-webkit-flex-basis","-webkit-flex-direction","-webkit-flex-flow","-webkit-flex-grow","-webkit-flex-shrink","-webkit-flex-wrap","-webkit-justify-content","-webkit-line-clamp","-webkit-mask","-webkit-mask-clip","-webkit-mask-composite","-webkit-mask-image","-webkit-mask-origin","-webkit-mask-position","-webkit-mask-position-x","-webkit-mask-position-y","-webkit-mask-repeat","-webkit-mask-size","-webkit-order","-webkit-perspective","-webkit-perspective-origin","-webkit-text-fill-color","-webkit-text-size-adjust","-webkit-text-stroke","-webkit-text-stroke-color","-webkit-text-stroke-width","-webkit-transform","-webkit-transform-origin","-webkit-transform-style","-webkit-transition","-webkit-transition-delay","-webkit-transition-duration","-webkit-transition-property","-webkit-transition-timing-function","-webkit-user-select","0","1","10","11","12","13","14","15","16","17","18","19","2","20","3","4","5","6","7","8","9","@@iterator","ABORT_ERR","ACTIVE","ACTIVE_ATTRIBUTES","ACTIVE_TEXTURE","ACTIVE_UNIFORMS","ACTIVE_UNIFORM_BLOCKS","ADDITION","ALIASED_LINE_WIDTH_RANGE","ALIASED_POINT_SIZE_RANGE","ALLOW_KEYBOARD_INPUT","ALLPASS","ALPHA","ALPHA_BITS","ALREADY_SIGNALED","ALT_MASK","ALWAYS","ANY_SAMPLES_PASSED","ANY_SAMPLES_PASSED_CONSERVATIVE","ANY_TYPE","ANY_UNORDERED_NODE_TYPE","ARRAY_BUFFER","ARRAY_BUFFER_BINDING","ATTACHED_SHADERS","ATTRIBUTE_NODE","AT_TARGET","AbortController","AbortSignal","AbsoluteOrientationSensor","AbstractRange","Accelerometer","AddSearchProvider","AggregateError","AnalyserNode","Animation","AnimationEffect","AnimationEvent","AnimationPlaybackEvent","AnimationTimeline","AnonXMLHttpRequest","Any","ApplicationCache","ApplicationCacheErrorEvent","Array","ArrayBuffer","ArrayType","Atomics","Attr","Audio","AudioBuffer","AudioBufferSourceNode","AudioContext","AudioDestinationNode","AudioListener","AudioNode","AudioParam","AudioParamMap","AudioProcessingEvent","AudioScheduledSourceNode","AudioStreamTrack","AudioWorklet","AudioWorkletNode","AuthenticatorAssertionResponse","AuthenticatorAttestationResponse","AuthenticatorResponse","AutocompleteErrorEvent","BACK","BAD_BOUNDARYPOINTS_ERR","BAD_REQUEST","BANDPASS","BLEND","BLEND_COLOR","BLEND_DST_ALPHA","BLEND_DST_RGB","BLEND_EQUATION","BLEND_EQUATION_ALPHA","BLEND_EQUATION_RGB","BLEND_SRC_ALPHA","BLEND_SRC_RGB","BLUE_BITS","BLUR","BOOL","BOOLEAN_TYPE","BOOL_VEC2","BOOL_VEC3","BOOL_VEC4","BOTH","BROWSER_DEFAULT_WEBGL","BUBBLING_PHASE","BUFFER_SIZE","BUFFER_USAGE","BYTE","BYTES_PER_ELEMENT","BackgroundFetchManager","BackgroundFetchRecord","BackgroundFetchRegistration","BarProp","BarcodeDetector","BaseAudioContext","BaseHref","BatteryManager","BeforeInstallPromptEvent","BeforeLoadEvent","BeforeUnloadEvent","BigInt","BigInt64Array","BigUint64Array","BiquadFilterNode","Blob","BlobEvent","Bluetooth","BluetoothCharacteristicProperties","BluetoothDevice","BluetoothRemoteGATTCharacteristic","BluetoothRemoteGATTDescriptor","BluetoothRemoteGATTServer","BluetoothRemoteGATTService","BluetoothUUID","Boolean","BroadcastChannel","ByteLengthQueuingStrategy","CAPTURING_PHASE","CCW","CDATASection","CDATA_SECTION_NODE","CHANGE","CHARSET_RULE","CHECKING","CLAMP_TO_EDGE","CLICK","CLOSED","CLOSING","COLOR","COLOR_ATTACHMENT0","COLOR_ATTACHMENT1","COLOR_ATTACHMENT10","COLOR_ATTACHMENT11","COLOR_ATTACHMENT12","COLOR_ATTACHMENT13","COLOR_ATTACHMENT14","COLOR_ATTACHMENT15","COLOR_ATTACHMENT2","COLOR_ATTACHMENT3","COLOR_ATTACHMENT4","COLOR_ATTACHMENT5","COLOR_ATTACHMENT6","COLOR_ATTACHMENT7","COLOR_ATTACHMENT8","COLOR_ATTACHMENT9","COLOR_BUFFER_BIT","COLOR_CLEAR_VALUE","COLOR_WRITEMASK","COMMENT_NODE","COMPARE_REF_TO_TEXTURE","COMPILE_STATUS","COMPLETION_STATUS_KHR","COMPRESSED_RGBA_S3TC_DXT1_EXT","COMPRESSED_RGBA_S3TC_DXT3_EXT","COMPRESSED_RGBA_S3TC_DXT5_EXT","COMPRESSED_RGB_S3TC_DXT1_EXT","COMPRESSED_TEXTURE_FORMATS","CONDITION_SATISFIED","CONFIGURATION_UNSUPPORTED","CONNECTING","CONSTANT_ALPHA","CONSTANT_COLOR","CONSTRAINT_ERR","CONTEXT_LOST_WEBGL","CONTROL_MASK","COPY_READ_BUFFER","COPY_READ_BUFFER_BINDING","COPY_WRITE_BUFFER","COPY_WRITE_BUFFER_BINDING","COUNTER_STYLE_RULE","CSS","CSS2Properties","CSSAnimation","CSSCharsetRule","CSSConditionRule","CSSCounterStyleRule","CSSFontFaceRule","CSSFontFeatureValuesRule","CSSGroupingRule","CSSImageValue","CSSImportRule","CSSKeyframeRule","CSSKeyframesRule","CSSKeywordValue","CSSMathInvert","CSSMathMax","CSSMathMin","CSSMathNegate","CSSMathProduct","CSSMathSum","CSSMathValue","CSSMatrixComponent","CSSMediaRule","CSSMozDocumentRule","CSSNameSpaceRule","CSSNamespaceRule","CSSNumericArray","CSSNumericValue","CSSPageRule","CSSPerspective","CSSPositionValue","CSSPrimitiveValue","CSSRotate","CSSRule","CSSRuleList","CSSScale","CSSSkew","CSSSkewX","CSSSkewY","CSSStyleDeclaration","CSSStyleRule","CSSStyleSheet","CSSStyleValue","CSSSupportsRule","CSSTransformComponent","CSSTransformValue","CSSTransition","CSSTranslate","CSSUnitValue","CSSUnknownRule","CSSUnparsedValue","CSSValue","CSSValueList","CSSVariableReferenceValue","CSSVariablesDeclaration","CSSVariablesRule","CSSViewportRule","CSS_ATTR","CSS_CM","CSS_COUNTER","CSS_CUSTOM","CSS_DEG","CSS_DIMENSION","CSS_EMS","CSS_EXS","CSS_FILTER_BLUR","CSS_FILTER_BRIGHTNESS","CSS_FILTER_CONTRAST","CSS_FILTER_CUSTOM","CSS_FILTER_DROP_SHADOW","CSS_FILTER_GRAYSCALE","CSS_FILTER_HUE_ROTATE","CSS_FILTER_INVERT","CSS_FILTER_OPACITY","CSS_FILTER_REFERENCE","CSS_FILTER_SATURATE","CSS_FILTER_SEPIA","CSS_GRAD","CSS_HZ","CSS_IDENT","CSS_IN","CSS_INHERIT","CSS_KHZ","CSS_MATRIX","CSS_MATRIX3D","CSS_MM","CSS_MS","CSS_NUMBER","CSS_PC","CSS_PERCENTAGE","CSS_PERSPECTIVE","CSS_PRIMITIVE_VALUE","CSS_PT","CSS_PX","CSS_RAD","CSS_RECT","CSS_RGBCOLOR","CSS_ROTATE","CSS_ROTATE3D","CSS_ROTATEX","CSS_ROTATEY","CSS_ROTATEZ","CSS_S","CSS_SCALE","CSS_SCALE3D","CSS_SCALEX","CSS_SCALEY","CSS_SCALEZ","CSS_SKEW","CSS_SKEWX","CSS_SKEWY","CSS_STRING","CSS_TRANSLATE","CSS_TRANSLATE3D","CSS_TRANSLATEX","CSS_TRANSLATEY","CSS_TRANSLATEZ","CSS_UNKNOWN","CSS_URI","CSS_VALUE_LIST","CSS_VH","CSS_VMAX","CSS_VMIN","CSS_VW","CULL_FACE","CULL_FACE_MODE","CURRENT_PROGRAM","CURRENT_QUERY","CURRENT_VERTEX_ATTRIB","CUSTOM","CW","Cache","CacheStorage","CanvasCaptureMediaStream","CanvasCaptureMediaStreamTrack","CanvasGradient","CanvasPattern","CanvasRenderingContext2D","CaretPosition","ChannelMergerNode","ChannelSplitterNode","CharacterData","ClientRect","ClientRectList","Clipboard","ClipboardEvent","ClipboardItem","CloseEvent","Collator","CommandEvent","Comment","CompileError","CompositionEvent","CompressionStream","Console","ConstantSourceNode","Controllers","ConvolverNode","CountQueuingStrategy","Counter","Credential","CredentialsContainer","Crypto","CryptoKey","CustomElementRegistry","CustomEvent","DATABASE_ERR","DATA_CLONE_ERR","DATA_ERR","DBLCLICK","DECR","DECR_WRAP","DELETE_STATUS","DEPTH","DEPTH24_STENCIL8","DEPTH32F_STENCIL8","DEPTH_ATTACHMENT","DEPTH_BITS","DEPTH_BUFFER_BIT","DEPTH_CLEAR_VALUE","DEPTH_COMPONENT","DEPTH_COMPONENT16","DEPTH_COMPONENT24","DEPTH_COMPONENT32F","DEPTH_FUNC","DEPTH_RANGE","DEPTH_STENCIL","DEPTH_STENCIL_ATTACHMENT","DEPTH_TEST","DEPTH_WRITEMASK","DEVICE_INELIGIBLE","DIRECTION_DOWN","DIRECTION_LEFT","DIRECTION_RIGHT","DIRECTION_UP","DISABLED","DISPATCH_REQUEST_ERR","DITHER","DOCUMENT_FRAGMENT_NODE","DOCUMENT_NODE","DOCUMENT_POSITION_CONTAINED_BY","DOCUMENT_POSITION_CONTAINS","DOCUMENT_POSITION_DISCONNECTED","DOCUMENT_POSITION_FOLLOWING","DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC","DOCUMENT_POSITION_PRECEDING","DOCUMENT_TYPE_NODE","DOMCursor","DOMError","DOMException","DOMImplementation","DOMImplementationLS","DOMMatrix","DOMMatrixReadOnly","DOMParser","DOMPoint","DOMPointReadOnly","DOMQuad","DOMRect","DOMRectList","DOMRectReadOnly","DOMRequest","DOMSTRING_SIZE_ERR","DOMSettableTokenList","DOMStringList","DOMStringMap","DOMTokenList","DOMTransactionEvent","DOM_DELTA_LINE","DOM_DELTA_PAGE","DOM_DELTA_PIXEL","DOM_INPUT_METHOD_DROP","DOM_INPUT_METHOD_HANDWRITING","DOM_INPUT_METHOD_IME","DOM_INPUT_METHOD_KEYBOARD","DOM_INPUT_METHOD_MULTIMODAL","DOM_INPUT_METHOD_OPTION","DOM_INPUT_METHOD_PASTE","DOM_INPUT_METHOD_SCRIPT","DOM_INPUT_METHOD_UNKNOWN","DOM_INPUT_METHOD_VOICE","DOM_KEY_LOCATION_JOYSTICK","DOM_KEY_LOCATION_LEFT","DOM_KEY_LOCATION_MOBILE","DOM_KEY_LOCATION_NUMPAD","DOM_KEY_LOCATION_RIGHT","DOM_KEY_LOCATION_STANDARD","DOM_VK_0","DOM_VK_1","DOM_VK_2","DOM_VK_3","DOM_VK_4","DOM_VK_5","DOM_VK_6","DOM_VK_7","DOM_VK_8","DOM_VK_9","DOM_VK_A","DOM_VK_ACCEPT","DOM_VK_ADD","DOM_VK_ALT","DOM_VK_ALTGR","DOM_VK_AMPERSAND","DOM_VK_ASTERISK","DOM_VK_AT","DOM_VK_ATTN","DOM_VK_B","DOM_VK_BACKSPACE","DOM_VK_BACK_QUOTE","DOM_VK_BACK_SLASH","DOM_VK_BACK_SPACE","DOM_VK_C","DOM_VK_CANCEL","DOM_VK_CAPS_LOCK","DOM_VK_CIRCUMFLEX","DOM_VK_CLEAR","DOM_VK_CLOSE_BRACKET","DOM_VK_CLOSE_CURLY_BRACKET","DOM_VK_CLOSE_PAREN","DOM_VK_COLON","DOM_VK_COMMA","DOM_VK_CONTEXT_MENU","DOM_VK_CONTROL","DOM_VK_CONVERT","DOM_VK_CRSEL","DOM_VK_CTRL","DOM_VK_D","DOM_VK_DECIMAL","DOM_VK_DELETE","DOM_VK_DIVIDE","DOM_VK_DOLLAR","DOM_VK_DOUBLE_QUOTE","DOM_VK_DOWN","DOM_VK_E","DOM_VK_EISU","DOM_VK_END","DOM_VK_ENTER","DOM_VK_EQUALS","DOM_VK_EREOF","DOM_VK_ESCAPE","DOM_VK_EXCLAMATION","DOM_VK_EXECUTE","DOM_VK_EXSEL","DOM_VK_F","DOM_VK_F1","DOM_VK_F10","DOM_VK_F11","DOM_VK_F12","DOM_VK_F13","DOM_VK_F14","DOM_VK_F15","DOM_VK_F16","DOM_VK_F17","DOM_VK_F18","DOM_VK_F19","DOM_VK_F2","DOM_VK_F20","DOM_VK_F21","DOM_VK_F22","DOM_VK_F23","DOM_VK_F24","DOM_VK_F25","DOM_VK_F26","DOM_VK_F27","DOM_VK_F28","DOM_VK_F29","DOM_VK_F3","DOM_VK_F30","DOM_VK_F31","DOM_VK_F32","DOM_VK_F33","DOM_VK_F34","DOM_VK_F35","DOM_VK_F36","DOM_VK_F4","DOM_VK_F5","DOM_VK_F6","DOM_VK_F7","DOM_VK_F8","DOM_VK_F9","DOM_VK_FINAL","DOM_VK_FRONT","DOM_VK_G","DOM_VK_GREATER_THAN","DOM_VK_H","DOM_VK_HANGUL","DOM_VK_HANJA","DOM_VK_HASH","DOM_VK_HELP","DOM_VK_HK_TOGGLE","DOM_VK_HOME","DOM_VK_HYPHEN_MINUS","DOM_VK_I","DOM_VK_INSERT","DOM_VK_J","DOM_VK_JUNJA","DOM_VK_K","DOM_VK_KANA","DOM_VK_KANJI","DOM_VK_L","DOM_VK_LEFT","DOM_VK_LEFT_TAB","DOM_VK_LESS_THAN","DOM_VK_M","DOM_VK_META","DOM_VK_MODECHANGE","DOM_VK_MULTIPLY","DOM_VK_N","DOM_VK_NONCONVERT","DOM_VK_NUMPAD0","DOM_VK_NUMPAD1","DOM_VK_NUMPAD2","DOM_VK_NUMPAD3","DOM_VK_NUMPAD4","DOM_VK_NUMPAD5","DOM_VK_NUMPAD6","DOM_VK_NUMPAD7","DOM_VK_NUMPAD8","DOM_VK_NUMPAD9","DOM_VK_NUM_LOCK","DOM_VK_O","DOM_VK_OEM_1","DOM_VK_OEM_102","DOM_VK_OEM_2","DOM_VK_OEM_3","DOM_VK_OEM_4","DOM_VK_OEM_5","DOM_VK_OEM_6","DOM_VK_OEM_7","DOM_VK_OEM_8","DOM_VK_OEM_COMMA","DOM_VK_OEM_MINUS","DOM_VK_OEM_PERIOD","DOM_VK_OEM_PLUS","DOM_VK_OPEN_BRACKET","DOM_VK_OPEN_CURLY_BRACKET","DOM_VK_OPEN_PAREN","DOM_VK_P","DOM_VK_PA1","DOM_VK_PAGEDOWN","DOM_VK_PAGEUP","DOM_VK_PAGE_DOWN","DOM_VK_PAGE_UP","DOM_VK_PAUSE","DOM_VK_PERCENT","DOM_VK_PERIOD","DOM_VK_PIPE","DOM_VK_PLAY","DOM_VK_PLUS","DOM_VK_PRINT","DOM_VK_PRINTSCREEN","DOM_VK_PROCESSKEY","DOM_VK_PROPERITES","DOM_VK_Q","DOM_VK_QUESTION_MARK","DOM_VK_QUOTE","DOM_VK_R","DOM_VK_REDO","DOM_VK_RETURN","DOM_VK_RIGHT","DOM_VK_S","DOM_VK_SCROLL_LOCK","DOM_VK_SELECT","DOM_VK_SEMICOLON","DOM_VK_SEPARATOR","DOM_VK_SHIFT","DOM_VK_SLASH","DOM_VK_SLEEP","DOM_VK_SPACE","DOM_VK_SUBTRACT","DOM_VK_T","DOM_VK_TAB","DOM_VK_TILDE","DOM_VK_U","DOM_VK_UNDERSCORE","DOM_VK_UNDO","DOM_VK_UNICODE","DOM_VK_UP","DOM_VK_V","DOM_VK_VOLUME_DOWN","DOM_VK_VOLUME_MUTE","DOM_VK_VOLUME_UP","DOM_VK_W","DOM_VK_WIN","DOM_VK_WINDOW","DOM_VK_WIN_ICO_00","DOM_VK_WIN_ICO_CLEAR","DOM_VK_WIN_ICO_HELP","DOM_VK_WIN_OEM_ATTN","DOM_VK_WIN_OEM_AUTO","DOM_VK_WIN_OEM_BACKTAB","DOM_VK_WIN_OEM_CLEAR","DOM_VK_WIN_OEM_COPY","DOM_VK_WIN_OEM_CUSEL","DOM_VK_WIN_OEM_ENLW","DOM_VK_WIN_OEM_FINISH","DOM_VK_WIN_OEM_FJ_JISHO","DOM_VK_WIN_OEM_FJ_LOYA","DOM_VK_WIN_OEM_FJ_MASSHOU","DOM_VK_WIN_OEM_FJ_ROYA","DOM_VK_WIN_OEM_FJ_TOUROKU","DOM_VK_WIN_OEM_JUMP","DOM_VK_WIN_OEM_PA1","DOM_VK_WIN_OEM_PA2","DOM_VK_WIN_OEM_PA3","DOM_VK_WIN_OEM_RESET","DOM_VK_WIN_OEM_WSCTRL","DOM_VK_X","DOM_VK_XF86XK_ADD_FAVORITE","DOM_VK_XF86XK_APPLICATION_LEFT","DOM_VK_XF86XK_APPLICATION_RIGHT","DOM_VK_XF86XK_AUDIO_CYCLE_TRACK","DOM_VK_XF86XK_AUDIO_FORWARD","DOM_VK_XF86XK_AUDIO_LOWER_VOLUME","DOM_VK_XF86XK_AUDIO_MEDIA","DOM_VK_XF86XK_AUDIO_MUTE","DOM_VK_XF86XK_AUDIO_NEXT","DOM_VK_XF86XK_AUDIO_PAUSE","DOM_VK_XF86XK_AUDIO_PLAY","DOM_VK_XF86XK_AUDIO_PREV","DOM_VK_XF86XK_AUDIO_RAISE_VOLUME","DOM_VK_XF86XK_AUDIO_RANDOM_PLAY","DOM_VK_XF86XK_AUDIO_RECORD","DOM_VK_XF86XK_AUDIO_REPEAT","DOM_VK_XF86XK_AUDIO_REWIND","DOM_VK_XF86XK_AUDIO_STOP","DOM_VK_XF86XK_AWAY","DOM_VK_XF86XK_BACK","DOM_VK_XF86XK_BACK_FORWARD","DOM_VK_XF86XK_BATTERY","DOM_VK_XF86XK_BLUE","DOM_VK_XF86XK_BLUETOOTH","DOM_VK_XF86XK_BOOK","DOM_VK_XF86XK_BRIGHTNESS_ADJUST","DOM_VK_XF86XK_CALCULATOR","DOM_VK_XF86XK_CALENDAR","DOM_VK_XF86XK_CD","DOM_VK_XF86XK_CLOSE","DOM_VK_XF86XK_COMMUNITY","DOM_VK_XF86XK_CONTRAST_ADJUST","DOM_VK_XF86XK_COPY","DOM_VK_XF86XK_CUT","DOM_VK_XF86XK_CYCLE_ANGLE","DOM_VK_XF86XK_DISPLAY","DOM_VK_XF86XK_DOCUMENTS","DOM_VK_XF86XK_DOS","DOM_VK_XF86XK_EJECT","DOM_VK_XF86XK_EXCEL","DOM_VK_XF86XK_EXPLORER","DOM_VK_XF86XK_FAVORITES","DOM_VK_XF86XK_FINANCE","DOM_VK_XF86XK_FORWARD","DOM_VK_XF86XK_FRAME_BACK","DOM_VK_XF86XK_FRAME_FORWARD","DOM_VK_XF86XK_GAME","DOM_VK_XF86XK_GO","DOM_VK_XF86XK_GREEN","DOM_VK_XF86XK_HIBERNATE","DOM_VK_XF86XK_HISTORY","DOM_VK_XF86XK_HOME_PAGE","DOM_VK_XF86XK_HOT_LINKS","DOM_VK_XF86XK_I_TOUCH","DOM_VK_XF86XK_KBD_BRIGHTNESS_DOWN","DOM_VK_XF86XK_KBD_BRIGHTNESS_UP","DOM_VK_XF86XK_KBD_LIGHT_ON_OFF","DOM_VK_XF86XK_LAUNCH0","DOM_VK_XF86XK_LAUNCH1","DOM_VK_XF86XK_LAUNCH2","DOM_VK_XF86XK_LAUNCH3","DOM_VK_XF86XK_LAUNCH4","DOM_VK_XF86XK_LAUNCH5","DOM_VK_XF86XK_LAUNCH6","DOM_VK_XF86XK_LAUNCH7","DOM_VK_XF86XK_LAUNCH8","DOM_VK_XF86XK_LAUNCH9","DOM_VK_XF86XK_LAUNCH_A","DOM_VK_XF86XK_LAUNCH_B","DOM_VK_XF86XK_LAUNCH_C","DOM_VK_XF86XK_LAUNCH_D","DOM_VK_XF86XK_LAUNCH_E","DOM_VK_XF86XK_LAUNCH_F","DOM_VK_XF86XK_LIGHT_BULB","DOM_VK_XF86XK_LOG_OFF","DOM_VK_XF86XK_MAIL","DOM_VK_XF86XK_MAIL_FORWARD","DOM_VK_XF86XK_MARKET","DOM_VK_XF86XK_MEETING","DOM_VK_XF86XK_MEMO","DOM_VK_XF86XK_MENU_KB","DOM_VK_XF86XK_MENU_PB","DOM_VK_XF86XK_MESSENGER","DOM_VK_XF86XK_MON_BRIGHTNESS_DOWN","DOM_VK_XF86XK_MON_BRIGHTNESS_UP","DOM_VK_XF86XK_MUSIC","DOM_VK_XF86XK_MY_COMPUTER","DOM_VK_XF86XK_MY_SITES","DOM_VK_XF86XK_NEW","DOM_VK_XF86XK_NEWS","DOM_VK_XF86XK_OFFICE_HOME","DOM_VK_XF86XK_OPEN","DOM_VK_XF86XK_OPEN_URL","DOM_VK_XF86XK_OPTION","DOM_VK_XF86XK_PASTE","DOM_VK_XF86XK_PHONE","DOM_VK_XF86XK_PICTURES","DOM_VK_XF86XK_POWER_DOWN","DOM_VK_XF86XK_POWER_OFF","DOM_VK_XF86XK_RED","DOM_VK_XF86XK_REFRESH","DOM_VK_XF86XK_RELOAD","DOM_VK_XF86XK_REPLY","DOM_VK_XF86XK_ROCKER_DOWN","DOM_VK_XF86XK_ROCKER_ENTER","DOM_VK_XF86XK_ROCKER_UP","DOM_VK_XF86XK_ROTATE_WINDOWS","DOM_VK_XF86XK_ROTATION_KB","DOM_VK_XF86XK_ROTATION_PB","DOM_VK_XF86XK_SAVE","DOM_VK_XF86XK_SCREEN_SAVER","DOM_VK_XF86XK_SCROLL_CLICK","DOM_VK_XF86XK_SCROLL_DOWN","DOM_VK_XF86XK_SCROLL_UP","DOM_VK_XF86XK_SEARCH","DOM_VK_XF86XK_SEND","DOM_VK_XF86XK_SHOP","DOM_VK_XF86XK_SPELL","DOM_VK_XF86XK_SPLIT_SCREEN","DOM_VK_XF86XK_STANDBY","DOM_VK_XF86XK_START","DOM_VK_XF86XK_STOP","DOM_VK_XF86XK_SUBTITLE","DOM_VK_XF86XK_SUPPORT","DOM_VK_XF86XK_SUSPEND","DOM_VK_XF86XK_TASK_PANE","DOM_VK_XF86XK_TERMINAL","DOM_VK_XF86XK_TIME","DOM_VK_XF86XK_TOOLS","DOM_VK_XF86XK_TOP_MENU","DOM_VK_XF86XK_TO_DO_LIST","DOM_VK_XF86XK_TRAVEL","DOM_VK_XF86XK_USER1KB","DOM_VK_XF86XK_USER2KB","DOM_VK_XF86XK_USER_PB","DOM_VK_XF86XK_UWB","DOM_VK_XF86XK_VENDOR_HOME","DOM_VK_XF86XK_VIDEO","DOM_VK_XF86XK_VIEW","DOM_VK_XF86XK_WAKE_UP","DOM_VK_XF86XK_WEB_CAM","DOM_VK_XF86XK_WHEEL_BUTTON","DOM_VK_XF86XK_WLAN","DOM_VK_XF86XK_WORD","DOM_VK_XF86XK_WWW","DOM_VK_XF86XK_XFER","DOM_VK_XF86XK_YELLOW","DOM_VK_XF86XK_ZOOM_IN","DOM_VK_XF86XK_ZOOM_OUT","DOM_VK_Y","DOM_VK_Z","DOM_VK_ZOOM","DONE","DONT_CARE","DOWNLOADING","DRAGDROP","DRAW_BUFFER0","DRAW_BUFFER1","DRAW_BUFFER10","DRAW_BUFFER11","DRAW_BUFFER12","DRAW_BUFFER13","DRAW_BUFFER14","DRAW_BUFFER15","DRAW_BUFFER2","DRAW_BUFFER3","DRAW_BUFFER4","DRAW_BUFFER5","DRAW_BUFFER6","DRAW_BUFFER7","DRAW_BUFFER8","DRAW_BUFFER9","DRAW_FRAMEBUFFER","DRAW_FRAMEBUFFER_BINDING","DST_ALPHA","DST_COLOR","DYNAMIC_COPY","DYNAMIC_DRAW","DYNAMIC_READ","DataChannel","DataTransfer","DataTransferItem","DataTransferItemList","DataView","Date","DateTimeFormat","DecompressionStream","DelayNode","DeprecationReportBody","DesktopNotification","DesktopNotificationCenter","DeviceLightEvent","DeviceMotionEvent","DeviceMotionEventAcceleration","DeviceMotionEventRotationRate","DeviceOrientationEvent","DeviceProximityEvent","DeviceStorage","DeviceStorageChangeEvent","Directory","DisplayNames","Document","DocumentFragment","DocumentTimeline","DocumentType","DragEvent","DynamicsCompressorNode","E","ELEMENT_ARRAY_BUFFER","ELEMENT_ARRAY_BUFFER_BINDING","ELEMENT_NODE","EMPTY","ENCODING_ERR","ENDED","END_TO_END","END_TO_START","ENTITY_NODE","ENTITY_REFERENCE_NODE","EPSILON","EQUAL","EQUALPOWER","ERROR","EXPONENTIAL_DISTANCE","Element","ElementInternals","ElementQuery","EnterPictureInPictureEvent","Entity","EntityReference","Error","ErrorEvent","EvalError","Event","EventException","EventSource","EventTarget","External","FASTEST","FIDOSDK","FILTER_ACCEPT","FILTER_INTERRUPT","FILTER_REJECT","FILTER_SKIP","FINISHED_STATE","FIRST_ORDERED_NODE_TYPE","FLOAT","FLOAT_32_UNSIGNED_INT_24_8_REV","FLOAT_MAT2","FLOAT_MAT2x3","FLOAT_MAT2x4","FLOAT_MAT3","FLOAT_MAT3x2","FLOAT_MAT3x4","FLOAT_MAT4","FLOAT_MAT4x2","FLOAT_MAT4x3","FLOAT_VEC2","FLOAT_VEC3","FLOAT_VEC4","FOCUS","FONT_FACE_RULE","FONT_FEATURE_VALUES_RULE","FRAGMENT_SHADER","FRAGMENT_SHADER_DERIVATIVE_HINT","FRAGMENT_SHADER_DERIVATIVE_HINT_OES","FRAMEBUFFER","FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE","FRAMEBUFFER_ATTACHMENT_BLUE_SIZE","FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING","FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE","FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE","FRAMEBUFFER_ATTACHMENT_GREEN_SIZE","FRAMEBUFFER_ATTACHMENT_OBJECT_NAME","FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE","FRAMEBUFFER_ATTACHMENT_RED_SIZE","FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE","FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE","FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER","FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL","FRAMEBUFFER_BINDING","FRAMEBUFFER_COMPLETE","FRAMEBUFFER_DEFAULT","FRAMEBUFFER_INCOMPLETE_ATTACHMENT","FRAMEBUFFER_INCOMPLETE_DIMENSIONS","FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT","FRAMEBUFFER_INCOMPLETE_MULTISAMPLE","FRAMEBUFFER_UNSUPPORTED","FRONT","FRONT_AND_BACK","FRONT_FACE","FUNC_ADD","FUNC_REVERSE_SUBTRACT","FUNC_SUBTRACT","FeaturePolicy","FeaturePolicyViolationReportBody","FederatedCredential","Feed","FeedEntry","File","FileError","FileList","FileReader","FileSystem","FileSystemDirectoryEntry","FileSystemDirectoryReader","FileSystemEntry","FileSystemFileEntry","FinalizationRegistry","FindInPage","Float32Array","Float64Array","FocusEvent","FontFace","FontFaceSet","FontFaceSetLoadEvent","FormData","FormDataEvent","FragmentDirective","Function","GENERATE_MIPMAP_HINT","GEQUAL","GREATER","GREEN_BITS","GainNode","Gamepad","GamepadAxisMoveEvent","GamepadButton","GamepadButtonEvent","GamepadEvent","GamepadHapticActuator","GamepadPose","Geolocation","GeolocationCoordinates","GeolocationPosition","GeolocationPositionError","GestureEvent","Global","Gyroscope","HALF_FLOAT","HAVE_CURRENT_DATA","HAVE_ENOUGH_DATA","HAVE_FUTURE_DATA","HAVE_METADATA","HAVE_NOTHING","HEADERS_RECEIVED","HIDDEN","HIERARCHY_REQUEST_ERR","HIGHPASS","HIGHSHELF","HIGH_FLOAT","HIGH_INT","HORIZONTAL","HORIZONTAL_AXIS","HRTF","HTMLAllCollection","HTMLAnchorElement","HTMLAppletElement","HTMLAreaElement","HTMLAudioElement","HTMLBRElement","HTMLBaseElement","HTMLBaseFontElement","HTMLBlockquoteElement","HTMLBodyElement","HTMLButtonElement","HTMLCanvasElement","HTMLCollection","HTMLCommandElement","HTMLContentElement","HTMLDListElement","HTMLDataElement","HTMLDataListElement","HTMLDetailsElement","HTMLDialogElement","HTMLDirectoryElement","HTMLDivElement","HTMLDocument","HTMLElement","HTMLEmbedElement","HTMLFieldSetElement","HTMLFontElement","HTMLFormControlsCollection","HTMLFormElement","HTMLFrameElement","HTMLFrameSetElement","HTMLHRElement","HTMLHeadElement","HTMLHeadingElement","HTMLHtmlElement","HTMLIFrameElement","HTMLImageElement","HTMLInputElement","HTMLIsIndexElement","HTMLKeygenElement","HTMLLIElement","HTMLLabelElement","HTMLLegendElement","HTMLLinkElement","HTMLMapElement","HTMLMarqueeElement","HTMLMediaElement","HTMLMenuElement","HTMLMenuItemElement","HTMLMetaElement","HTMLMeterElement","HTMLModElement","HTMLOListElement","HTMLObjectElement","HTMLOptGroupElement","HTMLOptionElement","HTMLOptionsCollection","HTMLOutputElement","HTMLParagraphElement","HTMLParamElement","HTMLPictureElement","HTMLPreElement","HTMLProgressElement","HTMLPropertiesCollection","HTMLQuoteElement","HTMLScriptElement","HTMLSelectElement","HTMLShadowElement","HTMLSlotElement","HTMLSourceElement","HTMLSpanElement","HTMLStyleElement","HTMLTableCaptionElement","HTMLTableCellElement","HTMLTableColElement","HTMLTableElement","HTMLTableRowElement","HTMLTableSectionElement","HTMLTemplateElement","HTMLTextAreaElement","HTMLTimeElement","HTMLTitleElement","HTMLTrackElement","HTMLUListElement","HTMLUnknownElement","HTMLVideoElement","HashChangeEvent","Headers","History","Hz","ICE_CHECKING","ICE_CLOSED","ICE_COMPLETED","ICE_CONNECTED","ICE_FAILED","ICE_GATHERING","ICE_WAITING","IDBCursor","IDBCursorWithValue","IDBDatabase","IDBDatabaseException","IDBFactory","IDBFileHandle","IDBFileRequest","IDBIndex","IDBKeyRange","IDBMutableFile","IDBObjectStore","IDBOpenDBRequest","IDBRequest","IDBTransaction","IDBVersionChangeEvent","IDLE","IIRFilterNode","IMPLEMENTATION_COLOR_READ_FORMAT","IMPLEMENTATION_COLOR_READ_TYPE","IMPORT_RULE","INCR","INCR_WRAP","INDEX_SIZE_ERR","INT","INTERLEAVED_ATTRIBS","INT_2_10_10_10_REV","INT_SAMPLER_2D","INT_SAMPLER_2D_ARRAY","INT_SAMPLER_3D","INT_SAMPLER_CUBE","INT_VEC2","INT_VEC3","INT_VEC4","INUSE_ATTRIBUTE_ERR","INVALID_ACCESS_ERR","INVALID_CHARACTER_ERR","INVALID_ENUM","INVALID_EXPRESSION_ERR","INVALID_FRAMEBUFFER_OPERATION","INVALID_INDEX","INVALID_MODIFICATION_ERR","INVALID_NODE_TYPE_ERR","INVALID_OPERATION","INVALID_STATE_ERR","INVALID_VALUE","INVERSE_DISTANCE","INVERT","IceCandidate","IdleDeadline","Image","ImageBitmap","ImageBitmapRenderingContext","ImageCapture","ImageData","Infinity","InputDeviceCapabilities","InputDeviceInfo","InputEvent","InputMethodContext","InstallTrigger","InstallTriggerImpl","Instance","Int16Array","Int32Array","Int8Array","Intent","InternalError","IntersectionObserver","IntersectionObserverEntry","Intl","IsSearchProviderInstalled","Iterator","JSON","KEEP","KEYDOWN","KEYFRAMES_RULE","KEYFRAME_RULE","KEYPRESS","KEYUP","KeyEvent","Keyboard","KeyboardEvent","KeyboardLayoutMap","KeyframeEffect","LENGTHADJUST_SPACING","LENGTHADJUST_SPACINGANDGLYPHS","LENGTHADJUST_UNKNOWN","LEQUAL","LESS","LINEAR","LINEAR_DISTANCE","LINEAR_MIPMAP_LINEAR","LINEAR_MIPMAP_NEAREST","LINES","LINE_LOOP","LINE_STRIP","LINE_WIDTH","LINK_STATUS","LIVE","LN10","LN2","LOADED","LOADING","LOG10E","LOG2E","LOWPASS","LOWSHELF","LOW_FLOAT","LOW_INT","LSException","LSParserFilter","LUMINANCE","LUMINANCE_ALPHA","LargestContentfulPaint","LayoutShift","LayoutShiftAttribution","LinearAccelerationSensor","LinkError","ListFormat","LocalMediaStream","Locale","Location","Lock","LockManager","MAX","MAX_3D_TEXTURE_SIZE","MAX_ARRAY_TEXTURE_LAYERS","MAX_CLIENT_WAIT_TIMEOUT_WEBGL","MAX_COLOR_ATTACHMENTS","MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS","MAX_COMBINED_TEXTURE_IMAGE_UNITS","MAX_COMBINED_UNIFORM_BLOCKS","MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS","MAX_CUBE_MAP_TEXTURE_SIZE","MAX_DRAW_BUFFERS","MAX_ELEMENTS_INDICES","MAX_ELEMENTS_VERTICES","MAX_ELEMENT_INDEX","MAX_FRAGMENT_INPUT_COMPONENTS","MAX_FRAGMENT_UNIFORM_BLOCKS","MAX_FRAGMENT_UNIFORM_COMPONENTS","MAX_FRAGMENT_UNIFORM_VECTORS","MAX_PROGRAM_TEXEL_OFFSET","MAX_RENDERBUFFER_SIZE","MAX_SAFE_INTEGER","MAX_SAMPLES","MAX_SERVER_WAIT_TIMEOUT","MAX_TEXTURE_IMAGE_UNITS","MAX_TEXTURE_LOD_BIAS","MAX_TEXTURE_MAX_ANISOTROPY_EXT","MAX_TEXTURE_SIZE","MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS","MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS","MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS","MAX_UNIFORM_BLOCK_SIZE","MAX_UNIFORM_BUFFER_BINDINGS","MAX_VALUE","MAX_VARYING_COMPONENTS","MAX_VARYING_VECTORS","MAX_VERTEX_ATTRIBS","MAX_VERTEX_OUTPUT_COMPONENTS","MAX_VERTEX_TEXTURE_IMAGE_UNITS","MAX_VERTEX_UNIFORM_BLOCKS","MAX_VERTEX_UNIFORM_COMPONENTS","MAX_VERTEX_UNIFORM_VECTORS","MAX_VIEWPORT_DIMS","MEDIA_ERR_ABORTED","MEDIA_ERR_DECODE","MEDIA_ERR_ENCRYPTED","MEDIA_ERR_NETWORK","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_KEYERR_CLIENT","MEDIA_KEYERR_DOMAIN","MEDIA_KEYERR_HARDWARECHANGE","MEDIA_KEYERR_OUTPUT","MEDIA_KEYERR_SERVICE","MEDIA_KEYERR_UNKNOWN","MEDIA_RULE","MEDIUM_FLOAT","MEDIUM_INT","META_MASK","MIDIAccess","MIDIConnectionEvent","MIDIInput","MIDIInputMap","MIDIMessageEvent","MIDIOutput","MIDIOutputMap","MIDIPort","MIN","MIN_PROGRAM_TEXEL_OFFSET","MIN_SAFE_INTEGER","MIN_VALUE","MIRRORED_REPEAT","MODE_ASYNCHRONOUS","MODE_SYNCHRONOUS","MODIFICATION","MOUSEDOWN","MOUSEDRAG","MOUSEMOVE","MOUSEOUT","MOUSEOVER","MOUSEUP","MOZ_KEYFRAMES_RULE","MOZ_KEYFRAME_RULE","MOZ_SOURCE_CURSOR","MOZ_SOURCE_ERASER","MOZ_SOURCE_KEYBOARD","MOZ_SOURCE_MOUSE","MOZ_SOURCE_PEN","MOZ_SOURCE_TOUCH","MOZ_SOURCE_UNKNOWN","MSGESTURE_FLAG_BEGIN","MSGESTURE_FLAG_CANCEL","MSGESTURE_FLAG_END","MSGESTURE_FLAG_INERTIA","MSGESTURE_FLAG_NONE","MSPOINTER_TYPE_MOUSE","MSPOINTER_TYPE_PEN","MSPOINTER_TYPE_TOUCH","MS_ASYNC_CALLBACK_STATUS_ASSIGN_DELEGATE","MS_ASYNC_CALLBACK_STATUS_CANCEL","MS_ASYNC_CALLBACK_STATUS_CHOOSEANY","MS_ASYNC_CALLBACK_STATUS_ERROR","MS_ASYNC_CALLBACK_STATUS_JOIN","MS_ASYNC_OP_STATUS_CANCELED","MS_ASYNC_OP_STATUS_ERROR","MS_ASYNC_OP_STATUS_SUCCESS","MS_MANIPULATION_STATE_ACTIVE","MS_MANIPULATION_STATE_CANCELLED","MS_MANIPULATION_STATE_COMMITTED","MS_MANIPULATION_STATE_DRAGGING","MS_MANIPULATION_STATE_INERTIA","MS_MANIPULATION_STATE_PRESELECT","MS_MANIPULATION_STATE_SELECTING","MS_MANIPULATION_STATE_STOPPED","MS_MEDIA_ERR_ENCRYPTED","MS_MEDIA_KEYERR_CLIENT","MS_MEDIA_KEYERR_DOMAIN","MS_MEDIA_KEYERR_HARDWARECHANGE","MS_MEDIA_KEYERR_OUTPUT","MS_MEDIA_KEYERR_SERVICE","MS_MEDIA_KEYERR_UNKNOWN","Map","Math","MathMLElement","MediaCapabilities","MediaCapabilitiesInfo","MediaController","MediaDeviceInfo","MediaDevices","MediaElementAudioSourceNode","MediaEncryptedEvent","MediaError","MediaKeyError","MediaKeyEvent","MediaKeyMessageEvent","MediaKeyNeededEvent","MediaKeySession","MediaKeyStatusMap","MediaKeySystemAccess","MediaKeys","MediaList","MediaMetadata","MediaQueryList","MediaQueryListEvent","MediaRecorder","MediaRecorderErrorEvent","MediaSession","MediaSettingsRange","MediaSource","MediaStream","MediaStreamAudioDestinationNode","MediaStreamAudioSourceNode","MediaStreamEvent","MediaStreamTrack","MediaStreamTrackAudioSourceNode","MediaStreamTrackEvent","Memory","MessageChannel","MessageEvent","MessagePort","Methods","MimeType","MimeTypeArray","Module","MouseEvent","MouseScrollEvent","MozAnimation","MozAnimationDelay","MozAnimationDirection","MozAnimationDuration","MozAnimationFillMode","MozAnimationIterationCount","MozAnimationName","MozAnimationPlayState","MozAnimationTimingFunction","MozAppearance","MozBackfaceVisibility","MozBinding","MozBorderBottomColors","MozBorderEnd","MozBorderEndColor","MozBorderEndStyle","MozBorderEndWidth","MozBorderImage","MozBorderLeftColors","MozBorderRightColors","MozBorderStart","MozBorderStartColor","MozBorderStartStyle","MozBorderStartWidth","MozBorderTopColors","MozBoxAlign","MozBoxDirection","MozBoxFlex","MozBoxOrdinalGroup","MozBoxOrient","MozBoxPack","MozBoxSizing","MozCSSKeyframeRule","MozCSSKeyframesRule","MozColumnCount","MozColumnFill","MozColumnGap","MozColumnRule","MozColumnRuleColor","MozColumnRuleStyle","MozColumnRuleWidth","MozColumnWidth","MozColumns","MozContactChangeEvent","MozFloatEdge","MozFontFeatureSettings","MozFontLanguageOverride","MozForceBrokenImageIcon","MozHyphens","MozImageRegion","MozMarginEnd","MozMarginStart","MozMmsEvent","MozMmsMessage","MozMobileMessageThread","MozOSXFontSmoothing","MozOrient","MozOsxFontSmoothing","MozOutlineRadius","MozOutlineRadiusBottomleft","MozOutlineRadiusBottomright","MozOutlineRadiusTopleft","MozOutlineRadiusTopright","MozPaddingEnd","MozPaddingStart","MozPerspective","MozPerspectiveOrigin","MozPowerManager","MozSettingsEvent","MozSmsEvent","MozSmsMessage","MozStackSizing","MozTabSize","MozTextAlignLast","MozTextDecorationColor","MozTextDecorationLine","MozTextDecorationStyle","MozTextSizeAdjust","MozTransform","MozTransformOrigin","MozTransformStyle","MozTransition","MozTransitionDelay","MozTransitionDuration","MozTransitionProperty","MozTransitionTimingFunction","MozUserFocus","MozUserInput","MozUserModify","MozUserSelect","MozWindowDragging","MozWindowShadow","MutationEvent","MutationObserver","MutationRecord","NAMESPACE_ERR","NAMESPACE_RULE","NEAREST","NEAREST_MIPMAP_LINEAR","NEAREST_MIPMAP_NEAREST","NEGATIVE_INFINITY","NETWORK_EMPTY","NETWORK_ERR","NETWORK_IDLE","NETWORK_LOADED","NETWORK_LOADING","NETWORK_NO_SOURCE","NEVER","NEW","NEXT","NEXT_NO_DUPLICATE","NICEST","NODE_AFTER","NODE_BEFORE","NODE_BEFORE_AND_AFTER","NODE_INSIDE","NONE","NON_TRANSIENT_ERR","NOTATION_NODE","NOTCH","NOTEQUAL","NOT_ALLOWED_ERR","NOT_FOUND_ERR","NOT_READABLE_ERR","NOT_SUPPORTED_ERR","NO_DATA_ALLOWED_ERR","NO_ERR","NO_ERROR","NO_MODIFICATION_ALLOWED_ERR","NUMBER_TYPE","NUM_COMPRESSED_TEXTURE_FORMATS","NaN","NamedNodeMap","NavigationPreloadManager","Navigator","NearbyLinks","NetworkInformation","Node","NodeFilter","NodeIterator","NodeList","Notation","Notification","NotifyPaintEvent","Number","NumberFormat","OBJECT_TYPE","OBSOLETE","OK","ONE","ONE_MINUS_CONSTANT_ALPHA","ONE_MINUS_CONSTANT_COLOR","ONE_MINUS_DST_ALPHA","ONE_MINUS_DST_COLOR","ONE_MINUS_SRC_ALPHA","ONE_MINUS_SRC_COLOR","OPEN","OPENED","OPENING","ORDERED_NODE_ITERATOR_TYPE","ORDERED_NODE_SNAPSHOT_TYPE","OTHER_ERROR","OUT_OF_MEMORY","Object","OfflineAudioCompletionEvent","OfflineAudioContext","OfflineResourceList","OffscreenCanvas","OffscreenCanvasRenderingContext2D","Option","OrientationSensor","OscillatorNode","OverconstrainedError","OverflowEvent","PACK_ALIGNMENT","PACK_ROW_LENGTH","PACK_SKIP_PIXELS","PACK_SKIP_ROWS","PAGE_RULE","PARSE_ERR","PATHSEG_ARC_ABS","PATHSEG_ARC_REL","PATHSEG_CLOSEPATH","PATHSEG_CURVETO_CUBIC_ABS","PATHSEG_CURVETO_CUBIC_REL","PATHSEG_CURVETO_CUBIC_SMOOTH_ABS","PATHSEG_CURVETO_CUBIC_SMOOTH_REL","PATHSEG_CURVETO_QUADRATIC_ABS","PATHSEG_CURVETO_QUADRATIC_REL","PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS","PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL","PATHSEG_LINETO_ABS","PATHSEG_LINETO_HORIZONTAL_ABS","PATHSEG_LINETO_HORIZONTAL_REL","PATHSEG_LINETO_REL","PATHSEG_LINETO_VERTICAL_ABS","PATHSEG_LINETO_VERTICAL_REL","PATHSEG_MOVETO_ABS","PATHSEG_MOVETO_REL","PATHSEG_UNKNOWN","PATH_EXISTS_ERR","PEAKING","PERMISSION_DENIED","PERSISTENT","PI","PIXEL_PACK_BUFFER","PIXEL_PACK_BUFFER_BINDING","PIXEL_UNPACK_BUFFER","PIXEL_UNPACK_BUFFER_BINDING","PLAYING_STATE","POINTS","POLYGON_OFFSET_FACTOR","POLYGON_OFFSET_FILL","POLYGON_OFFSET_UNITS","POSITION_UNAVAILABLE","POSITIVE_INFINITY","PREV","PREV_NO_DUPLICATE","PROCESSING_INSTRUCTION_NODE","PageChangeEvent","PageTransitionEvent","PaintRequest","PaintRequestList","PannerNode","PasswordCredential","Path2D","PaymentAddress","PaymentInstruments","PaymentManager","PaymentMethodChangeEvent","PaymentRequest","PaymentRequestUpdateEvent","PaymentResponse","Performance","PerformanceElementTiming","PerformanceEntry","PerformanceEventTiming","PerformanceLongTaskTiming","PerformanceMark","PerformanceMeasure","PerformanceNavigation","PerformanceNavigationTiming","PerformanceObserver","PerformanceObserverEntryList","PerformancePaintTiming","PerformanceResourceTiming","PerformanceServerTiming","PerformanceTiming","PeriodicSyncManager","PeriodicWave","PermissionStatus","Permissions","PhotoCapabilities","PictureInPictureWindow","Plugin","PluginArray","PluralRules","PointerEvent","PopStateEvent","PopupBlockedEvent","Presentation","PresentationAvailability","PresentationConnection","PresentationConnectionAvailableEvent","PresentationConnectionCloseEvent","PresentationConnectionList","PresentationReceiver","PresentationRequest","ProcessingInstruction","ProgressEvent","Promise","PromiseRejectionEvent","PropertyNodeList","Proxy","PublicKeyCredential","PushManager","PushSubscription","PushSubscriptionOptions","Q","QUERY_RESULT","QUERY_RESULT_AVAILABLE","QUOTA_ERR","QUOTA_EXCEEDED_ERR","QueryInterface","R11F_G11F_B10F","R16F","R16I","R16UI","R32F","R32I","R32UI","R8","R8I","R8UI","R8_SNORM","RASTERIZER_DISCARD","READ_BUFFER","READ_FRAMEBUFFER","READ_FRAMEBUFFER_BINDING","READ_ONLY","READ_ONLY_ERR","READ_WRITE","RED","RED_BITS","RED_INTEGER","REMOVAL","RENDERBUFFER","RENDERBUFFER_ALPHA_SIZE","RENDERBUFFER_BINDING","RENDERBUFFER_BLUE_SIZE","RENDERBUFFER_DEPTH_SIZE","RENDERBUFFER_GREEN_SIZE","RENDERBUFFER_HEIGHT","RENDERBUFFER_INTERNAL_FORMAT","RENDERBUFFER_RED_SIZE","RENDERBUFFER_SAMPLES","RENDERBUFFER_STENCIL_SIZE","RENDERBUFFER_WIDTH","RENDERER","RENDERING_INTENT_ABSOLUTE_COLORIMETRIC","RENDERING_INTENT_AUTO","RENDERING_INTENT_PERCEPTUAL","RENDERING_INTENT_RELATIVE_COLORIMETRIC","RENDERING_INTENT_SATURATION","RENDERING_INTENT_UNKNOWN","REPEAT","REPLACE","RG","RG16F","RG16I","RG16UI","RG32F","RG32I","RG32UI","RG8","RG8I","RG8UI","RG8_SNORM","RGB","RGB10_A2","RGB10_A2UI","RGB16F","RGB16I","RGB16UI","RGB32F","RGB32I","RGB32UI","RGB565","RGB5_A1","RGB8","RGB8I","RGB8UI","RGB8_SNORM","RGB9_E5","RGBA","RGBA16F","RGBA16I","RGBA16UI","RGBA32F","RGBA32I","RGBA32UI","RGBA4","RGBA8","RGBA8I","RGBA8UI","RGBA8_SNORM","RGBA_INTEGER","RGBColor","RGB_INTEGER","RG_INTEGER","ROTATION_CLOCKWISE","ROTATION_COUNTERCLOCKWISE","RTCCertificate","RTCDTMFSender","RTCDTMFToneChangeEvent","RTCDataChannel","RTCDataChannelEvent","RTCDtlsTransport","RTCError","RTCErrorEvent","RTCIceCandidate","RTCIceTransport","RTCPeerConnection","RTCPeerConnectionIceErrorEvent","RTCPeerConnectionIceEvent","RTCRtpReceiver","RTCRtpSender","RTCRtpTransceiver","RTCSctpTransport","RTCSessionDescription","RTCStatsReport","RTCTrackEvent","RadioNodeList","Range","RangeError","RangeException","ReadableStream","ReadableStreamDefaultReader","RecordErrorEvent","Rect","ReferenceError","Reflect","RegExp","RelativeOrientationSensor","RelativeTimeFormat","RemotePlayback","Report","ReportBody","ReportingObserver","Request","ResizeObserver","ResizeObserverEntry","ResizeObserverSize","Response","RuntimeError","SAMPLER_2D","SAMPLER_2D_ARRAY","SAMPLER_2D_ARRAY_SHADOW","SAMPLER_2D_SHADOW","SAMPLER_3D","SAMPLER_BINDING","SAMPLER_CUBE","SAMPLER_CUBE_SHADOW","SAMPLES","SAMPLE_ALPHA_TO_COVERAGE","SAMPLE_BUFFERS","SAMPLE_COVERAGE","SAMPLE_COVERAGE_INVERT","SAMPLE_COVERAGE_VALUE","SAWTOOTH","SCHEDULED_STATE","SCISSOR_BOX","SCISSOR_TEST","SCROLL_PAGE_DOWN","SCROLL_PAGE_UP","SDP_ANSWER","SDP_OFFER","SDP_PRANSWER","SECURITY_ERR","SELECT","SEPARATE_ATTRIBS","SERIALIZE_ERR","SEVERITY_ERROR","SEVERITY_FATAL_ERROR","SEVERITY_WARNING","SHADER_COMPILER","SHADER_TYPE","SHADING_LANGUAGE_VERSION","SHIFT_MASK","SHORT","SHOWING","SHOW_ALL","SHOW_ATTRIBUTE","SHOW_CDATA_SECTION","SHOW_COMMENT","SHOW_DOCUMENT","SHOW_DOCUMENT_FRAGMENT","SHOW_DOCUMENT_TYPE","SHOW_ELEMENT","SHOW_ENTITY","SHOW_ENTITY_REFERENCE","SHOW_NOTATION","SHOW_PROCESSING_INSTRUCTION","SHOW_TEXT","SIGNALED","SIGNED_NORMALIZED","SINE","SOUNDFIELD","SQLException","SQRT1_2","SQRT2","SQUARE","SRC_ALPHA","SRC_ALPHA_SATURATE","SRC_COLOR","SRGB","SRGB8","SRGB8_ALPHA8","START_TO_END","START_TO_START","STATIC_COPY","STATIC_DRAW","STATIC_READ","STENCIL","STENCIL_ATTACHMENT","STENCIL_BACK_FAIL","STENCIL_BACK_FUNC","STENCIL_BACK_PASS_DEPTH_FAIL","STENCIL_BACK_PASS_DEPTH_PASS","STENCIL_BACK_REF","STENCIL_BACK_VALUE_MASK","STENCIL_BACK_WRITEMASK","STENCIL_BITS","STENCIL_BUFFER_BIT","STENCIL_CLEAR_VALUE","STENCIL_FAIL","STENCIL_FUNC","STENCIL_INDEX","STENCIL_INDEX8","STENCIL_PASS_DEPTH_FAIL","STENCIL_PASS_DEPTH_PASS","STENCIL_REF","STENCIL_TEST","STENCIL_VALUE_MASK","STENCIL_WRITEMASK","STREAM_COPY","STREAM_DRAW","STREAM_READ","STRING_TYPE","STYLE_RULE","SUBPIXEL_BITS","SUPPORTS_RULE","SVGAElement","SVGAltGlyphDefElement","SVGAltGlyphElement","SVGAltGlyphItemElement","SVGAngle","SVGAnimateColorElement","SVGAnimateElement","SVGAnimateMotionElement","SVGAnimateTransformElement","SVGAnimatedAngle","SVGAnimatedBoolean","SVGAnimatedEnumeration","SVGAnimatedInteger","SVGAnimatedLength","SVGAnimatedLengthList","SVGAnimatedNumber","SVGAnimatedNumberList","SVGAnimatedPreserveAspectRatio","SVGAnimatedRect","SVGAnimatedString","SVGAnimatedTransformList","SVGAnimationElement","SVGCircleElement","SVGClipPathElement","SVGColor","SVGComponentTransferFunctionElement","SVGCursorElement","SVGDefsElement","SVGDescElement","SVGDiscardElement","SVGDocument","SVGElement","SVGElementInstance","SVGElementInstanceList","SVGEllipseElement","SVGException","SVGFEBlendElement","SVGFEColorMatrixElement","SVGFEComponentTransferElement","SVGFECompositeElement","SVGFEConvolveMatrixElement","SVGFEDiffuseLightingElement","SVGFEDisplacementMapElement","SVGFEDistantLightElement","SVGFEDropShadowElement","SVGFEFloodElement","SVGFEFuncAElement","SVGFEFuncBElement","SVGFEFuncGElement","SVGFEFuncRElement","SVGFEGaussianBlurElement","SVGFEImageElement","SVGFEMergeElement","SVGFEMergeNodeElement","SVGFEMorphologyElement","SVGFEOffsetElement","SVGFEPointLightElement","SVGFESpecularLightingElement","SVGFESpotLightElement","SVGFETileElement","SVGFETurbulenceElement","SVGFilterElement","SVGFontElement","SVGFontFaceElement","SVGFontFaceFormatElement","SVGFontFaceNameElement","SVGFontFaceSrcElement","SVGFontFaceUriElement","SVGForeignObjectElement","SVGGElement","SVGGeometryElement","SVGGlyphElement","SVGGlyphRefElement","SVGGradientElement","SVGGraphicsElement","SVGHKernElement","SVGImageElement","SVGLength","SVGLengthList","SVGLineElement","SVGLinearGradientElement","SVGMPathElement","SVGMarkerElement","SVGMaskElement","SVGMatrix","SVGMetadataElement","SVGMissingGlyphElement","SVGNumber","SVGNumberList","SVGPaint","SVGPathElement","SVGPathSeg","SVGPathSegArcAbs","SVGPathSegArcRel","SVGPathSegClosePath","SVGPathSegCurvetoCubicAbs","SVGPathSegCurvetoCubicRel","SVGPathSegCurvetoCubicSmoothAbs","SVGPathSegCurvetoCubicSmoothRel","SVGPathSegCurvetoQuadraticAbs","SVGPathSegCurvetoQuadraticRel","SVGPathSegCurvetoQuadraticSmoothAbs","SVGPathSegCurvetoQuadraticSmoothRel","SVGPathSegLinetoAbs","SVGPathSegLinetoHorizontalAbs","SVGPathSegLinetoHorizontalRel","SVGPathSegLinetoRel","SVGPathSegLinetoVerticalAbs","SVGPathSegLinetoVerticalRel","SVGPathSegList","SVGPathSegMovetoAbs","SVGPathSegMovetoRel","SVGPatternElement","SVGPoint","SVGPointList","SVGPolygonElement","SVGPolylineElement","SVGPreserveAspectRatio","SVGRadialGradientElement","SVGRect","SVGRectElement","SVGRenderingIntent","SVGSVGElement","SVGScriptElement","SVGSetElement","SVGStopElement","SVGStringList","SVGStyleElement","SVGSwitchElement","SVGSymbolElement","SVGTRefElement","SVGTSpanElement","SVGTextContentElement","SVGTextElement","SVGTextPathElement","SVGTextPositioningElement","SVGTitleElement","SVGTransform","SVGTransformList","SVGUnitTypes","SVGUseElement","SVGVKernElement","SVGViewElement","SVGViewSpec","SVGZoomAndPan","SVGZoomEvent","SVG_ANGLETYPE_DEG","SVG_ANGLETYPE_GRAD","SVG_ANGLETYPE_RAD","SVG_ANGLETYPE_UNKNOWN","SVG_ANGLETYPE_UNSPECIFIED","SVG_CHANNEL_A","SVG_CHANNEL_B","SVG_CHANNEL_G","SVG_CHANNEL_R","SVG_CHANNEL_UNKNOWN","SVG_COLORTYPE_CURRENTCOLOR","SVG_COLORTYPE_RGBCOLOR","SVG_COLORTYPE_RGBCOLOR_ICCCOLOR","SVG_COLORTYPE_UNKNOWN","SVG_EDGEMODE_DUPLICATE","SVG_EDGEMODE_NONE","SVG_EDGEMODE_UNKNOWN","SVG_EDGEMODE_WRAP","SVG_FEBLEND_MODE_COLOR","SVG_FEBLEND_MODE_COLOR_BURN","SVG_FEBLEND_MODE_COLOR_DODGE","SVG_FEBLEND_MODE_DARKEN","SVG_FEBLEND_MODE_DIFFERENCE","SVG_FEBLEND_MODE_EXCLUSION","SVG_FEBLEND_MODE_HARD_LIGHT","SVG_FEBLEND_MODE_HUE","SVG_FEBLEND_MODE_LIGHTEN","SVG_FEBLEND_MODE_LUMINOSITY","SVG_FEBLEND_MODE_MULTIPLY","SVG_FEBLEND_MODE_NORMAL","SVG_FEBLEND_MODE_OVERLAY","SVG_FEBLEND_MODE_SATURATION","SVG_FEBLEND_MODE_SCREEN","SVG_FEBLEND_MODE_SOFT_LIGHT","SVG_FEBLEND_MODE_UNKNOWN","SVG_FECOLORMATRIX_TYPE_HUEROTATE","SVG_FECOLORMATRIX_TYPE_LUMINANCETOALPHA","SVG_FECOLORMATRIX_TYPE_MATRIX","SVG_FECOLORMATRIX_TYPE_SATURATE","SVG_FECOLORMATRIX_TYPE_UNKNOWN","SVG_FECOMPONENTTRANSFER_TYPE_DISCRETE","SVG_FECOMPONENTTRANSFER_TYPE_GAMMA","SVG_FECOMPONENTTRANSFER_TYPE_IDENTITY","SVG_FECOMPONENTTRANSFER_TYPE_LINEAR","SVG_FECOMPONENTTRANSFER_TYPE_TABLE","SVG_FECOMPONENTTRANSFER_TYPE_UNKNOWN","SVG_FECOMPOSITE_OPERATOR_ARITHMETIC","SVG_FECOMPOSITE_OPERATOR_ATOP","SVG_FECOMPOSITE_OPERATOR_IN","SVG_FECOMPOSITE_OPERATOR_OUT","SVG_FECOMPOSITE_OPERATOR_OVER","SVG_FECOMPOSITE_OPERATOR_UNKNOWN","SVG_FECOMPOSITE_OPERATOR_XOR","SVG_INVALID_VALUE_ERR","SVG_LENGTHTYPE_CM","SVG_LENGTHTYPE_EMS","SVG_LENGTHTYPE_EXS","SVG_LENGTHTYPE_IN","SVG_LENGTHTYPE_MM","SVG_LENGTHTYPE_NUMBER","SVG_LENGTHTYPE_PC","SVG_LENGTHTYPE_PERCENTAGE","SVG_LENGTHTYPE_PT","SVG_LENGTHTYPE_PX","SVG_LENGTHTYPE_UNKNOWN","SVG_MARKERUNITS_STROKEWIDTH","SVG_MARKERUNITS_UNKNOWN","SVG_MARKERUNITS_USERSPACEONUSE","SVG_MARKER_ORIENT_ANGLE","SVG_MARKER_ORIENT_AUTO","SVG_MARKER_ORIENT_UNKNOWN","SVG_MASKTYPE_ALPHA","SVG_MASKTYPE_LUMINANCE","SVG_MATRIX_NOT_INVERTABLE","SVG_MEETORSLICE_MEET","SVG_MEETORSLICE_SLICE","SVG_MEETORSLICE_UNKNOWN","SVG_MORPHOLOGY_OPERATOR_DILATE","SVG_MORPHOLOGY_OPERATOR_ERODE","SVG_MORPHOLOGY_OPERATOR_UNKNOWN","SVG_PAINTTYPE_CURRENTCOLOR","SVG_PAINTTYPE_NONE","SVG_PAINTTYPE_RGBCOLOR","SVG_PAINTTYPE_RGBCOLOR_ICCCOLOR","SVG_PAINTTYPE_UNKNOWN","SVG_PAINTTYPE_URI","SVG_PAINTTYPE_URI_CURRENTCOLOR","SVG_PAINTTYPE_URI_NONE","SVG_PAINTTYPE_URI_RGBCOLOR","SVG_PAINTTYPE_URI_RGBCOLOR_ICCCOLOR","SVG_PRESERVEASPECTRATIO_NONE","SVG_PRESERVEASPECTRATIO_UNKNOWN","SVG_PRESERVEASPECTRATIO_XMAXYMAX","SVG_PRESERVEASPECTRATIO_XMAXYMID","SVG_PRESERVEASPECTRATIO_XMAXYMIN","SVG_PRESERVEASPECTRATIO_XMIDYMAX","SVG_PRESERVEASPECTRATIO_XMIDYMID","SVG_PRESERVEASPECTRATIO_XMIDYMIN","SVG_PRESERVEASPECTRATIO_XMINYMAX","SVG_PRESERVEASPECTRATIO_XMINYMID","SVG_PRESERVEASPECTRATIO_XMINYMIN","SVG_SPREADMETHOD_PAD","SVG_SPREADMETHOD_REFLECT","SVG_SPREADMETHOD_REPEAT","SVG_SPREADMETHOD_UNKNOWN","SVG_STITCHTYPE_NOSTITCH","SVG_STITCHTYPE_STITCH","SVG_STITCHTYPE_UNKNOWN","SVG_TRANSFORM_MATRIX","SVG_TRANSFORM_ROTATE","SVG_TRANSFORM_SCALE","SVG_TRANSFORM_SKEWX","SVG_TRANSFORM_SKEWY","SVG_TRANSFORM_TRANSLATE","SVG_TRANSFORM_UNKNOWN","SVG_TURBULENCE_TYPE_FRACTALNOISE","SVG_TURBULENCE_TYPE_TURBULENCE","SVG_TURBULENCE_TYPE_UNKNOWN","SVG_UNIT_TYPE_OBJECTBOUNDINGBOX","SVG_UNIT_TYPE_UNKNOWN","SVG_UNIT_TYPE_USERSPACEONUSE","SVG_WRONG_TYPE_ERR","SVG_ZOOMANDPAN_DISABLE","SVG_ZOOMANDPAN_MAGNIFY","SVG_ZOOMANDPAN_UNKNOWN","SYNC_CONDITION","SYNC_FENCE","SYNC_FLAGS","SYNC_FLUSH_COMMANDS_BIT","SYNC_GPU_COMMANDS_COMPLETE","SYNC_STATUS","SYNTAX_ERR","SavedPages","Screen","ScreenOrientation","Script","ScriptProcessorNode","ScrollAreaEvent","SecurityPolicyViolationEvent","Selection","Sensor","SensorErrorEvent","ServiceWorker","ServiceWorkerContainer","ServiceWorkerRegistration","SessionDescription","Set","ShadowRoot","SharedArrayBuffer","SharedWorker","SimpleGestureEvent","SourceBuffer","SourceBufferList","SpeechSynthesis","SpeechSynthesisErrorEvent","SpeechSynthesisEvent","SpeechSynthesisUtterance","SpeechSynthesisVoice","StaticRange","StereoPannerNode","StopIteration","Storage","StorageEvent","StorageManager","String","StructType","StylePropertyMap","StylePropertyMapReadOnly","StyleSheet","StyleSheetList","SubmitEvent","SubtleCrypto","Symbol","SyncManager","SyntaxError","TEMPORARY","TEXTPATH_METHODTYPE_ALIGN","TEXTPATH_METHODTYPE_STRETCH","TEXTPATH_METHODTYPE_UNKNOWN","TEXTPATH_SPACINGTYPE_AUTO","TEXTPATH_SPACINGTYPE_EXACT","TEXTPATH_SPACINGTYPE_UNKNOWN","TEXTURE","TEXTURE0","TEXTURE1","TEXTURE10","TEXTURE11","TEXTURE12","TEXTURE13","TEXTURE14","TEXTURE15","TEXTURE16","TEXTURE17","TEXTURE18","TEXTURE19","TEXTURE2","TEXTURE20","TEXTURE21","TEXTURE22","TEXTURE23","TEXTURE24","TEXTURE25","TEXTURE26","TEXTURE27","TEXTURE28","TEXTURE29","TEXTURE3","TEXTURE30","TEXTURE31","TEXTURE4","TEXTURE5","TEXTURE6","TEXTURE7","TEXTURE8","TEXTURE9","TEXTURE_2D","TEXTURE_2D_ARRAY","TEXTURE_3D","TEXTURE_BASE_LEVEL","TEXTURE_BINDING_2D","TEXTURE_BINDING_2D_ARRAY","TEXTURE_BINDING_3D","TEXTURE_BINDING_CUBE_MAP","TEXTURE_COMPARE_FUNC","TEXTURE_COMPARE_MODE","TEXTURE_CUBE_MAP","TEXTURE_CUBE_MAP_NEGATIVE_X","TEXTURE_CUBE_MAP_NEGATIVE_Y","TEXTURE_CUBE_MAP_NEGATIVE_Z","TEXTURE_CUBE_MAP_POSITIVE_X","TEXTURE_CUBE_MAP_POSITIVE_Y","TEXTURE_CUBE_MAP_POSITIVE_Z","TEXTURE_IMMUTABLE_FORMAT","TEXTURE_IMMUTABLE_LEVELS","TEXTURE_MAG_FILTER","TEXTURE_MAX_ANISOTROPY_EXT","TEXTURE_MAX_LEVEL","TEXTURE_MAX_LOD","TEXTURE_MIN_FILTER","TEXTURE_MIN_LOD","TEXTURE_WRAP_R","TEXTURE_WRAP_S","TEXTURE_WRAP_T","TEXT_NODE","TIMEOUT","TIMEOUT_ERR","TIMEOUT_EXPIRED","TIMEOUT_IGNORED","TOO_LARGE_ERR","TRANSACTION_INACTIVE_ERR","TRANSFORM_FEEDBACK","TRANSFORM_FEEDBACK_ACTIVE","TRANSFORM_FEEDBACK_BINDING","TRANSFORM_FEEDBACK_BUFFER","TRANSFORM_FEEDBACK_BUFFER_BINDING","TRANSFORM_FEEDBACK_BUFFER_MODE","TRANSFORM_FEEDBACK_BUFFER_SIZE","TRANSFORM_FEEDBACK_BUFFER_START","TRANSFORM_FEEDBACK_PAUSED","TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN","TRANSFORM_FEEDBACK_VARYINGS","TRIANGLE","TRIANGLES","TRIANGLE_FAN","TRIANGLE_STRIP","TYPE_BACK_FORWARD","TYPE_ERR","TYPE_MISMATCH_ERR","TYPE_NAVIGATE","TYPE_RELOAD","TYPE_RESERVED","Table","TaskAttributionTiming","Text","TextDecoder","TextDecoderStream","TextEncoder","TextEncoderStream","TextEvent","TextMetrics","TextTrack","TextTrackCue","TextTrackCueList","TextTrackList","TimeEvent","TimeRanges","Touch","TouchEvent","TouchList","TrackEvent","TransformStream","TransitionEvent","TreeWalker","TrustedHTML","TrustedScript","TrustedScriptURL","TrustedTypePolicy","TrustedTypePolicyFactory","TypeError","TypedObject","U2F","UIEvent","UNCACHED","UNIFORM_ARRAY_STRIDE","UNIFORM_BLOCK_ACTIVE_UNIFORMS","UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES","UNIFORM_BLOCK_BINDING","UNIFORM_BLOCK_DATA_SIZE","UNIFORM_BLOCK_INDEX","UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER","UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER","UNIFORM_BUFFER","UNIFORM_BUFFER_BINDING","UNIFORM_BUFFER_OFFSET_ALIGNMENT","UNIFORM_BUFFER_SIZE","UNIFORM_BUFFER_START","UNIFORM_IS_ROW_MAJOR","UNIFORM_MATRIX_STRIDE","UNIFORM_OFFSET","UNIFORM_SIZE","UNIFORM_TYPE","UNKNOWN_ERR","UNKNOWN_RULE","UNMASKED_RENDERER_WEBGL","UNMASKED_VENDOR_WEBGL","UNORDERED_NODE_ITERATOR_TYPE","UNORDERED_NODE_SNAPSHOT_TYPE","UNPACK_ALIGNMENT","UNPACK_COLORSPACE_CONVERSION_WEBGL","UNPACK_FLIP_Y_WEBGL","UNPACK_IMAGE_HEIGHT","UNPACK_PREMULTIPLY_ALPHA_WEBGL","UNPACK_ROW_LENGTH","UNPACK_SKIP_IMAGES","UNPACK_SKIP_PIXELS","UNPACK_SKIP_ROWS","UNSCHEDULED_STATE","UNSENT","UNSIGNALED","UNSIGNED_BYTE","UNSIGNED_INT","UNSIGNED_INT_10F_11F_11F_REV","UNSIGNED_INT_24_8","UNSIGNED_INT_2_10_10_10_REV","UNSIGNED_INT_5_9_9_9_REV","UNSIGNED_INT_SAMPLER_2D","UNSIGNED_INT_SAMPLER_2D_ARRAY","UNSIGNED_INT_SAMPLER_3D","UNSIGNED_INT_SAMPLER_CUBE","UNSIGNED_INT_VEC2","UNSIGNED_INT_VEC3","UNSIGNED_INT_VEC4","UNSIGNED_NORMALIZED","UNSIGNED_SHORT","UNSIGNED_SHORT_4_4_4_4","UNSIGNED_SHORT_5_5_5_1","UNSIGNED_SHORT_5_6_5","UNSPECIFIED_EVENT_TYPE_ERR","UPDATEREADY","URIError","URL","URLSearchParams","URLUnencoded","URL_MISMATCH_ERR","USB","USBAlternateInterface","USBConfiguration","USBConnectionEvent","USBDevice","USBEndpoint","USBInTransferResult","USBInterface","USBIsochronousInTransferPacket","USBIsochronousInTransferResult","USBIsochronousOutTransferPacket","USBIsochronousOutTransferResult","USBOutTransferResult","UTC","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray","UserActivation","UserMessageHandler","UserMessageHandlersNamespace","UserProximityEvent","VALIDATE_STATUS","VALIDATION_ERR","VARIABLES_RULE","VENDOR","VERSION","VERSION_CHANGE","VERSION_ERR","VERTEX_ARRAY_BINDING","VERTEX_ATTRIB_ARRAY_BUFFER_BINDING","VERTEX_ATTRIB_ARRAY_DIVISOR","VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE","VERTEX_ATTRIB_ARRAY_ENABLED","VERTEX_ATTRIB_ARRAY_INTEGER","VERTEX_ATTRIB_ARRAY_NORMALIZED","VERTEX_ATTRIB_ARRAY_POINTER","VERTEX_ATTRIB_ARRAY_SIZE","VERTEX_ATTRIB_ARRAY_STRIDE","VERTEX_ATTRIB_ARRAY_TYPE","VERTEX_SHADER","VERTICAL","VERTICAL_AXIS","VER_ERR","VIEWPORT","VIEWPORT_RULE","VRDisplay","VRDisplayCapabilities","VRDisplayEvent","VREyeParameters","VRFieldOfView","VRFrameData","VRPose","VRStageParameters","VTTCue","VTTRegion","ValidityState","VideoPlaybackQuality","VideoStreamTrack","VisualViewport","WAIT_FAILED","WEBKIT_FILTER_RULE","WEBKIT_KEYFRAMES_RULE","WEBKIT_KEYFRAME_RULE","WEBKIT_REGION_RULE","WRONG_DOCUMENT_ERR","WakeLock","WakeLockSentinel","WasmAnyRef","WaveShaperNode","WeakMap","WeakRef","WeakSet","WebAssembly","WebGL2RenderingContext","WebGLActiveInfo","WebGLBuffer","WebGLContextEvent","WebGLFramebuffer","WebGLProgram","WebGLQuery","WebGLRenderbuffer","WebGLRenderingContext","WebGLSampler","WebGLShader","WebGLShaderPrecisionFormat","WebGLSync","WebGLTexture","WebGLTransformFeedback","WebGLUniformLocation","WebGLVertexArray","WebGLVertexArrayObject","WebKitAnimationEvent","WebKitBlobBuilder","WebKitCSSFilterRule","WebKitCSSFilterValue","WebKitCSSKeyframeRule","WebKitCSSKeyframesRule","WebKitCSSMatrix","WebKitCSSRegionRule","WebKitCSSTransformValue","WebKitDataCue","WebKitGamepad","WebKitMediaKeyError","WebKitMediaKeyMessageEvent","WebKitMediaKeySession","WebKitMediaKeys","WebKitMediaSource","WebKitMutationObserver","WebKitNamespace","WebKitPlaybackTargetAvailabilityEvent","WebKitPoint","WebKitShadowRoot","WebKitSourceBuffer","WebKitSourceBufferList","WebKitTransitionEvent","WebSocket","WebkitAlignContent","WebkitAlignItems","WebkitAlignSelf","WebkitAnimation","WebkitAnimationDelay","WebkitAnimationDirection","WebkitAnimationDuration","WebkitAnimationFillMode","WebkitAnimationIterationCount","WebkitAnimationName","WebkitAnimationPlayState","WebkitAnimationTimingFunction","WebkitAppearance","WebkitBackfaceVisibility","WebkitBackgroundClip","WebkitBackgroundOrigin","WebkitBackgroundSize","WebkitBorderBottomLeftRadius","WebkitBorderBottomRightRadius","WebkitBorderImage","WebkitBorderRadius","WebkitBorderTopLeftRadius","WebkitBorderTopRightRadius","WebkitBoxAlign","WebkitBoxDirection","WebkitBoxFlex","WebkitBoxOrdinalGroup","WebkitBoxOrient","WebkitBoxPack","WebkitBoxShadow","WebkitBoxSizing","WebkitFilter","WebkitFlex","WebkitFlexBasis","WebkitFlexDirection","WebkitFlexFlow","WebkitFlexGrow","WebkitFlexShrink","WebkitFlexWrap","WebkitJustifyContent","WebkitLineClamp","WebkitMask","WebkitMaskClip","WebkitMaskComposite","WebkitMaskImage","WebkitMaskOrigin","WebkitMaskPosition","WebkitMaskPositionX","WebkitMaskPositionY","WebkitMaskRepeat","WebkitMaskSize","WebkitOrder","WebkitPerspective","WebkitPerspectiveOrigin","WebkitTextFillColor","WebkitTextSizeAdjust","WebkitTextStroke","WebkitTextStrokeColor","WebkitTextStrokeWidth","WebkitTransform","WebkitTransformOrigin","WebkitTransformStyle","WebkitTransition","WebkitTransitionDelay","WebkitTransitionDuration","WebkitTransitionProperty","WebkitTransitionTimingFunction","WebkitUserSelect","WheelEvent","Window","Worker","Worklet","WritableStream","WritableStreamDefaultWriter","XMLDocument","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestException","XMLHttpRequestProgressEvent","XMLHttpRequestUpload","XMLSerializer","XMLStylesheetProcessingInstruction","XPathEvaluator","XPathException","XPathExpression","XPathNSResolver","XPathResult","XRBoundedReferenceSpace","XRDOMOverlayState","XRFrame","XRHitTestResult","XRHitTestSource","XRInputSource","XRInputSourceArray","XRInputSourceEvent","XRInputSourcesChangeEvent","XRLayer","XRPose","XRRay","XRReferenceSpace","XRReferenceSpaceEvent","XRRenderState","XRRigidTransform","XRSession","XRSessionEvent","XRSpace","XRSystem","XRTransientInputHitTestResult","XRTransientInputHitTestSource","XRView","XRViewerPose","XRViewport","XRWebGLLayer","XSLTProcessor","ZERO","_XD0M_","_YD0M_","__defineGetter__","__defineSetter__","__lookupGetter__","__lookupSetter__","__opera","__proto__","_browserjsran","a","aLink","abbr","abort","aborted","abs","absolute","acceleration","accelerationIncludingGravity","accelerator","accept","acceptCharset","acceptNode","accessKey","accessKeyLabel","accuracy","acos","acosh","action","actionURL","actions","activated","active","activeCues","activeElement","activeSourceBuffers","activeSourceCount","activeTexture","activeVRDisplays","actualBoundingBoxAscent","actualBoundingBoxDescent","actualBoundingBoxLeft","actualBoundingBoxRight","add","addAll","addBehavior","addCandidate","addColorStop","addCue","addElement","addEventListener","addFilter","addFromString","addFromUri","addIceCandidate","addImport","addListener","addModule","addNamed","addPageRule","addPath","addPointer","addRange","addRegion","addRule","addSearchEngine","addSourceBuffer","addStream","addTextTrack","addTrack","addTransceiver","addWakeLockListener","added","addedNodes","additionalName","additiveSymbols","addons","address","addressLine","adoptNode","adoptedStyleSheets","adr","advance","after","album","alert","algorithm","align","align-content","align-items","align-self","alignContent","alignItems","alignSelf","alignmentBaseline","alinkColor","all","allSettled","allow","allowFullscreen","allowPaymentRequest","allowedDirections","allowedFeatures","allowedToPlay","allowsFeature","alpha","alt","altGraphKey","altHtml","altKey","altLeft","alternate","alternateSetting","alternates","altitude","altitudeAccuracy","amplitude","ancestorOrigins","anchor","anchorNode","anchorOffset","anchors","and","angle","angularAcceleration","angularVelocity","animVal","animate","animatedInstanceRoot","animatedNormalizedPathSegList","animatedPathSegList","animatedPoints","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","animationDelay","animationDirection","animationDuration","animationFillMode","animationIterationCount","animationName","animationPlayState","animationStartTime","animationTimingFunction","animationsPaused","anniversary","antialias","anticipatedRemoval","any","app","appCodeName","appMinorVersion","appName","appNotifications","appVersion","appearance","append","appendBuffer","appendChild","appendData","appendItem","appendMedium","appendNamed","appendRule","appendStream","appendWindowEnd","appendWindowStart","applets","applicationCache","applicationServerKey","apply","applyConstraints","applyElement","arc","arcTo","architecture","archive","areas","arguments","ariaAtomic","ariaAutoComplete","ariaBusy","ariaChecked","ariaColCount","ariaColIndex","ariaColSpan","ariaCurrent","ariaDescription","ariaDisabled","ariaExpanded","ariaHasPopup","ariaHidden","ariaKeyShortcuts","ariaLabel","ariaLevel","ariaLive","ariaModal","ariaMultiLine","ariaMultiSelectable","ariaOrientation","ariaPlaceholder","ariaPosInSet","ariaPressed","ariaReadOnly","ariaRelevant","ariaRequired","ariaRoleDescription","ariaRowCount","ariaRowIndex","ariaRowSpan","ariaSelected","ariaSetSize","ariaSort","ariaValueMax","ariaValueMin","ariaValueNow","ariaValueText","arrayBuffer","artist","artwork","as","asIntN","asUintN","asin","asinh","assert","assign","assignedElements","assignedNodes","assignedSlot","async","asyncIterator","atEnd","atan","atan2","atanh","atob","attachEvent","attachInternals","attachShader","attachShadow","attachments","attack","attestationObject","attrChange","attrName","attributeFilter","attributeName","attributeNamespace","attributeOldValue","attributeStyleMap","attributes","attribution","audioBitsPerSecond","audioTracks","audioWorklet","authenticatedSignedWrites","authenticatorData","autoIncrement","autobuffer","autocapitalize","autocomplete","autocorrect","autofocus","automationRate","autoplay","availHeight","availLeft","availTop","availWidth","availability","available","aversion","ax","axes","axis","ay","azimuth","b","back","backface-visibility","backfaceVisibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","backgroundAttachment","backgroundBlendMode","backgroundClip","backgroundColor","backgroundFetch","backgroundImage","backgroundOrigin","backgroundPosition","backgroundPositionX","backgroundPositionY","backgroundRepeat","backgroundSize","badInput","badge","balance","baseFrequencyX","baseFrequencyY","baseLatency","baseLayer","baseNode","baseOffset","baseURI","baseVal","baselineShift","battery","bday","before","beginElement","beginElementAt","beginPath","beginQuery","beginTransformFeedback","behavior","behaviorCookie","behaviorPart","behaviorUrns","beta","bezierCurveTo","bgColor","bgProperties","bias","big","bigint64","biguint64","binaryType","bind","bindAttribLocation","bindBuffer","bindBufferBase","bindBufferRange","bindFramebuffer","bindRenderbuffer","bindSampler","bindTexture","bindTransformFeedback","bindVertexArray","bitness","blendColor","blendEquation","blendEquationSeparate","blendFunc","blendFuncSeparate","blink","blitFramebuffer","blob","block-size","blockDirection","blockSize","blockedURI","blue","bluetooth","blur","body","bodyUsed","bold","bookmarks","booleanValue","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","borderBlock","borderBlockColor","borderBlockEnd","borderBlockEndColor","borderBlockEndStyle","borderBlockEndWidth","borderBlockStart","borderBlockStartColor","borderBlockStartStyle","borderBlockStartWidth","borderBlockStyle","borderBlockWidth","borderBottom","borderBottomColor","borderBottomLeftRadius","borderBottomRightRadius","borderBottomStyle","borderBottomWidth","borderBoxSize","borderCollapse","borderColor","borderColorDark","borderColorLight","borderEndEndRadius","borderEndStartRadius","borderImage","borderImageOutset","borderImageRepeat","borderImageSlice","borderImageSource","borderImageWidth","borderInline","borderInlineColor","borderInlineEnd","borderInlineEndColor","borderInlineEndStyle","borderInlineEndWidth","borderInlineStart","borderInlineStartColor","borderInlineStartStyle","borderInlineStartWidth","borderInlineStyle","borderInlineWidth","borderLeft","borderLeftColor","borderLeftStyle","borderLeftWidth","borderRadius","borderRight","borderRightColor","borderRightStyle","borderRightWidth","borderSpacing","borderStartEndRadius","borderStartStartRadius","borderStyle","borderTop","borderTopColor","borderTopLeftRadius","borderTopRightRadius","borderTopStyle","borderTopWidth","borderWidth","bottom","bottomMargin","bound","boundElements","boundingClientRect","boundingHeight","boundingLeft","boundingTop","boundingWidth","bounds","boundsGeometry","box-decoration-break","box-shadow","box-sizing","boxDecorationBreak","boxShadow","boxSizing","brand","brands","break-after","break-before","break-inside","breakAfter","breakBefore","breakInside","broadcast","browserLanguage","btoa","bubbles","buffer","bufferData","bufferDepth","bufferSize","bufferSubData","buffered","bufferedAmount","bufferedAmountLowThreshold","buildID","buildNumber","button","buttonID","buttons","byteLength","byteOffset","bytesWritten","c","cache","caches","call","caller","canBeFormatted","canBeMounted","canBeShared","canHaveChildren","canHaveHTML","canInsertDTMF","canMakePayment","canPlayType","canPresent","canTrickleIceCandidates","cancel","cancelAndHoldAtTime","cancelAnimationFrame","cancelBubble","cancelIdleCallback","cancelScheduledValues","cancelVideoFrameCallback","cancelWatchAvailability","cancelable","candidate","canonicalUUID","canvas","capabilities","caption","caption-side","captionSide","capture","captureEvents","captureStackTrace","captureStream","caret-color","caretBidiLevel","caretColor","caretPositionFromPoint","caretRangeFromPoint","cast","catch","category","cbrt","cd","ceil","cellIndex","cellPadding","cellSpacing","cells","ch","chOff","chain","challenge","changeType","changedTouches","channel","channelCount","channelCountMode","channelInterpretation","char","charAt","charCode","charCodeAt","charIndex","charLength","characterData","characterDataOldValue","characterSet","characteristic","charging","chargingTime","charset","check","checkEnclosure","checkFramebufferStatus","checkIntersection","checkValidity","checked","childElementCount","childList","childNodes","children","chrome","ciphertext","cite","city","claimInterface","claimed","classList","className","classid","clear","clearAppBadge","clearAttributes","clearBufferfi","clearBufferfv","clearBufferiv","clearBufferuiv","clearColor","clearData","clearDepth","clearHalt","clearImmediate","clearInterval","clearLiveSeekableRange","clearMarks","clearMaxGCPauseAccumulator","clearMeasures","clearParameters","clearRect","clearResourceTimings","clearShadow","clearStencil","clearTimeout","clearWatch","click","clickCount","clientDataJSON","clientHeight","clientInformation","clientLeft","clientRect","clientRects","clientTop","clientWaitSync","clientWidth","clientX","clientY","clip","clip-path","clip-rule","clipBottom","clipLeft","clipPath","clipPathUnits","clipRight","clipRule","clipTop","clipboard","clipboardData","clone","cloneContents","cloneNode","cloneRange","close","closePath","closed","closest","clz","clz32","cm","cmp","code","codeBase","codePointAt","codeType","colSpan","collapse","collapseToEnd","collapseToStart","collapsed","collect","colno","color","color-adjust","color-interpolation","color-interpolation-filters","colorAdjust","colorDepth","colorInterpolation","colorInterpolationFilters","colorMask","colorType","cols","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columnCount","columnFill","columnGap","columnNumber","columnRule","columnRuleColor","columnRuleStyle","columnRuleWidth","columnSpan","columnWidth","columns","command","commit","commitPreferences","commitStyles","commonAncestorContainer","compact","compareBoundaryPoints","compareDocumentPosition","compareEndPoints","compareExchange","compareNode","comparePoint","compatMode","compatible","compile","compileShader","compileStreaming","complete","component","componentFromPoint","composed","composedPath","composite","compositionEndOffset","compositionStartOffset","compressedTexImage2D","compressedTexImage3D","compressedTexSubImage2D","compressedTexSubImage3D","computedStyleMap","concat","conditionText","coneInnerAngle","coneOuterAngle","coneOuterGain","configuration","configurationName","configurationValue","configurations","confirm","confirmComposition","confirmSiteSpecificTrackingException","confirmWebWideTrackingException","connect","connectEnd","connectShark","connectStart","connected","connection","connectionList","connectionSpeed","connectionState","connections","console","consolidate","constraint","constrictionActive","construct","constructor","contactID","contain","containerId","containerName","containerSrc","containerType","contains","containsNode","content","contentBoxSize","contentDocument","contentEditable","contentHint","contentOverflow","contentRect","contentScriptType","contentStyleType","contentType","contentWindow","context","contextMenu","contextmenu","continue","continuePrimaryKey","continuous","control","controlTransferIn","controlTransferOut","controller","controls","controlsList","convertPointFromNode","convertQuadFromNode","convertRectFromNode","convertToBlob","convertToSpecifiedUnits","cookie","cookieEnabled","coords","copyBufferSubData","copyFromChannel","copyTexImage2D","copyTexSubImage2D","copyTexSubImage3D","copyToChannel","copyWithin","correspondingElement","correspondingUseElement","corruptedVideoFrames","cos","cosh","count","countReset","counter-increment","counter-reset","counter-set","counterIncrement","counterReset","counterSet","country","cpuClass","cpuSleepAllowed","create","createAnalyser","createAnswer","createAttribute","createAttributeNS","createBiquadFilter","createBuffer","createBufferSource","createCDATASection","createCSSStyleSheet","createCaption","createChannelMerger","createChannelSplitter","createComment","createConstantSource","createContextualFragment","createControlRange","createConvolver","createDTMFSender","createDataChannel","createDelay","createDelayNode","createDocument","createDocumentFragment","createDocumentType","createDynamicsCompressor","createElement","createElementNS","createEntityReference","createEvent","createEventObject","createExpression","createFramebuffer","createFunction","createGain","createGainNode","createHTML","createHTMLDocument","createIIRFilter","createImageBitmap","createImageData","createIndex","createJavaScriptNode","createLinearGradient","createMediaElementSource","createMediaKeys","createMediaStreamDestination","createMediaStreamSource","createMediaStreamTrackSource","createMutableFile","createNSResolver","createNodeIterator","createNotification","createObjectStore","createObjectURL","createOffer","createOscillator","createPanner","createPattern","createPeriodicWave","createPolicy","createPopup","createProcessingInstruction","createProgram","createQuery","createRadialGradient","createRange","createRangeCollection","createReader","createRenderbuffer","createSVGAngle","createSVGLength","createSVGMatrix","createSVGNumber","createSVGPathSegArcAbs","createSVGPathSegArcRel","createSVGPathSegClosePath","createSVGPathSegCurvetoCubicAbs","createSVGPathSegCurvetoCubicRel","createSVGPathSegCurvetoCubicSmoothAbs","createSVGPathSegCurvetoCubicSmoothRel","createSVGPathSegCurvetoQuadraticAbs","createSVGPathSegCurvetoQuadraticRel","createSVGPathSegCurvetoQuadraticSmoothAbs","createSVGPathSegCurvetoQuadraticSmoothRel","createSVGPathSegLinetoAbs","createSVGPathSegLinetoHorizontalAbs","createSVGPathSegLinetoHorizontalRel","createSVGPathSegLinetoRel","createSVGPathSegLinetoVerticalAbs","createSVGPathSegLinetoVerticalRel","createSVGPathSegMovetoAbs","createSVGPathSegMovetoRel","createSVGPoint","createSVGRect","createSVGTransform","createSVGTransformFromMatrix","createSampler","createScript","createScriptProcessor","createScriptURL","createSession","createShader","createShadowRoot","createStereoPanner","createStyleSheet","createTBody","createTFoot","createTHead","createTextNode","createTextRange","createTexture","createTouch","createTouchList","createTransformFeedback","createTreeWalker","createVertexArray","createWaveShaper","creationTime","credentials","crossOrigin","crossOriginIsolated","crypto","csi","csp","cssFloat","cssRules","cssText","cssValueType","ctrlKey","ctrlLeft","cues","cullFace","currentDirection","currentLocalDescription","currentNode","currentPage","currentRect","currentRemoteDescription","currentScale","currentScript","currentSrc","currentState","currentStyle","currentTarget","currentTime","currentTranslate","currentView","cursor","curve","customElements","customError","cx","cy","d","data","dataFld","dataFormatAs","dataLoss","dataLossMessage","dataPageSize","dataSrc","dataTransfer","database","databases","dataset","dateTime","db","debug","debuggerEnabled","declare","decode","decodeAudioData","decodeURI","decodeURIComponent","decodedBodySize","decoding","decodingInfo","decrypt","default","defaultCharset","defaultChecked","defaultMuted","defaultPlaybackRate","defaultPolicy","defaultPrevented","defaultRequest","defaultSelected","defaultStatus","defaultURL","defaultValue","defaultView","defaultstatus","defer","define","defineMagicFunction","defineMagicVariable","defineProperties","defineProperty","deg","delay","delayTime","delegatesFocus","delete","deleteBuffer","deleteCaption","deleteCell","deleteContents","deleteData","deleteDatabase","deleteFramebuffer","deleteFromDocument","deleteIndex","deleteMedium","deleteObjectStore","deleteProgram","deleteProperty","deleteQuery","deleteRenderbuffer","deleteRow","deleteRule","deleteSampler","deleteShader","deleteSync","deleteTFoot","deleteTHead","deleteTexture","deleteTransformFeedback","deleteVertexArray","deliverChangeRecords","delivery","deliveryInfo","deliveryStatus","deliveryTimestamp","delta","deltaMode","deltaX","deltaY","deltaZ","dependentLocality","depthFar","depthFunc","depthMask","depthNear","depthRange","deref","deriveBits","deriveKey","description","deselectAll","designMode","desiredSize","destination","destinationURL","detach","detachEvent","detachShader","detail","details","detect","detune","device","deviceClass","deviceId","deviceMemory","devicePixelContentBoxSize","devicePixelRatio","deviceProtocol","deviceSubclass","deviceVersionMajor","deviceVersionMinor","deviceVersionSubminor","deviceXDPI","deviceYDPI","didTimeout","diffuseConstant","digest","dimensions","dir","dirName","direction","dirxml","disable","disablePictureInPicture","disableRemotePlayback","disableVertexAttribArray","disabled","dischargingTime","disconnect","disconnectShark","dispatchEvent","display","displayId","displayName","disposition","distanceModel","div","divisor","djsapi","djsproxy","doImport","doNotTrack","doScroll","doctype","document","documentElement","documentMode","documentURI","dolphin","dolphinGameCenter","dolphininfo","dolphinmeta","domComplete","domContentLoadedEventEnd","domContentLoadedEventStart","domInteractive","domLoading","domOverlayState","domain","domainLookupEnd","domainLookupStart","dominant-baseline","dominantBaseline","done","dopplerFactor","dotAll","downDegrees","downlink","download","downloadTotal","downloaded","dpcm","dpi","dppx","dragDrop","draggable","drawArrays","drawArraysInstanced","drawArraysInstancedANGLE","drawBuffers","drawCustomFocusRing","drawElements","drawElementsInstanced","drawElementsInstancedANGLE","drawFocusIfNeeded","drawImage","drawImageFromRect","drawRangeElements","drawSystemFocusRing","drawingBufferHeight","drawingBufferWidth","dropEffect","droppedVideoFrames","dropzone","dtmf","dump","dumpProfile","duplicate","durability","duration","dvname","dvnum","dx","dy","dynsrc","e","edgeMode","effect","effectAllowed","effectiveDirective","effectiveType","elapsedTime","element","elementFromPoint","elementTiming","elements","elementsFromPoint","elevation","ellipse","em","email","embeds","emma","empty","empty-cells","emptyCells","emptyHTML","emptyScript","emulatedPosition","enable","enableBackground","enableDelegations","enableStyleSheetsForSet","enableVertexAttribArray","enabled","enabledPlugin","encode","encodeInto","encodeURI","encodeURIComponent","encodedBodySize","encoding","encodingInfo","encrypt","enctype","end","endContainer","endElement","endElementAt","endOfStream","endOffset","endQuery","endTime","endTransformFeedback","ended","endpoint","endpointNumber","endpoints","endsWith","enterKeyHint","entities","entries","entryType","enumerate","enumerateDevices","enumerateEditable","environmentBlendMode","equals","error","errorCode","errorDetail","errorText","escape","estimate","eval","evaluate","event","eventPhase","every","ex","exception","exchange","exec","execCommand","execCommandShowHelp","execScript","exitFullscreen","exitPictureInPicture","exitPointerLock","exitPresent","exp","expand","expandEntityReferences","expando","expansion","expiration","expirationTime","expires","expiryDate","explicitOriginalTarget","expm1","exponent","exponentialRampToValueAtTime","exportKey","exports","extend","extensions","extentNode","extentOffset","external","externalResourcesRequired","extractContents","extractable","eye","f","face","factoryReset","failureReason","fallback","family","familyName","farthestViewportElement","fastSeek","fatal","featureId","featurePolicy","featureSettings","features","fenceSync","fetch","fetchStart","fftSize","fgColor","fieldOfView","file","fileCreatedDate","fileHandle","fileModifiedDate","fileName","fileSize","fileUpdatedDate","filename","files","filesystem","fill","fill-opacity","fill-rule","fillLightMode","fillOpacity","fillRect","fillRule","fillStyle","fillText","filter","filterResX","filterResY","filterUnits","filters","finally","find","findIndex","findRule","findText","finish","finished","fireEvent","firesTouchEvents","firstChild","firstElementChild","firstPage","fixed","flags","flat","flatMap","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","flexBasis","flexDirection","flexFlow","flexGrow","flexShrink","flexWrap","flipX","flipY","float","float32","float64","flood-color","flood-opacity","floodColor","floodOpacity","floor","flush","focus","focusNode","focusOffset","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","fontFamily","fontFeatureSettings","fontKerning","fontLanguageOverride","fontOpticalSizing","fontSize","fontSizeAdjust","fontSmoothingEnabled","fontStretch","fontStyle","fontSynthesis","fontVariant","fontVariantAlternates","fontVariantCaps","fontVariantEastAsian","fontVariantLigatures","fontVariantNumeric","fontVariantPosition","fontVariationSettings","fontWeight","fontcolor","fontfaces","fonts","fontsize","for","forEach","force","forceRedraw","form","formAction","formData","formEnctype","formMethod","formNoValidate","formTarget","format","formatToParts","forms","forward","forwardX","forwardY","forwardZ","foundation","fr","fragmentDirective","frame","frameBorder","frameElement","frameSpacing","framebuffer","framebufferHeight","framebufferRenderbuffer","framebufferTexture2D","framebufferTextureLayer","framebufferWidth","frames","freeSpace","freeze","frequency","frequencyBinCount","from","fromCharCode","fromCodePoint","fromElement","fromEntries","fromFloat32Array","fromFloat64Array","fromMatrix","fromPoint","fromQuad","fromRect","frontFace","fround","fullPath","fullScreen","fullVersionList","fullscreen","fullscreenElement","fullscreenEnabled","fx","fy","gain","gamepad","gamma","gap","gatheringState","gatt","genderIdentity","generateCertificate","generateKey","generateMipmap","generateRequest","geolocation","gestureObject","get","getActiveAttrib","getActiveUniform","getActiveUniformBlockName","getActiveUniformBlockParameter","getActiveUniforms","getAdjacentText","getAll","getAllKeys","getAllResponseHeaders","getAllowlistForFeature","getAnimations","getAsFile","getAsString","getAttachedShaders","getAttribLocation","getAttribute","getAttributeNS","getAttributeNames","getAttributeNode","getAttributeNodeNS","getAttributeType","getAudioTracks","getAvailability","getBBox","getBattery","getBigInt64","getBigUint64","getBlob","getBookmark","getBoundingClientRect","getBounds","getBoxQuads","getBufferParameter","getBufferSubData","getByteFrequencyData","getByteTimeDomainData","getCSSCanvasContext","getCTM","getCandidateWindowClientRect","getCanonicalLocales","getCapabilities","getChannelData","getCharNumAtPosition","getCharacteristic","getCharacteristics","getClientExtensionResults","getClientRect","getClientRects","getCoalescedEvents","getCompositionAlternatives","getComputedStyle","getComputedTextLength","getComputedTiming","getConfiguration","getConstraints","getContext","getContextAttributes","getContributingSources","getCounterValue","getCueAsHTML","getCueById","getCurrentPosition","getCurrentTime","getData","getDatabaseNames","getDate","getDay","getDefaultComputedStyle","getDescriptor","getDescriptors","getDestinationInsertionPoints","getDevices","getDirectory","getDisplayMedia","getDistributedNodes","getEditable","getElementById","getElementsByClassName","getElementsByName","getElementsByTagName","getElementsByTagNameNS","getEnclosureList","getEndPositionOfChar","getEntries","getEntriesByName","getEntriesByType","getError","getExtension","getExtentOfChar","getEyeParameters","getFeature","getFile","getFiles","getFilesAndDirectories","getFingerprints","getFloat32","getFloat64","getFloatFrequencyData","getFloatTimeDomainData","getFloatValue","getFragDataLocation","getFrameData","getFramebufferAttachmentParameter","getFrequencyResponse","getFullYear","getGamepads","getHighEntropyValues","getHitTestResults","getHitTestResultsForTransientInput","getHours","getIdentityAssertion","getIds","getImageData","getIndexedParameter","getInstalledRelatedApps","getInt16","getInt32","getInt8","getInternalformatParameter","getIntersectionList","getItem","getItems","getKey","getKeyframes","getLayers","getLayoutMap","getLineDash","getLocalCandidates","getLocalParameters","getLocalStreams","getMarks","getMatchedCSSRules","getMaxGCPauseSinceClear","getMeasures","getMetadata","getMilliseconds","getMinutes","getModifierState","getMonth","getNamedItem","getNamedItemNS","getNativeFramebufferScaleFactor","getNotifications","getNotifier","getNumberOfChars","getOffsetReferenceSpace","getOutputTimestamp","getOverrideHistoryNavigationMode","getOverrideStyle","getOwnPropertyDescriptor","getOwnPropertyDescriptors","getOwnPropertyNames","getOwnPropertySymbols","getParameter","getParameters","getParent","getPathSegAtLength","getPhotoCapabilities","getPhotoSettings","getPointAtLength","getPose","getPredictedEvents","getPreference","getPreferenceDefault","getPresentationAttribute","getPreventDefault","getPrimaryService","getPrimaryServices","getProgramInfoLog","getProgramParameter","getPropertyCSSValue","getPropertyPriority","getPropertyShorthand","getPropertyType","getPropertyValue","getPrototypeOf","getQuery","getQueryParameter","getRGBColorValue","getRandomValues","getRangeAt","getReader","getReceivers","getRectValue","getRegistration","getRegistrations","getRemoteCandidates","getRemoteCertificates","getRemoteParameters","getRemoteStreams","getRenderbufferParameter","getResponseHeader","getRoot","getRootNode","getRotationOfChar","getSVGDocument","getSamplerParameter","getScreenCTM","getSeconds","getSelectedCandidatePair","getSelection","getSenders","getService","getSettings","getShaderInfoLog","getShaderParameter","getShaderPrecisionFormat","getShaderSource","getSimpleDuration","getSiteIcons","getSources","getSpeculativeParserUrls","getStartPositionOfChar","getStartTime","getState","getStats","getStatusForPolicy","getStorageUpdates","getStreamById","getStringValue","getSubStringLength","getSubscription","getSupportedConstraints","getSupportedExtensions","getSupportedFormats","getSyncParameter","getSynchronizationSources","getTags","getTargetRanges","getTexParameter","getTime","getTimezoneOffset","getTiming","getTotalLength","getTrackById","getTracks","getTransceivers","getTransform","getTransformFeedbackVarying","getTransformToElement","getTransports","getType","getTypeMapping","getUTCDate","getUTCDay","getUTCFullYear","getUTCHours","getUTCMilliseconds","getUTCMinutes","getUTCMonth","getUTCSeconds","getUint16","getUint32","getUint8","getUniform","getUniformBlockIndex","getUniformIndices","getUniformLocation","getUserMedia","getVRDisplays","getValues","getVarDate","getVariableValue","getVertexAttrib","getVertexAttribOffset","getVideoPlaybackQuality","getVideoTracks","getViewerPose","getViewport","getVoices","getWakeLockState","getWriter","getYear","givenName","global","globalAlpha","globalCompositeOperation","globalThis","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","go","grabFrame","grad","gradientTransform","gradientUnits","grammars","green","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","gridArea","gridAutoColumns","gridAutoFlow","gridAutoRows","gridColumn","gridColumnEnd","gridColumnGap","gridColumnStart","gridGap","gridRow","gridRowEnd","gridRowGap","gridRowStart","gridTemplate","gridTemplateAreas","gridTemplateColumns","gridTemplateRows","gripSpace","group","groupCollapsed","groupEnd","groupId","hadRecentInput","hand","handedness","hapticActuators","hardwareConcurrency","has","hasAttribute","hasAttributeNS","hasAttributes","hasBeenActive","hasChildNodes","hasComposition","hasEnrolledInstrument","hasExtension","hasExternalDisplay","hasFeature","hasFocus","hasInstance","hasLayout","hasOrientation","hasOwnProperty","hasPointerCapture","hasPosition","hasReading","hasStorageAccess","hash","head","headers","heading","height","hidden","hide","hideFocus","high","highWaterMark","hint","history","honorificPrefix","honorificSuffix","horizontalOverflow","host","hostCandidate","hostname","href","hrefTranslate","hreflang","hspace","html5TagCheckInerface","htmlFor","htmlText","httpEquiv","httpRequestStatusCode","hwTimestamp","hyphens","hypot","iccId","iceConnectionState","iceGatheringState","iceTransport","icon","iconURL","id","identifier","identity","idpLoginUrl","ignoreBOM","ignoreCase","ignoreDepthValues","image-orientation","image-rendering","imageHeight","imageOrientation","imageRendering","imageSizes","imageSmoothingEnabled","imageSmoothingQuality","imageSrcset","imageWidth","images","ime-mode","imeMode","implementation","importKey","importNode","importStylesheet","imports","impp","imul","in","in1","in2","inBandMetadataTrackDispatchType","inRange","includes","incremental","indeterminate","index","indexNames","indexOf","indexedDB","indicate","inertiaDestinationX","inertiaDestinationY","info","init","initAnimationEvent","initBeforeLoadEvent","initClipboardEvent","initCloseEvent","initCommandEvent","initCompositionEvent","initCustomEvent","initData","initDataType","initDeviceMotionEvent","initDeviceOrientationEvent","initDragEvent","initErrorEvent","initEvent","initFocusEvent","initGestureEvent","initHashChangeEvent","initKeyEvent","initKeyboardEvent","initMSManipulationEvent","initMessageEvent","initMouseEvent","initMouseScrollEvent","initMouseWheelEvent","initMutationEvent","initNSMouseEvent","initOverflowEvent","initPageEvent","initPageTransitionEvent","initPointerEvent","initPopStateEvent","initProgressEvent","initScrollAreaEvent","initSimpleGestureEvent","initStorageEvent","initTextEvent","initTimeEvent","initTouchEvent","initTransitionEvent","initUIEvent","initWebKitAnimationEvent","initWebKitTransitionEvent","initWebKitWheelEvent","initWheelEvent","initialTime","initialize","initiatorType","inline-size","inlineSize","inlineVerticalFieldOfView","inner","innerHTML","innerHeight","innerText","innerWidth","input","inputBuffer","inputEncoding","inputMethod","inputMode","inputSource","inputSources","inputType","inputs","insertAdjacentElement","insertAdjacentHTML","insertAdjacentText","insertBefore","insertCell","insertDTMF","insertData","insertItemBefore","insertNode","insertRow","insertRule","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","insetBlock","insetBlockEnd","insetBlockStart","insetInline","insetInlineEnd","insetInlineStart","installing","instanceRoot","instantiate","instantiateStreaming","instruments","int16","int32","int8","integrity","interactionMode","intercept","interfaceClass","interfaceName","interfaceNumber","interfaceProtocol","interfaceSubclass","interfaces","interimResults","internalSubset","interpretation","intersectionRatio","intersectionRect","intersectsNode","interval","invalidIteratorState","invalidateFramebuffer","invalidateSubFramebuffer","inverse","invertSelf","is","is2D","isActive","isAlternate","isArray","isBingCurrentSearchDefault","isBuffer","isCandidateWindowVisible","isChar","isCollapsed","isComposing","isConcatSpreadable","isConnected","isContentEditable","isContentHandlerRegistered","isContextLost","isDefaultNamespace","isDirectory","isDisabled","isEnabled","isEqual","isEqualNode","isExtensible","isExternalCTAP2SecurityKeySupported","isFile","isFinite","isFramebuffer","isFrozen","isGenerator","isHTML","isHistoryNavigation","isId","isIdentity","isInjected","isInteger","isIntersecting","isLockFree","isMap","isMultiLine","isNaN","isOpen","isPointInFill","isPointInPath","isPointInRange","isPointInStroke","isPrefAlternate","isPresenting","isPrimary","isProgram","isPropertyImplicit","isProtocolHandlerRegistered","isPrototypeOf","isQuery","isRenderbuffer","isSafeInteger","isSameNode","isSampler","isScript","isScriptURL","isSealed","isSecureContext","isSessionSupported","isShader","isSupported","isSync","isTextEdit","isTexture","isTransformFeedback","isTrusted","isTypeSupported","isUserVerifyingPlatformAuthenticatorAvailable","isVertexArray","isView","isVisible","isochronousTransferIn","isochronousTransferOut","isolation","italics","item","itemId","itemProp","itemRef","itemScope","itemType","itemValue","items","iterateNext","iterationComposite","iterator","javaEnabled","jobTitle","join","json","justify-content","justify-items","justify-self","justifyContent","justifyItems","justifySelf","k1","k2","k3","k4","kHz","keepalive","kernelMatrix","kernelUnitLengthX","kernelUnitLengthY","kerning","key","keyCode","keyFor","keyIdentifier","keyLightEnabled","keyLocation","keyPath","keyStatuses","keySystem","keyText","keyUsage","keyboard","keys","keytype","kind","knee","label","labels","lang","language","languages","largeArcFlag","lastChild","lastElementChild","lastEventId","lastIndex","lastIndexOf","lastInputTime","lastMatch","lastMessageSubject","lastMessageType","lastModified","lastModifiedDate","lastPage","lastParen","lastState","lastStyleSheetSet","latitude","layerX","layerY","layoutFlow","layoutGrid","layoutGridChar","layoutGridLine","layoutGridMode","layoutGridType","lbound","left","leftContext","leftDegrees","leftMargin","leftProjectionMatrix","leftViewMatrix","length","lengthAdjust","lengthComputable","letter-spacing","letterSpacing","level","lighting-color","lightingColor","limitingConeAngle","line","line-break","line-height","lineAlign","lineBreak","lineCap","lineDashOffset","lineHeight","lineJoin","lineNumber","lineTo","lineWidth","linearAcceleration","linearRampToValueAtTime","linearVelocity","lineno","lines","link","linkColor","linkProgram","links","list","list-style","list-style-image","list-style-position","list-style-type","listStyle","listStyleImage","listStylePosition","listStyleType","listener","load","loadEventEnd","loadEventStart","loadTime","loadTimes","loaded","loading","localDescription","localName","localService","localStorage","locale","localeCompare","location","locationbar","lock","locked","lockedFile","locks","log","log10","log1p","log2","logicalXDPI","logicalYDPI","longDesc","longitude","lookupNamespaceURI","lookupPrefix","loop","loopEnd","loopStart","looping","low","lower","lowerBound","lowerOpen","lowsrc","m11","m12","m13","m14","m21","m22","m23","m24","m31","m32","m33","m34","m41","m42","m43","m44","makeXRCompatible","manifest","manufacturer","manufacturerName","map","mapping","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","marginBlock","marginBlockEnd","marginBlockStart","marginBottom","marginHeight","marginInline","marginInlineEnd","marginInlineStart","marginLeft","marginRight","marginTop","marginWidth","mark","marker","marker-end","marker-mid","marker-offset","marker-start","markerEnd","markerHeight","markerMid","markerOffset","markerStart","markerUnits","markerWidth","marks","mask","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-position-x","mask-position-y","mask-repeat","mask-size","mask-type","maskClip","maskComposite","maskContentUnits","maskImage","maskMode","maskOrigin","maskPosition","maskPositionX","maskPositionY","maskRepeat","maskSize","maskType","maskUnits","match","matchAll","matchMedia","matchMedium","matches","matrix","matrixTransform","max","max-block-size","max-height","max-inline-size","max-width","maxActions","maxAlternatives","maxBlockSize","maxChannelCount","maxChannels","maxConnectionsPerServer","maxDecibels","maxDistance","maxHeight","maxInlineSize","maxLayers","maxLength","maxMessageSize","maxPacketLifeTime","maxRetransmits","maxTouchPoints","maxValue","maxWidth","measure","measureText","media","mediaCapabilities","mediaDevices","mediaElement","mediaGroup","mediaKeys","mediaSession","mediaStream","mediaText","meetOrSlice","memory","menubar","mergeAttributes","message","messageClass","messageHandlers","messageType","metaKey","metadata","method","methodDetails","methodName","mid","mimeType","mimeTypes","min","min-block-size","min-height","min-inline-size","min-width","minBlockSize","minDecibels","minHeight","minInlineSize","minLength","minValue","minWidth","miterLimit","mix-blend-mode","mixBlendMode","mm","mobile","mode","model","modify","mount","move","moveBy","moveEnd","moveFirst","moveFocusDown","moveFocusLeft","moveFocusRight","moveFocusUp","moveNext","moveRow","moveStart","moveTo","moveToBookmark","moveToElementText","moveToPoint","movementX","movementY","mozAdd","mozAnimationStartTime","mozAnon","mozApps","mozAudioCaptured","mozAudioChannelType","mozAutoplayEnabled","mozCancelAnimationFrame","mozCancelFullScreen","mozCancelRequestAnimationFrame","mozCaptureStream","mozCaptureStreamUntilEnded","mozClearDataAt","mozContact","mozContacts","mozCreateFileHandle","mozCurrentTransform","mozCurrentTransformInverse","mozCursor","mozDash","mozDashOffset","mozDecodedFrames","mozExitPointerLock","mozFillRule","mozFragmentEnd","mozFrameDelay","mozFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozGetAll","mozGetAllKeys","mozGetAsFile","mozGetDataAt","mozGetMetadata","mozGetUserMedia","mozHasAudio","mozHasItem","mozHidden","mozImageSmoothingEnabled","mozIndexedDB","mozInnerScreenX","mozInnerScreenY","mozInputSource","mozIsTextField","mozItem","mozItemCount","mozItems","mozLength","mozLockOrientation","mozMatchesSelector","mozMovementX","mozMovementY","mozOpaque","mozOrientation","mozPaintCount","mozPaintedFrames","mozParsedFrames","mozPay","mozPointerLockElement","mozPresentedFrames","mozPreservesPitch","mozPressure","mozPrintCallback","mozRTCIceCandidate","mozRTCPeerConnection","mozRTCSessionDescription","mozRemove","mozRequestAnimationFrame","mozRequestFullScreen","mozRequestPointerLock","mozSetDataAt","mozSetImageElement","mozSourceNode","mozSrcObject","mozSystem","mozTCPSocket","mozTextStyle","mozTypesAt","mozUnlockOrientation","mozUserCancelled","mozVisibilityState","ms","msAnimation","msAnimationDelay","msAnimationDirection","msAnimationDuration","msAnimationFillMode","msAnimationIterationCount","msAnimationName","msAnimationPlayState","msAnimationStartTime","msAnimationTimingFunction","msBackfaceVisibility","msBlockProgression","msCSSOMElementFloatMetrics","msCaching","msCachingEnabled","msCancelRequestAnimationFrame","msCapsLockWarningOff","msClearImmediate","msClose","msContentZoomChaining","msContentZoomFactor","msContentZoomLimit","msContentZoomLimitMax","msContentZoomLimitMin","msContentZoomSnap","msContentZoomSnapPoints","msContentZoomSnapType","msContentZooming","msConvertURL","msCrypto","msDoNotTrack","msElementsFromPoint","msElementsFromRect","msExitFullscreen","msExtendedCode","msFillRule","msFirstPaint","msFlex","msFlexAlign","msFlexDirection","msFlexFlow","msFlexItemAlign","msFlexLinePack","msFlexNegative","msFlexOrder","msFlexPack","msFlexPositive","msFlexPreferredSize","msFlexWrap","msFlowFrom","msFlowInto","msFontFeatureSettings","msFullscreenElement","msFullscreenEnabled","msGetInputContext","msGetRegionContent","msGetUntransformedBounds","msGraphicsTrustStatus","msGridColumn","msGridColumnAlign","msGridColumnSpan","msGridColumns","msGridRow","msGridRowAlign","msGridRowSpan","msGridRows","msHidden","msHighContrastAdjust","msHyphenateLimitChars","msHyphenateLimitLines","msHyphenateLimitZone","msHyphens","msImageSmoothingEnabled","msImeAlign","msIndexedDB","msInterpolationMode","msIsStaticHTML","msKeySystem","msKeys","msLaunchUri","msLockOrientation","msManipulationViewsEnabled","msMatchMedia","msMatchesSelector","msMaxTouchPoints","msOrientation","msOverflowStyle","msPerspective","msPerspectiveOrigin","msPlayToDisabled","msPlayToPreferredSourceUri","msPlayToPrimary","msPointerEnabled","msRegionOverflow","msReleasePointerCapture","msRequestAnimationFrame","msRequestFullscreen","msSaveBlob","msSaveOrOpenBlob","msScrollChaining","msScrollLimit","msScrollLimitXMax","msScrollLimitXMin","msScrollLimitYMax","msScrollLimitYMin","msScrollRails","msScrollSnapPointsX","msScrollSnapPointsY","msScrollSnapType","msScrollSnapX","msScrollSnapY","msScrollTranslation","msSetImmediate","msSetMediaKeys","msSetPointerCapture","msTextCombineHorizontal","msTextSizeAdjust","msToBlob","msTouchAction","msTouchSelect","msTraceAsyncCallbackCompleted","msTraceAsyncCallbackStarting","msTraceAsyncOperationCompleted","msTraceAsyncOperationStarting","msTransform","msTransformOrigin","msTransformStyle","msTransition","msTransitionDelay","msTransitionDuration","msTransitionProperty","msTransitionTimingFunction","msUnlockOrientation","msUpdateAsyncCallbackRelation","msUserSelect","msVisibilityState","msWrapFlow","msWrapMargin","msWrapThrough","msWriteProfilerMark","msZoom","msZoomTo","mt","mul","multiEntry","multiSelectionObj","multiline","multiple","multiply","multiplySelf","mutableFile","muted","n","name","nameProp","namedItem","namedRecordset","names","namespaceURI","namespaces","naturalHeight","naturalWidth","navigate","navigation","navigationMode","navigationPreload","navigationStart","navigator","near","nearestViewportElement","negative","negotiated","netscape","networkState","newScale","newTranslate","newURL","newValue","newValueSpecifiedUnits","newVersion","newhome","next","nextElementSibling","nextHopProtocol","nextNode","nextPage","nextSibling","nickname","noHref","noModule","noResize","noShade","noValidate","noWrap","node","nodeName","nodeType","nodeValue","nonce","normalize","normalizedPathSegList","notationName","notations","note","noteGrainOn","noteOff","noteOn","notify","now","numOctaves","number","numberOfChannels","numberOfInputs","numberOfItems","numberOfOutputs","numberValue","oMatchesSelector","object","object-fit","object-position","objectFit","objectPosition","objectStore","objectStoreNames","objectType","observe","of","offscreenBuffering","offset","offset-anchor","offset-distance","offset-path","offset-rotate","offsetAnchor","offsetDistance","offsetHeight","offsetLeft","offsetNode","offsetParent","offsetPath","offsetRotate","offsetTop","offsetWidth","offsetX","offsetY","ok","oldURL","oldValue","oldVersion","olderShadowRoot","onLine","onabort","onabsolutedeviceorientation","onactivate","onactive","onaddsourcebuffer","onaddstream","onaddtrack","onafterprint","onafterscriptexecute","onafterupdate","onanimationcancel","onanimationend","onanimationiteration","onanimationstart","onappinstalled","onaudioend","onaudioprocess","onaudiostart","onautocomplete","onautocompleteerror","onauxclick","onbeforeactivate","onbeforecopy","onbeforecut","onbeforedeactivate","onbeforeeditfocus","onbeforeinstallprompt","onbeforepaste","onbeforeprint","onbeforescriptexecute","onbeforeunload","onbeforeupdate","onbeforexrselect","onbegin","onblocked","onblur","onbounce","onboundary","onbufferedamountlow","oncached","oncancel","oncandidatewindowhide","oncandidatewindowshow","oncandidatewindowupdate","oncanplay","oncanplaythrough","once","oncellchange","onchange","oncharacteristicvaluechanged","onchargingchange","onchargingtimechange","onchecking","onclick","onclose","onclosing","oncompassneedscalibration","oncomplete","onconnect","onconnecting","onconnectionavailable","onconnectionstatechange","oncontextmenu","oncontrollerchange","oncontrolselect","oncopy","oncuechange","oncut","ondataavailable","ondatachannel","ondatasetchanged","ondatasetcomplete","ondblclick","ondeactivate","ondevicechange","ondevicelight","ondevicemotion","ondeviceorientation","ondeviceorientationabsolute","ondeviceproximity","ondischargingtimechange","ondisconnect","ondisplay","ondownloading","ondrag","ondragend","ondragenter","ondragexit","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onencrypted","onend","onended","onenter","onenterpictureinpicture","onerror","onerrorupdate","onexit","onfilterchange","onfinish","onfocus","onfocusin","onfocusout","onformdata","onfreeze","onfullscreenchange","onfullscreenerror","ongatheringstatechange","ongattserverdisconnected","ongesturechange","ongestureend","ongesturestart","ongotpointercapture","onhashchange","onhelp","onicecandidate","onicecandidateerror","oniceconnectionstatechange","onicegatheringstatechange","oninactive","oninput","oninputsourceschange","oninvalid","onkeydown","onkeypress","onkeystatuseschange","onkeyup","onlanguagechange","onlayoutcomplete","onleavepictureinpicture","onlevelchange","onload","onloadeddata","onloadedmetadata","onloadend","onloading","onloadingdone","onloadingerror","onloadstart","onlosecapture","onlostpointercapture","only","onmark","onmessage","onmessageerror","onmidimessage","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onmousewheel","onmove","onmoveend","onmovestart","onmozfullscreenchange","onmozfullscreenerror","onmozorientationchange","onmozpointerlockchange","onmozpointerlockerror","onmscontentzoom","onmsfullscreenchange","onmsfullscreenerror","onmsgesturechange","onmsgesturedoubletap","onmsgestureend","onmsgesturehold","onmsgesturestart","onmsgesturetap","onmsgotpointercapture","onmsinertiastart","onmslostpointercapture","onmsmanipulationstatechanged","onmsneedkey","onmsorientationchange","onmspointercancel","onmspointerdown","onmspointerenter","onmspointerhover","onmspointerleave","onmspointermove","onmspointerout","onmspointerover","onmspointerup","onmssitemodejumplistitemremoved","onmsthumbnailclick","onmute","onnegotiationneeded","onnomatch","onnoupdate","onobsolete","onoffline","ononline","onopen","onorientationchange","onpagechange","onpagehide","onpageshow","onpaste","onpause","onpayerdetailchange","onpaymentmethodchange","onplay","onplaying","onpluginstreamstart","onpointercancel","onpointerdown","onpointerenter","onpointerleave","onpointerlockchange","onpointerlockerror","onpointermove","onpointerout","onpointerover","onpointerrawupdate","onpointerup","onpopstate","onprocessorerror","onprogress","onpropertychange","onratechange","onreading","onreadystatechange","onrejectionhandled","onrelease","onremove","onremovesourcebuffer","onremovestream","onremovetrack","onrepeat","onreset","onresize","onresizeend","onresizestart","onresourcetimingbufferfull","onresult","onresume","onrowenter","onrowexit","onrowsdelete","onrowsinserted","onscroll","onsearch","onsecuritypolicyviolation","onseeked","onseeking","onselect","onselectedcandidatepairchange","onselectend","onselectionchange","onselectstart","onshippingaddresschange","onshippingoptionchange","onshow","onsignalingstatechange","onsoundend","onsoundstart","onsourceclose","onsourceclosed","onsourceended","onsourceopen","onspeechend","onspeechstart","onsqueeze","onsqueezeend","onsqueezestart","onstalled","onstart","onstatechange","onstop","onstorage","onstoragecommit","onsubmit","onsuccess","onsuspend","onterminate","ontextinput","ontimeout","ontimeupdate","ontoggle","ontonechange","ontouchcancel","ontouchend","ontouchmove","ontouchstart","ontrack","ontransitioncancel","ontransitionend","ontransitionrun","ontransitionstart","onunhandledrejection","onunload","onunmute","onupdate","onupdateend","onupdatefound","onupdateready","onupdatestart","onupgradeneeded","onuserproximity","onversionchange","onvisibilitychange","onvoiceschanged","onvolumechange","onvrdisplayactivate","onvrdisplayconnect","onvrdisplaydeactivate","onvrdisplaydisconnect","onvrdisplaypresentchange","onwaiting","onwaitingforkey","onwarning","onwebkitanimationend","onwebkitanimationiteration","onwebkitanimationstart","onwebkitcurrentplaybacktargetiswirelesschanged","onwebkitfullscreenchange","onwebkitfullscreenerror","onwebkitkeyadded","onwebkitkeyerror","onwebkitkeymessage","onwebkitneedkey","onwebkitorientationchange","onwebkitplaybacktargetavailabilitychanged","onwebkitpointerlockchange","onwebkitpointerlockerror","onwebkitresourcetimingbufferfull","onwebkittransitionend","onwheel","onzoom","opacity","open","openCursor","openDatabase","openKeyCursor","opened","opener","opera","operationType","operator","opr","optimum","options","or","order","orderX","orderY","ordered","org","organization","orient","orientAngle","orientType","orientation","orientationX","orientationY","orientationZ","origin","originalPolicy","originalTarget","orphans","oscpu","outerHTML","outerHeight","outerText","outerWidth","outline","outline-color","outline-offset","outline-style","outline-width","outlineColor","outlineOffset","outlineStyle","outlineWidth","outputBuffer","outputLatency","outputs","overflow","overflow-anchor","overflow-block","overflow-inline","overflow-wrap","overflow-x","overflow-y","overflowAnchor","overflowBlock","overflowInline","overflowWrap","overflowX","overflowY","overrideMimeType","oversample","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","overscrollBehavior","overscrollBehaviorBlock","overscrollBehaviorInline","overscrollBehaviorX","overscrollBehaviorY","ownKeys","ownerDocument","ownerElement","ownerNode","ownerRule","ownerSVGElement","owningElement","p1","p2","p3","p4","packetSize","packets","pad","padEnd","padStart","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","paddingBlock","paddingBlockEnd","paddingBlockStart","paddingBottom","paddingInline","paddingInlineEnd","paddingInlineStart","paddingLeft","paddingRight","paddingTop","page","page-break-after","page-break-before","page-break-inside","pageBreakAfter","pageBreakBefore","pageBreakInside","pageCount","pageLeft","pageTop","pageX","pageXOffset","pageY","pageYOffset","pages","paint-order","paintOrder","paintRequests","paintType","paintWorklet","palette","pan","panningModel","parameters","parent","parentElement","parentNode","parentRule","parentStyleSheet","parentTextEdit","parentWindow","parse","parseAll","parseFloat","parseFromString","parseInt","part","participants","passive","password","pasteHTML","path","pathLength","pathSegList","pathSegType","pathSegTypeAsLetter","pathname","pattern","patternContentUnits","patternMismatch","patternTransform","patternUnits","pause","pauseAnimations","pauseOnExit","pauseProfilers","pauseTransformFeedback","paused","payerEmail","payerName","payerPhone","paymentManager","pc","peerIdentity","pending","pendingLocalDescription","pendingRemoteDescription","percent","performance","periodicSync","permission","permissionState","permissions","persist","persisted","personalbar","perspective","perspective-origin","perspectiveOrigin","phone","phoneticFamilyName","phoneticGivenName","photo","pictureInPictureElement","pictureInPictureEnabled","pictureInPictureWindow","ping","pipeThrough","pipeTo","pitch","pixelBottom","pixelDepth","pixelHeight","pixelLeft","pixelRight","pixelStorei","pixelTop","pixelUnitToMillimeterX","pixelUnitToMillimeterY","pixelWidth","place-content","place-items","place-self","placeContent","placeItems","placeSelf","placeholder","platformVersion","platform","platforms","play","playEffect","playState","playbackRate","playbackState","playbackTime","played","playoutDelayHint","playsInline","plugins","pluginspage","pname","pointer-events","pointerBeforeReferenceNode","pointerEnabled","pointerEvents","pointerId","pointerLockElement","pointerType","points","pointsAtX","pointsAtY","pointsAtZ","polygonOffset","pop","populateMatrix","popupWindowFeatures","popupWindowName","popupWindowURI","port","port1","port2","ports","posBottom","posHeight","posLeft","posRight","posTop","posWidth","pose","position","positionAlign","positionX","positionY","positionZ","postError","postMessage","postalCode","poster","pow","powerEfficient","powerOff","preMultiplySelf","precision","preferredStyleSheetSet","preferredStylesheetSet","prefix","preload","prepend","presentation","preserveAlpha","preserveAspectRatio","preserveAspectRatioString","pressed","pressure","prevValue","preventDefault","preventExtensions","preventSilentAccess","previousElementSibling","previousNode","previousPage","previousRect","previousScale","previousSibling","previousTranslate","primaryKey","primitiveType","primitiveUnits","principals","print","priority","privateKey","probablySupportsContext","process","processIceMessage","processingEnd","processingStart","product","productId","productName","productSub","profile","profileEnd","profiles","projectionMatrix","promise","prompt","properties","propertyIsEnumerable","propertyName","protocol","protocolLong","prototype","provider","pseudoClass","pseudoElement","pt","publicId","publicKey","published","pulse","push","pushManager","pushNotification","pushState","put","putImageData","px","quadraticCurveTo","qualifier","quaternion","query","queryCommandEnabled","queryCommandIndeterm","queryCommandState","queryCommandSupported","queryCommandText","queryCommandValue","querySelector","querySelectorAll","queueMicrotask","quote","quotes","r","r1","r2","race","rad","radiogroup","radiusX","radiusY","random","range","rangeCount","rangeMax","rangeMin","rangeOffset","rangeOverflow","rangeParent","rangeUnderflow","rate","ratio","raw","rawId","read","readAsArrayBuffer","readAsBinaryString","readAsBlob","readAsDataURL","readAsText","readBuffer","readEntries","readOnly","readPixels","readReportRequested","readText","readValue","readable","ready","readyState","reason","reboot","receivedAlert","receiver","receivers","recipient","reconnect","recordNumber","recordsAvailable","recordset","rect","red","redEyeReduction","redirect","redirectCount","redirectEnd","redirectStart","redirected","reduce","reduceRight","reduction","refDistance","refX","refY","referenceNode","referenceSpace","referrer","referrerPolicy","refresh","region","regionAnchorX","regionAnchorY","regionId","regions","register","registerContentHandler","registerElement","registerProperty","registerProtocolHandler","reject","rel","relList","relatedAddress","relatedNode","relatedPort","relatedTarget","release","releaseCapture","releaseEvents","releaseInterface","releaseLock","releasePointerCapture","releaseShaderCompiler","reliable","reliableWrite","reload","rem","remainingSpace","remote","remoteDescription","remove","removeAllRanges","removeAttribute","removeAttributeNS","removeAttributeNode","removeBehavior","removeChild","removeCue","removeEventListener","removeFilter","removeImport","removeItem","removeListener","removeNamedItem","removeNamedItemNS","removeNode","removeParameter","removeProperty","removeRange","removeRegion","removeRule","removeSiteSpecificTrackingException","removeSourceBuffer","removeStream","removeTrack","removeVariable","removeWakeLockListener","removeWebWideTrackingException","removed","removedNodes","renderHeight","renderState","renderTime","renderWidth","renderbufferStorage","renderbufferStorageMultisample","renderedBuffer","renderingMode","renotify","repeat","replace","replaceAdjacentText","replaceAll","replaceChild","replaceChildren","replaceData","replaceId","replaceItem","replaceNode","replaceState","replaceSync","replaceTrack","replaceWholeText","replaceWith","reportValidity","request","requestAnimationFrame","requestAutocomplete","requestData","requestDevice","requestFrame","requestFullscreen","requestHitTestSource","requestHitTestSourceForTransientInput","requestId","requestIdleCallback","requestMIDIAccess","requestMediaKeySystemAccess","requestPermission","requestPictureInPicture","requestPointerLock","requestPresent","requestReferenceSpace","requestSession","requestStart","requestStorageAccess","requestSubmit","requestVideoFrameCallback","requestingWindow","requireInteraction","required","requiredExtensions","requiredFeatures","reset","resetPose","resetTransform","resize","resizeBy","resizeTo","resolve","response","responseBody","responseEnd","responseReady","responseStart","responseText","responseType","responseURL","responseXML","restartIce","restore","result","resultIndex","resultType","results","resume","resumeProfilers","resumeTransformFeedback","retry","returnValue","rev","reverse","reversed","revocable","revokeObjectURL","rgbColor","right","rightContext","rightDegrees","rightMargin","rightProjectionMatrix","rightViewMatrix","role","rolloffFactor","root","rootBounds","rootElement","rootMargin","rotate","rotateAxisAngle","rotateAxisAngleSelf","rotateFromVector","rotateFromVectorSelf","rotateSelf","rotation","rotationAngle","rotationRate","round","row-gap","rowGap","rowIndex","rowSpan","rows","rtcpTransport","rtt","ruby-align","ruby-position","rubyAlign","rubyOverhang","rubyPosition","rules","runtime","runtimeStyle","rx","ry","s","safari","sample","sampleCoverage","sampleRate","samplerParameterf","samplerParameteri","sandbox","save","saveData","scale","scale3d","scale3dSelf","scaleNonUniform","scaleNonUniformSelf","scaleSelf","scheme","scissor","scope","scopeName","scoped","screen","screenBrightness","screenEnabled","screenLeft","screenPixelToMillimeterX","screenPixelToMillimeterY","screenTop","screenX","screenY","scriptURL","scripts","scroll","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","scrollAmount","scrollBehavior","scrollBy","scrollByLines","scrollByPages","scrollDelay","scrollHeight","scrollIntoView","scrollIntoViewIfNeeded","scrollLeft","scrollLeftMax","scrollMargin","scrollMarginBlock","scrollMarginBlockEnd","scrollMarginBlockStart","scrollMarginBottom","scrollMarginInline","scrollMarginInlineEnd","scrollMarginInlineStart","scrollMarginLeft","scrollMarginRight","scrollMarginTop","scrollMaxX","scrollMaxY","scrollPadding","scrollPaddingBlock","scrollPaddingBlockEnd","scrollPaddingBlockStart","scrollPaddingBottom","scrollPaddingInline","scrollPaddingInlineEnd","scrollPaddingInlineStart","scrollPaddingLeft","scrollPaddingRight","scrollPaddingTop","scrollRestoration","scrollSnapAlign","scrollSnapType","scrollTo","scrollTop","scrollTopMax","scrollWidth","scrollX","scrollY","scrollbar-color","scrollbar-width","scrollbar3dLightColor","scrollbarArrowColor","scrollbarBaseColor","scrollbarColor","scrollbarDarkShadowColor","scrollbarFaceColor","scrollbarHighlightColor","scrollbarShadowColor","scrollbarTrackColor","scrollbarWidth","scrollbars","scrolling","scrollingElement","sctp","sctpCauseCode","sdp","sdpLineNumber","sdpMLineIndex","sdpMid","seal","search","searchBox","searchBoxJavaBridge_","searchParams","sectionRowIndex","secureConnectionStart","security","seed","seekToNextFrame","seekable","seeking","select","selectAllChildren","selectAlternateInterface","selectConfiguration","selectNode","selectNodeContents","selectNodes","selectSingleNode","selectSubString","selected","selectedIndex","selectedOptions","selectedStyleSheetSet","selectedStylesheetSet","selection","selectionDirection","selectionEnd","selectionStart","selector","selectorText","self","send","sendAsBinary","sendBeacon","sender","sentAlert","sentTimestamp","separator","serialNumber","serializeToString","serverTiming","service","serviceWorker","session","sessionId","sessionStorage","set","setActionHandler","setActive","setAlpha","setAppBadge","setAttribute","setAttributeNS","setAttributeNode","setAttributeNodeNS","setBaseAndExtent","setBigInt64","setBigUint64","setBingCurrentSearchDefault","setCapture","setCodecPreferences","setColor","setCompositeOperation","setConfiguration","setCurrentTime","setCustomValidity","setData","setDate","setDragImage","setEnd","setEndAfter","setEndBefore","setEndPoint","setFillColor","setFilterRes","setFloat32","setFloat64","setFloatValue","setFormValue","setFullYear","setHeaderValue","setHours","setIdentityProvider","setImmediate","setInt16","setInt32","setInt8","setInterval","setItem","setKeyframes","setLineCap","setLineDash","setLineJoin","setLineWidth","setLiveSeekableRange","setLocalDescription","setMatrix","setMatrixValue","setMediaKeys","setMilliseconds","setMinutes","setMiterLimit","setMonth","setNamedItem","setNamedItemNS","setNonUserCodeExceptions","setOrientToAngle","setOrientToAuto","setOrientation","setOverrideHistoryNavigationMode","setPaint","setParameter","setParameters","setPeriodicWave","setPointerCapture","setPosition","setPositionState","setPreference","setProperty","setPrototypeOf","setRGBColor","setRGBColorICCColor","setRadius","setRangeText","setRemoteDescription","setRequestHeader","setResizable","setResourceTimingBufferSize","setRotate","setScale","setSeconds","setSelectionRange","setServerCertificate","setShadow","setSinkId","setSkewX","setSkewY","setStart","setStartAfter","setStartBefore","setStdDeviation","setStreams","setStringValue","setStrokeColor","setSuggestResult","setTargetAtTime","setTargetValueAtTime","setTime","setTimeout","setTransform","setTranslate","setUTCDate","setUTCFullYear","setUTCHours","setUTCMilliseconds","setUTCMinutes","setUTCMonth","setUTCSeconds","setUint16","setUint32","setUint8","setUri","setValidity","setValueAtTime","setValueCurveAtTime","setVariable","setVelocity","setVersion","setYear","settingName","settingValue","sex","shaderSource","shadowBlur","shadowColor","shadowOffsetX","shadowOffsetY","shadowRoot","shape","shape-image-threshold","shape-margin","shape-outside","shape-rendering","shapeImageThreshold","shapeMargin","shapeOutside","shapeRendering","sheet","shift","shiftKey","shiftLeft","shippingAddress","shippingOption","shippingType","show","showHelp","showModal","showModalDialog","showModelessDialog","showNotification","sidebar","sign","signal","signalingState","signature","silent","sin","singleNodeValue","sinh","sinkId","sittingToStandingTransform","size","sizeToContent","sizeX","sizeZ","sizes","skewX","skewXSelf","skewY","skewYSelf","slice","slope","slot","small","smil","smooth","smoothingTimeConstant","snapToLines","snapshotItem","snapshotLength","some","sort","sortingCode","source","sourceBuffer","sourceBuffers","sourceCapabilities","sourceFile","sourceIndex","sources","spacing","span","speak","speakAs","speaking","species","specified","specularConstant","specularExponent","speechSynthesis","speed","speedOfSound","spellcheck","splice","split","splitText","spreadMethod","sqrt","src","srcElement","srcFilter","srcObject","srcUrn","srcdoc","srclang","srcset","stack","stackTraceLimit","stacktrace","stageParameters","standalone","standby","start","startContainer","startIce","startMessages","startNotifications","startOffset","startProfiling","startRendering","startShark","startTime","startsWith","state","status","statusCode","statusMessage","statusText","statusbar","stdDeviationX","stdDeviationY","stencilFunc","stencilFuncSeparate","stencilMask","stencilMaskSeparate","stencilOp","stencilOpSeparate","step","stepDown","stepMismatch","stepUp","sticky","stitchTiles","stop","stop-color","stop-opacity","stopColor","stopImmediatePropagation","stopNotifications","stopOpacity","stopProfiling","stopPropagation","stopShark","stopped","storage","storageArea","storageName","storageStatus","store","storeSiteSpecificTrackingException","storeWebWideTrackingException","stpVersion","stream","streams","stretch","strike","string","stringValue","stringify","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeRect","strokeStyle","strokeText","strokeWidth","style","styleFloat","styleMap","styleMedia","styleSheet","styleSheetSets","styleSheets","sub","subarray","subject","submit","submitFrame","submitter","subscribe","substr","substring","substringData","subtle","subtree","suffix","suffixes","summary","sup","supported","supportedContentEncodings","supportedEntryTypes","supports","supportsSession","surfaceScale","surroundContents","suspend","suspendRedraw","swapCache","swapNode","sweepFlag","symbols","sync","sysexEnabled","system","systemCode","systemId","systemLanguage","systemXDPI","systemYDPI","tBodies","tFoot","tHead","tabIndex","table","table-layout","tableLayout","tableValues","tag","tagName","tagUrn","tags","taintEnabled","takePhoto","takeRecords","tan","tangentialPressure","tanh","target","targetElement","targetRayMode","targetRaySpace","targetTouches","targetX","targetY","tcpType","tee","tel","terminate","test","texImage2D","texImage3D","texParameterf","texParameteri","texStorage2D","texStorage3D","texSubImage2D","texSubImage3D","text","text-align","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-transform","text-underline-offset","text-underline-position","textAlign","textAlignLast","textAnchor","textAutospace","textBaseline","textCombineUpright","textContent","textDecoration","textDecorationBlink","textDecorationColor","textDecorationLine","textDecorationLineThrough","textDecorationNone","textDecorationOverline","textDecorationSkipInk","textDecorationStyle","textDecorationThickness","textDecorationUnderline","textEmphasis","textEmphasisColor","textEmphasisPosition","textEmphasisStyle","textIndent","textJustify","textJustifyTrim","textKashida","textKashidaSpace","textLength","textOrientation","textOverflow","textRendering","textShadow","textTracks","textTransform","textUnderlineOffset","textUnderlinePosition","then","threadId","threshold","thresholds","tiltX","tiltY","time","timeEnd","timeLog","timeOrigin","timeRemaining","timeStamp","timecode","timeline","timelineTime","timeout","timestamp","timestampOffset","timing","title","to","toArray","toBlob","toDataURL","toDateString","toElement","toExponential","toFixed","toFloat32Array","toFloat64Array","toGMTString","toISOString","toJSON","toLocaleDateString","toLocaleFormat","toLocaleLowerCase","toLocaleString","toLocaleTimeString","toLocaleUpperCase","toLowerCase","toMatrix","toMethod","toPrecision","toPrimitive","toSdp","toSource","toStaticHTML","toString","toStringTag","toSum","toTimeString","toUTCString","toUpperCase","toggle","toggleAttribute","toggleLongPressEnabled","tone","toneBuffer","tooLong","tooShort","toolbar","top","topMargin","total","totalFrameDelay","totalVideoFrames","touch-action","touchAction","touched","touches","trace","track","trackVisibility","transaction","transactions","transceiver","transferControlToOffscreen","transferFromImageBitmap","transferImageBitmap","transferIn","transferOut","transferSize","transferToImageBitmap","transform","transform-box","transform-origin","transform-style","transformBox","transformFeedbackVaryings","transformOrigin","transformPoint","transformString","transformStyle","transformToDocument","transformToFragment","transition","transition-delay","transition-duration","transition-property","transition-timing-function","transitionDelay","transitionDuration","transitionProperty","transitionTimingFunction","translate","translateSelf","translationX","translationY","transport","trim","trimEnd","trimLeft","trimRight","trimStart","trueSpeed","trunc","truncate","trustedTypes","turn","twist","type","typeDetail","typeMismatch","typeMustMatch","types","u2f","ubound","uint16","uint32","uint8","uint8Clamped","undefined","unescape","uneval","unicode","unicode-bidi","unicodeBidi","unicodeRange","uniform1f","uniform1fv","uniform1i","uniform1iv","uniform1ui","uniform1uiv","uniform2f","uniform2fv","uniform2i","uniform2iv","uniform2ui","uniform2uiv","uniform3f","uniform3fv","uniform3i","uniform3iv","uniform3ui","uniform3uiv","uniform4f","uniform4fv","uniform4i","uniform4iv","uniform4ui","uniform4uiv","uniformBlockBinding","uniformMatrix2fv","uniformMatrix2x3fv","uniformMatrix2x4fv","uniformMatrix3fv","uniformMatrix3x2fv","uniformMatrix3x4fv","uniformMatrix4fv","uniformMatrix4x2fv","uniformMatrix4x3fv","unique","uniqueID","uniqueNumber","unit","unitType","units","unloadEventEnd","unloadEventStart","unlock","unmount","unobserve","unpause","unpauseAnimations","unreadCount","unregister","unregisterContentHandler","unregisterProtocolHandler","unscopables","unselectable","unshift","unsubscribe","unsuspendRedraw","unsuspendRedrawAll","unwatch","unwrapKey","upDegrees","upX","upY","upZ","update","updateCommands","updateIce","updateInterval","updatePlaybackRate","updateRenderState","updateSettings","updateTiming","updateViaCache","updateWith","updated","updating","upgrade","upload","uploadTotal","uploaded","upper","upperBound","upperOpen","uri","url","urn","urns","usages","usb","usbVersionMajor","usbVersionMinor","usbVersionSubminor","useCurrentView","useMap","useProgram","usedSpace","user-select","userActivation","userAgent","userAgentData","userChoice","userHandle","userHint","userLanguage","userSelect","userVisibleOnly","username","usernameFragment","utterance","uuid","v8BreakIterator","vAlign","vLink","valid","validate","validateProgram","validationMessage","validity","value","valueAsDate","valueAsNumber","valueAsString","valueInSpecifiedUnits","valueMissing","valueOf","valueText","valueType","values","variable","variant","variationSettings","vector-effect","vectorEffect","velocityAngular","velocityExpansion","velocityX","velocityY","vendor","vendorId","vendorSub","verify","version","vertexAttrib1f","vertexAttrib1fv","vertexAttrib2f","vertexAttrib2fv","vertexAttrib3f","vertexAttrib3fv","vertexAttrib4f","vertexAttrib4fv","vertexAttribDivisor","vertexAttribDivisorANGLE","vertexAttribI4i","vertexAttribI4iv","vertexAttribI4ui","vertexAttribI4uiv","vertexAttribIPointer","vertexAttribPointer","vertical","vertical-align","verticalAlign","verticalOverflow","vh","vibrate","vibrationActuator","videoBitsPerSecond","videoHeight","videoTracks","videoWidth","view","viewBox","viewBoxString","viewTarget","viewTargetString","viewport","viewportAnchorX","viewportAnchorY","viewportElement","views","violatedDirective","visibility","visibilityState","visible","visualViewport","vlinkColor","vmax","vmin","voice","voiceURI","volume","vrml","vspace","vw","w","wait","waitSync","waiting","wake","wakeLock","wand","warn","wasClean","wasDiscarded","watch","watchAvailability","watchPosition","webdriver","webkitAddKey","webkitAlignContent","webkitAlignItems","webkitAlignSelf","webkitAnimation","webkitAnimationDelay","webkitAnimationDirection","webkitAnimationDuration","webkitAnimationFillMode","webkitAnimationIterationCount","webkitAnimationName","webkitAnimationPlayState","webkitAnimationTimingFunction","webkitAppearance","webkitAudioContext","webkitAudioDecodedByteCount","webkitAudioPannerNode","webkitBackfaceVisibility","webkitBackground","webkitBackgroundAttachment","webkitBackgroundClip","webkitBackgroundColor","webkitBackgroundImage","webkitBackgroundOrigin","webkitBackgroundPosition","webkitBackgroundPositionX","webkitBackgroundPositionY","webkitBackgroundRepeat","webkitBackgroundSize","webkitBackingStorePixelRatio","webkitBorderBottomLeftRadius","webkitBorderBottomRightRadius","webkitBorderImage","webkitBorderImageOutset","webkitBorderImageRepeat","webkitBorderImageSlice","webkitBorderImageSource","webkitBorderImageWidth","webkitBorderRadius","webkitBorderTopLeftRadius","webkitBorderTopRightRadius","webkitBoxAlign","webkitBoxDirection","webkitBoxFlex","webkitBoxOrdinalGroup","webkitBoxOrient","webkitBoxPack","webkitBoxShadow","webkitBoxSizing","webkitCancelAnimationFrame","webkitCancelFullScreen","webkitCancelKeyRequest","webkitCancelRequestAnimationFrame","webkitClearResourceTimings","webkitClosedCaptionsVisible","webkitConvertPointFromNodeToPage","webkitConvertPointFromPageToNode","webkitCreateShadowRoot","webkitCurrentFullScreenElement","webkitCurrentPlaybackTargetIsWireless","webkitDecodedFrameCount","webkitDirectionInvertedFromDevice","webkitDisplayingFullscreen","webkitDroppedFrameCount","webkitEnterFullScreen","webkitEnterFullscreen","webkitEntries","webkitExitFullScreen","webkitExitFullscreen","webkitExitPointerLock","webkitFilter","webkitFlex","webkitFlexBasis","webkitFlexDirection","webkitFlexFlow","webkitFlexGrow","webkitFlexShrink","webkitFlexWrap","webkitFullScreenKeyboardInputAllowed","webkitFullscreenElement","webkitFullscreenEnabled","webkitGenerateKeyRequest","webkitGetAsEntry","webkitGetDatabaseNames","webkitGetEntries","webkitGetEntriesByName","webkitGetEntriesByType","webkitGetFlowByName","webkitGetGamepads","webkitGetImageDataHD","webkitGetNamedFlows","webkitGetRegionFlowRanges","webkitGetUserMedia","webkitHasClosedCaptions","webkitHidden","webkitIDBCursor","webkitIDBDatabase","webkitIDBDatabaseError","webkitIDBDatabaseException","webkitIDBFactory","webkitIDBIndex","webkitIDBKeyRange","webkitIDBObjectStore","webkitIDBRequest","webkitIDBTransaction","webkitImageSmoothingEnabled","webkitIndexedDB","webkitInitMessageEvent","webkitIsFullScreen","webkitJustifyContent","webkitKeys","webkitLineClamp","webkitLineDashOffset","webkitLockOrientation","webkitMask","webkitMaskClip","webkitMaskComposite","webkitMaskImage","webkitMaskOrigin","webkitMaskPosition","webkitMaskPositionX","webkitMaskPositionY","webkitMaskRepeat","webkitMaskSize","webkitMatchesSelector","webkitMediaStream","webkitNotifications","webkitOfflineAudioContext","webkitOrder","webkitOrientation","webkitPeerConnection00","webkitPersistentStorage","webkitPerspective","webkitPerspectiveOrigin","webkitPointerLockElement","webkitPostMessage","webkitPreservesPitch","webkitPutImageDataHD","webkitRTCPeerConnection","webkitRegionOverset","webkitRelativePath","webkitRequestAnimationFrame","webkitRequestFileSystem","webkitRequestFullScreen","webkitRequestFullscreen","webkitRequestPointerLock","webkitResolveLocalFileSystemURL","webkitSetMediaKeys","webkitSetResourceTimingBufferSize","webkitShadowRoot","webkitShowPlaybackTargetPicker","webkitSlice","webkitSpeechGrammar","webkitSpeechGrammarList","webkitSpeechRecognition","webkitSpeechRecognitionError","webkitSpeechRecognitionEvent","webkitStorageInfo","webkitSupportsFullscreen","webkitTemporaryStorage","webkitTextFillColor","webkitTextSizeAdjust","webkitTextStroke","webkitTextStrokeColor","webkitTextStrokeWidth","webkitTransform","webkitTransformOrigin","webkitTransformStyle","webkitTransition","webkitTransitionDelay","webkitTransitionDuration","webkitTransitionProperty","webkitTransitionTimingFunction","webkitURL","webkitUnlockOrientation","webkitUserSelect","webkitVideoDecodedByteCount","webkitVisibilityState","webkitWirelessVideoPlaybackDisabled","webkitdirectory","webkitdropzone","webstore","weight","whatToShow","wheelDelta","wheelDeltaX","wheelDeltaY","whenDefined","which","white-space","whiteSpace","wholeText","widows","width","will-change","willChange","willValidate","window","withCredentials","word-break","word-spacing","word-wrap","wordBreak","wordSpacing","wordWrap","workerStart","wow64","wrap","wrapKey","writable","writableAuxiliaries","write","writeText","writeValue","writeWithoutResponse","writeln","writing-mode","writingMode","x","x1","x2","xChannelSelector","xmlEncoding","xmlStandalone","xmlVersion","xmlbase","xmllang","xmlspace","xor","xr","y","y1","y2","yChannelSelector","yandex","z","z-index","zIndex","zoom","zoomAndPan","zoomRectScreen"];function find_builtins(e){Qn.forEach(add);var t=["Symbol","Map","Promise","Proxy","Reflect","Set","WeakMap","WeakSet"];var n={};var i=typeof global==="object"?global:self;t.forEach((function(e){n[e]=i[e]||function(){}}));["null","true","false","NaN","Infinity","-Infinity","undefined"].forEach(add);[Object,Array,Function,Number,String,Boolean,Error,Math,Date,RegExp,n.Symbol,ArrayBuffer,DataView,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,eval,EvalError,Float32Array,Float64Array,Int8Array,Int16Array,Int32Array,isFinite,isNaN,JSON,n.Map,parseFloat,parseInt,n.Promise,n.Proxy,RangeError,ReferenceError,n.Reflect,n.Set,SyntaxError,TypeError,Uint8Array,Uint8ClampedArray,Uint16Array,Uint32Array,URIError,n.WeakMap,n.WeakSet].forEach((function(e){Object.getOwnPropertyNames(e).map(add);if(e.prototype){Object.getOwnPropertyNames(e.prototype).map(add)}}));function add(t){e.add(t)}}function reserve_quoted_keys(e,t){function add(e){push_uniq(t,e)}e.walk(new TreeWalker((function(e){if(e instanceof at&&e.quote){add(e.key)}else if(e instanceof ot&&e.quote){add(e.key.name)}else if(e instanceof Ye){addStrings(e.property,add)}})))}function addStrings(e,t){e.walk(new TreeWalker((function(e){if(e instanceof He){addStrings(e.tail_node(),t)}else if(e instanceof Gt){t(e.value)}else if(e instanceof et){addStrings(e.consequent,t);addStrings(e.alternative,t)}return true})))}function mangle_private_properties(e,t){var n=-1;var i=new Map;var r=t.nth_identifier||En;e=e.transform(new TreeTransformer((function(e){if(e instanceof mt||e instanceof _t||e instanceof ut||e instanceof st){e.key.name=mangle_private(e.key.name)}else if(e instanceof qe){e.property=mangle_private(e.property)}})));return e;function mangle_private(e){let t=i.get(e);if(!t){t=r.get(++n);i.set(e,t)}return t}}function mangle_properties(e,t){t=defaults(t,{builtins:false,cache:null,debug:false,keep_quoted:false,nth_identifier:En,only_cache:false,regex:null,reserved:null,undeclared:false},true);var n=t.nth_identifier;var i=t.reserved;if(!Array.isArray(i))i=[i];var r=new Set(i);if(!t.builtins)find_builtins(r);var o=-1;var a;if(t.cache){a=t.cache.props}else{a=new Map}var s=t.regex&&new RegExp(t.regex);var u=t.debug!==false;var l;if(u){l=t.debug===true?"":t.debug}var c=new Set;var f=new Set;a.forEach((e=>f.add(e)));var _=!!t.keep_quoted;e.walk(new TreeWalker((function(e){if(e instanceof mt||e instanceof _t||e instanceof ut||e instanceof st||e instanceof qe);else if(e instanceof at){if(typeof e.key=="string"&&(!_||!e.quote)){add(e.key)}}else if(e instanceof ot){if(!_||!e.quote){add(e.key.name)}}else if(e instanceof We){var n=!!t.undeclared;if(!n){var i=e;while(i.expression){i=i.expression}n=!(i.thedef&&i.thedef.undeclared)}if(n&&(!_||!e.quote)){add(e.property)}}else if(e instanceof Ye){if(!_){addStrings(e.property,add)}}else if(e instanceof Ke&&e.expression.print_to_string()=="Object.defineProperty"){addStrings(e.args[1],add)}else if(e instanceof Je&&e.operator==="in"){addStrings(e.left,add)}})));return e.transform(new TreeTransformer((function(e){if(e instanceof mt||e instanceof _t||e instanceof ut||e instanceof st||e instanceof qe);else if(e instanceof at){if(typeof e.key=="string"&&(!_||!e.quote)){e.key=mangle(e.key)}}else if(e instanceof ot){if(!_||!e.quote){e.key.name=mangle(e.key.name)}}else if(e instanceof We){if(!_||!e.quote){e.property=mangle(e.property)}}else if(!_&&e instanceof Ye){e.property=mangleStrings(e.property)}else if(e instanceof Ke&&e.expression.print_to_string()=="Object.defineProperty"){e.args[1]=mangleStrings(e.args[1])}else if(e instanceof Je&&e.operator==="in"){e.left=mangleStrings(e.left)}})));function can_mangle(e){if(f.has(e))return false;if(r.has(e))return false;if(t.only_cache){return a.has(e)}if(/^-?[0-9]+(\.[0-9]+)?(e[+-][0-9]+)?$/.test(e))return false;return true}function should_mangle(e){if(s&&!s.test(e))return false;if(r.has(e))return false;return a.has(e)||c.has(e)}function add(e){if(can_mangle(e))c.add(e);if(!should_mangle(e)){f.add(e)}}function mangle(e){if(!should_mangle(e)){return e}var t=a.get(e);if(!t){if(u){var i="_$"+e+"$"+l+"_";if(can_mangle(i)){t=i}}if(!t){do{t=n.get(++o)}while(!can_mangle(t))}a.set(e,t)}return t}function mangleStrings(e){return e.transform(new TreeTransformer((function(e){if(e instanceof He){var t=e.expressions.length-1;e.expressions[t]=mangleStrings(e.expressions[t])}else if(e instanceof Gt){e.value=mangle(e.value)}else if(e instanceof et){e.consequent=mangleStrings(e.consequent);e.alternative=mangleStrings(e.alternative)}return e})))}}var Jn=typeof atob=="undefined"?function(e){return Buffer.from(e,"base64").toString()}:atob;var ei=typeof btoa=="undefined"?function(e){return Buffer.from(e).toString("base64")}:btoa;function read_source_map(e){var t=/(?:^|[^.])\/\/# sourceMappingURL=data:application\/json(;[\w=-]*)?;base64,([+/0-9A-Za-z]*=*)\s*$/.exec(e);if(!t){console.warn("inline source map not found");return null}return Jn(t[2])}function set_shorthand(e,t,n){if(t[e]){n.forEach((function(n){if(t[n]){if(typeof t[n]!="object")t[n]={};if(!(e in t[n]))t[n][e]=t[e]}}))}}function init_cache(e){if(!e)return;if(!("props"in e)){e.props=new Map}else if(!(e.props instanceof Map)){e.props=map_from_object(e.props)}}function cache_to_json(e){return{props:map_to_object(e.props)}}function log_input(e,t,n,i){if(!(n&&n.writeFileSync&&n.mkdirSync)){return}try{n.mkdirSync(i)}catch(e){if(e.code!=="EEXIST")throw e}const r=`${i}/terser-debug-${Math.random()*9999999|0}.log`;t=t||{};const o=JSON.stringify(t,((e,t)=>{if(typeof t==="function")return"[Function "+t.toString()+"]";if(t instanceof RegExp)return"[RegExp "+t.toString()+"]";return t}),4);const files_str=e=>{if(typeof e==="object"&&t.parse&&t.parse.spidermonkey){return JSON.stringify(e,null,2)}else if(typeof e==="object"){return Object.keys(e).map((t=>t+": "+files_str(e[t]))).join("\n\n")}else if(typeof e==="string"){return"```\n"+e+"\n```"}else{return e}};n.writeFileSync(r,"Options: \n"+o+"\n\nInput files:\n\n"+files_str(e)+"\n")}async function minify(e,t,n){if(n&&typeof process==="object"&&process.env&&typeof process.env.TERSER_DEBUG_DIR==="string"){log_input(e,t,n,process.env.TERSER_DEBUG_DIR)}t=defaults(t,{compress:{},ecma:undefined,enclose:false,ie8:false,keep_classnames:undefined,keep_fnames:false,mangle:{},module:false,nameCache:null,output:null,format:null,parse:{},rename:undefined,safari10:false,sourceMap:false,spidermonkey:false,timings:false,toplevel:false,warnings:false,wrap:false},true);var i=t.timings&&{start:Date.now()};if(t.keep_classnames===undefined){t.keep_classnames=t.keep_fnames}if(t.rename===undefined){t.rename=t.compress&&t.mangle}if(t.output&&t.format){throw new Error("Please only specify either output or format option, preferrably format.")}t.format=t.format||t.output||{};set_shorthand("ecma",t,["parse","compress","format"]);set_shorthand("ie8",t,["compress","mangle","format"]);set_shorthand("keep_classnames",t,["compress","mangle"]);set_shorthand("keep_fnames",t,["compress","mangle"]);set_shorthand("module",t,["parse","compress","mangle"]);set_shorthand("safari10",t,["mangle","format"]);set_shorthand("toplevel",t,["compress","mangle"]);set_shorthand("warnings",t,["compress"]);var r;if(t.mangle){t.mangle=defaults(t.mangle,{cache:t.nameCache&&(t.nameCache.vars||{}),eval:false,ie8:false,keep_classnames:false,keep_fnames:false,module:false,nth_identifier:En,properties:false,reserved:[],safari10:false,toplevel:false},true);if(t.mangle.properties){if(typeof t.mangle.properties!="object"){t.mangle.properties={}}if(t.mangle.properties.keep_quoted){r=t.mangle.properties.reserved;if(!Array.isArray(r))r=[];t.mangle.properties.reserved=r}if(t.nameCache&&!("cache"in t.mangle.properties)){t.mangle.properties.cache=t.nameCache.props||{}}}init_cache(t.mangle.cache);init_cache(t.mangle.properties.cache)}if(t.sourceMap){t.sourceMap=defaults(t.sourceMap,{asObject:false,content:null,filename:null,includeSources:false,root:null,url:null},true)}if(i)i.parse=Date.now();var o;if(e instanceof oe){o=e}else{if(typeof e=="string"||t.parse.spidermonkey&&!Array.isArray(e)){e=[e]}t.parse=t.parse||{};t.parse.toplevel=null;if(t.parse.spidermonkey){t.parse.toplevel=z.from_mozilla_ast(Object.keys(e).reduce((function(t,n){if(!t)return e[n];t.body=t.body.concat(e[n].body);return t}),null))}else{delete t.parse.spidermonkey;for(var a in e)if(HOP(e,a)){t.parse.filename=a;t.parse.toplevel=parse(e[a],t.parse);if(t.sourceMap&&t.sourceMap.content=="inline"){if(Object.keys(e).length>1)throw new Error("inline source map only works with singular input");t.sourceMap.content=read_source_map(e[a])}}}o=t.parse.toplevel}if(r&&t.mangle.properties.keep_quoted!=="strict"){reserve_quoted_keys(o,r)}if(t.wrap){o=o.wrap_commonjs(t.wrap)}if(t.enclose){o=o.wrap_enclose(t.enclose)}if(i)i.rename=Date.now();if(i)i.compress=Date.now();if(t.compress){o=new Compressor(t.compress,{mangle_options:t.mangle}).compress(o)}if(i)i.scope=Date.now();if(t.mangle)o.figure_out_scope(t.mangle);if(i)i.mangle=Date.now();if(t.mangle){o.compute_char_frequency(t.mangle);o.mangle_names(t.mangle);o=mangle_private_properties(o,t.mangle)}if(i)i.properties=Date.now();if(t.mangle&&t.mangle.properties){o=mangle_properties(o,t.mangle.properties)}if(i)i.format=Date.now();var s={};if(t.format.ast){s.ast=o}if(t.format.spidermonkey){s.ast=o.to_mozilla_ast()}if(!HOP(t.format,"code")||t.format.code){if(!t.format.ast){t.format._destroy_ast=true;walk(o,(e=>{if(e instanceof re){e.variables=undefined;e.enclosed=undefined;e.parent_scope=undefined}if(e.block_scope){e.block_scope.variables=undefined;e.block_scope.enclosed=undefined;e.parent_scope=undefined}}))}if(t.sourceMap){if(t.sourceMap.includeSources&&e instanceof oe){throw new Error("original source content unavailable")}t.format.source_map=await SourceMap({file:t.sourceMap.filename,orig:t.sourceMap.content,root:t.sourceMap.root,files:t.sourceMap.includeSources?e:null})}delete t.format.ast;delete t.format.code;delete t.format.spidermonkey;var u=OutputStream(t.format);o.print(u);s.code=u.get();if(t.sourceMap){Object.defineProperty(s,"map",{configurable:true,enumerable:true,get(){const e=t.format.source_map.getEncoded();return s.map=t.sourceMap.asObject?e:JSON.stringify(e)},set(e){Object.defineProperty(s,"map",{value:e,writable:true})}});s.decoded_map=t.format.source_map.getDecoded();if(t.sourceMap.url=="inline"){var l=typeof s.map==="object"?JSON.stringify(s.map):s.map;s.code+="\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,"+ei(l)}else if(t.sourceMap.url){s.code+="\n//# sourceMappingURL="+t.sourceMap.url}}}if(t.nameCache&&t.mangle){if(t.mangle.cache)t.nameCache.vars=cache_to_json(t.mangle.cache);if(t.mangle.properties&&t.mangle.properties.cache){t.nameCache.props=cache_to_json(t.mangle.properties.cache)}}if(t.format&&t.format.source_map){t.format.source_map.destroy()}if(i){i.end=Date.now();s.timings={parse:.001*(i.rename-i.parse),rename:.001*(i.compress-i.rename),compress:.001*(i.scope-i.compress),scope:.001*(i.mangle-i.scope),mangle:.001*(i.properties-i.mangle),properties:.001*(i.format-i.properties),format:.001*(i.end-i.format),total:.001*(i.end-i.start)}}return s}async function run_cli({program:e,packageJson:t,fs:i,path:r}){const o=new Set(["cname","parent_scope","scope","uses_eval","uses_with"]);var a={};var s={compress:false,mangle:false};const u=await _default_options();e.version(t.name+" "+t.version);e.parseArgv=e.parse;e.parse=undefined;if(process.argv.includes("ast"))e.helpInformation=describe_ast;else if(process.argv.includes("options"))e.helpInformation=function(){var e=[];for(var t in u){e.push("--"+(t==="sourceMap"?"source-map":t)+" options:");e.push(format_object(u[t]));e.push("")}return e.join("\n")};e.option("-p, --parse <options>","Specify parser options.",parse_js());e.option("-c, --compress [options]","Enable compressor/specify compressor options.",parse_js());e.option("-m, --mangle [options]","Mangle names/specify mangler options.",parse_js());e.option("--mangle-props [options]","Mangle properties/specify mangler options.",parse_js());e.option("-f, --format [options]","Format options.",parse_js());e.option("-b, --beautify [options]","Alias for --format.",parse_js());e.option("-o, --output <file>","Output file (default STDOUT).");e.option("--comments [filter]","Preserve copyright comments in the output.");e.option("--config-file <file>","Read minify() options from JSON file.");e.option("-d, --define <expr>[=value]","Global definitions.",parse_js("define"));e.option("--ecma <version>","Specify ECMAScript release: 5, 2015, 2016 or 2017...");e.option("-e, --enclose [arg[,...][:value[,...]]]","Embed output in a big function with configurable arguments and values.");e.option("--ie8","Support non-standard Internet Explorer 8.");e.option("--keep-classnames","Do not mangle/drop class names.");e.option("--keep-fnames","Do not mangle/drop function names. Useful for code relying on Function.prototype.name.");e.option("--module","Input is an ES6 module");e.option("--name-cache <file>","File to hold mangled name mappings.");e.option("--rename","Force symbol expansion.");e.option("--no-rename","Disable symbol expansion.");e.option("--safari10","Support non-standard Safari 10.");e.option("--source-map [options]","Enable source map/specify source map options.",parse_js());e.option("--timings","Display operations run time on STDERR.");e.option("--toplevel","Compress and/or mangle variables in toplevel scope.");e.option("--wrap <name>","Embed everything as a function with “exports” corresponding to “name” globally.");e.arguments("[files...]").parseArgv(process.argv);if(e.configFile){s=JSON.parse(read_file(e.configFile))}if(!e.output&&e.sourceMap&&e.sourceMap.url!="inline"){fatal("ERROR: cannot write source map to STDOUT")}["compress","enclose","ie8","mangle","module","safari10","sourceMap","toplevel","wrap"].forEach((function(t){if(t in e){s[t]=e[t]}}));if("ecma"in e){if(e.ecma!=(e.ecma|0))fatal("ERROR: ecma must be an integer");const t=e.ecma|0;if(t>5&&t<2015)s.ecma=t+2009;else s.ecma=t}if(e.format||e.beautify){const t=e.format||e.beautify;s.format=typeof t==="object"?t:{}}if(e.comments){if(typeof s.format!="object")s.format={};s.format.comments=typeof e.comments=="string"?e.comments=="false"?false:e.comments:"some"}if(e.define){if(typeof s.compress!="object")s.compress={};if(typeof s.compress.global_defs!="object")s.compress.global_defs={};for(var l in e.define){s.compress.global_defs[l]=e.define[l]}}if(e.keepClassnames){s.keep_classnames=true}if(e.keepFnames){s.keep_fnames=true}if(e.mangleProps){if(e.mangleProps.domprops){delete e.mangleProps.domprops}else{if(typeof e.mangleProps!="object")e.mangleProps={};if(!Array.isArray(e.mangleProps.reserved))e.mangleProps.reserved=[]}if(typeof s.mangle!="object")s.mangle={};s.mangle.properties=e.mangleProps}if(e.nameCache){s.nameCache=JSON.parse(read_file(e.nameCache,"{}"))}if(e.output=="ast"){s.format={ast:true,code:false}}if(e.parse){if(!e.parse.acorn&&!e.parse.spidermonkey){s.parse=e.parse}else if(e.sourceMap&&e.sourceMap.content=="inline"){fatal("ERROR: inline source map only works with built-in parser")}}if(~e.rawArgs.indexOf("--rename")){s.rename=true}else if(!e.rename){s.rename=false}let convert_path=e=>e;if(typeof e.sourceMap=="object"&&"base"in e.sourceMap){convert_path=function(){var t=e.sourceMap.base;delete s.sourceMap.base;return function(e){return r.relative(t,e)}}()}let c;if(s.files&&s.files.length){c=s.files;delete s.files}else if(e.args.length){c=e.args}if(c){simple_glob(c).forEach((function(e){a[convert_path(e)]=read_file(e)}))}else{await new Promise((e=>{var t=[];process.stdin.setEncoding("utf8");process.stdin.on("data",(function(e){t.push(e)})).on("end",(function(){a=[t.join("")];e()}));process.stdin.resume()}))}await run_cli();function convert_ast(e){return z.from_mozilla_ast(Object.keys(a).reduce(e,null))}async function run_cli(){var t=e.sourceMap&&e.sourceMap.content;if(t&&t!=="inline"){s.sourceMap.content=read_file(t,t)}if(e.timings)s.timings=true;try{if(e.parse){if(e.parse.acorn){a=convert_ast((function(t,i){return n(988).parse(a[i],{ecmaVersion:2018,locations:true,program:t,sourceFile:i,sourceType:s.module||e.parse.module?"module":"script"})}))}else if(e.parse.spidermonkey){a=convert_ast((function(e,t){var n=JSON.parse(a[t]);if(!e)return n;e.body=e.body.concat(n.body);return e}))}}}catch(e){fatal(e)}let r;try{r=await minify(a,s,i)}catch(e){if(e.name=="SyntaxError"){print_error("Parse error at "+e.filename+":"+e.line+","+e.col);var u=e.col;var l=a[e.filename].split(/\r?\n/);var c=l[e.line-1];if(!c&&!u){c=l[e.line-2];u=c.length}if(c){var f=70;if(u>f){c=c.slice(u-f);u=f}print_error(c.slice(0,80));print_error(c.slice(0,u).replace(/\S/g," ")+"^")}}if(e.defs){print_error("Supported options:");print_error(format_object(e.defs))}fatal(e);return}if(e.output=="ast"){if(!s.compress&&!s.mangle){r.ast.figure_out_scope({})}console.log(JSON.stringify(r.ast,(function(e,t){if(t)switch(e){case"thedef":return symdef(t);case"enclosed":return t.length?t.map(symdef):undefined;case"variables":case"globals":return t.size?collect_from_map(t,symdef):undefined}if(o.has(e))return;if(t instanceof AST_Token)return;if(t instanceof Map)return;if(t instanceof z){var n={_class:"AST_"+t.TYPE};if(t.block_scope){n.variables=t.block_scope.variables;n.enclosed=t.block_scope.enclosed}t.CTOR.PROPS.forEach((function(e){n[e]=t[e]}));return n}return t}),2))}else if(e.output=="spidermonkey"){try{const e=await minify(r.code,{compress:false,mangle:false,format:{ast:true,code:false}},i);console.log(JSON.stringify(e.ast.to_mozilla_ast(),null,2))}catch(e){fatal(e);return}}else if(e.output){i.writeFileSync(e.output,r.code);if(s.sourceMap&&s.sourceMap.url!=="inline"&&r.map){i.writeFileSync(e.output+".map",r.map)}}else{console.log(r.code)}if(e.nameCache){i.writeFileSync(e.nameCache,JSON.stringify(s.nameCache))}if(r.timings)for(var _ in r.timings){print_error("- "+_+": "+r.timings[_].toFixed(3)+"s")}}function fatal(e){if(e instanceof Error)e=e.stack.replace(/^\S*?Error:/,"ERROR:");print_error(e);process.exit(1)}function simple_glob(e){if(Array.isArray(e)){return[].concat.apply([],e.map(simple_glob))}if(e&&e.match(/[*?]/)){var t=r.dirname(e);try{var n=i.readdirSync(t)}catch(e){}if(n){var o="^"+r.basename(e).replace(/[.+^$[\]\\(){}]/g,"\\$&").replace(/\*/g,"[^/\\\\]*").replace(/\?/g,"[^/\\\\]")+"$";var a=process.platform==="win32"?"i":"";var s=new RegExp(o,a);var u=n.filter((function(e){return s.test(e)})).map((function(e){return r.join(t,e)}));if(u.length)return u}}return[e]}function read_file(e,t){try{return i.readFileSync(e,"utf8")}catch(e){if((e.code=="ENOENT"||e.code=="ENAMETOOLONG")&&t!=null)return t;fatal(e)}}function parse_js(e){return function(t,n){n=n||{};try{walk(parse(t,{expression:true}),(t=>{if(t instanceof tt){var i=t.left.print_to_string();var r=t.right;if(e){n[i]=r}else if(r instanceof it){n[i]=r.elements.map(to_string)}else if(r instanceof Wt){r=r.value;n[i]=new RegExp(r.source,r.flags)}else{n[i]=to_string(r)}return true}if(t instanceof gt||t instanceof Xe){var i=t.print_to_string();n[i]=true;return true}if(!(t instanceof He))throw t;function to_string(e){return e instanceof Kt?e.getValue():e.print_to_string({quote_keys:true})}}))}catch(i){if(e){fatal("Error parsing arguments for '"+e+"': "+t)}else{n[t]=null}}return n}}function symdef(e){var t=1e6+e.id+" "+e.name;if(e.mangled_name)t+=" "+e.mangled_name;return t}function collect_from_map(e,t){var n=[];e.forEach((function(e){n.push(t(e))}));return n}function format_object(e){var t=[];var n="";Object.keys(e).map((function(t){if(n.length<t.length)n=Array(t.length+1).join(" ");return[t,JSON.stringify(e[t])]})).forEach((function(e){t.push("  "+e[0]+n.slice(e[0].length-2)+e[1])}));return t.join("\n")}function print_error(e){process.stderr.write(e);process.stderr.write("\n")}function describe_ast(){var e=OutputStream({beautify:true});function doitem(t){e.print("AST_"+t.TYPE);const n=t.SELF_PROPS.filter((e=>!/^\$/.test(e)));if(n.length>0){e.space();e.with_parens((function(){n.forEach((function(t,n){if(n)e.space();e.print(t)}))}))}if(t.documentation){e.space();e.print_string(t.documentation)}if(t.SUBCLASSES.length>0){e.space();e.with_block((function(){t.SUBCLASSES.forEach((function(t){e.indent();doitem(t);e.newline()}))}))}}doitem(z);return e+"\n"}}async function _default_options(){const e={};Object.keys(infer_options({0:0})).forEach((t=>{const n=infer_options({[t]:{0:0}});if(n)e[t]=n}));return e}async function infer_options(e){try{await minify("",e)}catch(e){return e.defs}}e._default_options=_default_options;e._run_cli=run_cli;e.minify=minify}))}};var t={};function __nccwpck_require__(n){var i=t[n];if(i!==undefined){return i.exports}var r=t[n]={exports:{}};var o=true;try{e[n].call(r.exports,r,r.exports,__nccwpck_require__);o=false}finally{if(o)delete t[n]}return r.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(221);module.exports=n})();