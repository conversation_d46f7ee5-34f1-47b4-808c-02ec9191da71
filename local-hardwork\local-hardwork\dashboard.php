<?php
session_start();
// In a real application, you would check if the user is logged in here
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin Portal</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <nav class="navbar">
            <div class="logo">Admin Portal</div>
            <div class="nav-links">
                <a href="index.php">Home</a>
                <a href="dashboard.php" class="active">Dashboard</a>
            </div>
        </nav>

        <main class="dashboard">
            <div class="dashboard-content">
                <h1>Dashboard</h1>
                
                <div class="profile-settings">
                    <h2>Profile Settings</h2>
                    <form action="update-profile.php" method="POST" enctype="multipart/form-data" class="profile-form">
                        <div class="profile-photo">
                            <img src="default-avatar.png" alt="Profile Photo" id="profile-preview">
                            <div class="photo-upload">
                                <label for="photo-upload" class="upload-btn">Change Photo</label>
                                <input type="file" id="photo-upload" name="profile_photo" accept="image/*" style="display: none;">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" placeholder="Enter your email">
                        </div>

                        <div class="form-group">
                            <label for="coordinates">Location Coordinates</label>
                            <input type="text" id="coordinates" name="coordinates" placeholder="Enter coordinates (e.g., 40.7128° N, 74.0060° W)">
                        </div>

                        <button type="submit" class="save-btn">Save Changes</button>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Preview uploaded image
        document.getElementById('photo-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('profile-preview').src = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html> 