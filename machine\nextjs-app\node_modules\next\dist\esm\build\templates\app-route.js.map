{"version": 3, "sources": ["../../../src/build/templates/app-route.ts"], "names": ["module", "RouteKind", "userland", "AppRouteRouteModule", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "header<PERSON><PERSON>s", "staticGenerationBailout", "originalPathname"], "mappings": "AAAA,OAAO,qCAAoC;AAE3C,oEAAoE;AACpE,YAAYA,YAAY,kEAAiE;AAGzF,SAASC,SAAS,QAAQ,iCAAgC;AAE1D,0DAA0D;AAC1D,YAAYC,cAAc,eAAc;AAExC,MAAMC,sBACJH,OAAOG,mBAAmB;AAO5B,2EAA2E;AAC3E,UAAU;AACV,0BAA0B;AAE1B,MAAMC,cAAc,IAAID,oBAAoB;IAC1CE,YAAY;QACVC,MAAML,UAAUM,SAAS;QACzBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,kBAAkB;IAClBC;IACAX;AACF;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,EACJY,mBAAmB,EACnBC,4BAA4B,EAC5BC,WAAW,EACXC,WAAW,EACXC,uBAAuB,EACxB,GAAGd;AAEJ,MAAMe,mBAAmB;AAEzB,SACEf,WAAW,EACXU,mBAAmB,EACnBC,4BAA4B,EAC5BC,WAAW,EACXC,WAAW,EACXC,uBAAuB,EACvBC,gBAAgB,KACjB"}