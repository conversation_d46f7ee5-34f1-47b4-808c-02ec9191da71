from app import db, User, app
from werkzeug.security import generate_password_hash

def init_db():
    with app.app_context():
        db.create_all()
        # Check if admin exists
        if not User.query.filter_by(username='admin').first():
            admin = User(username='admin', password=generate_password_hash('admin123'), is_admin=True)
            db.session.add(admin)
            db.session.commit()
            print('Admin user created: username=admin, password=admin123')
        else:
            print('Admin user already exists.')

if __name__ == '__main__':
    init_db() 