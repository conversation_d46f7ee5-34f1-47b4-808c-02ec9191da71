{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/resolve-route-data.ts"], "names": ["resolveArray", "resolveRobots", "data", "content", "rules", "Array", "isArray", "rule", "userAgent", "agent", "allow", "item", "disallow", "crawlDelay", "host", "sitemap", "for<PERSON>ach", "resolveSitemap", "url", "lastModified", "serializedDate", "Date", "toISOString", "changeFrequency", "priority", "resolveManifest", "JSON", "stringify", "resolveRouteData", "fileType"], "mappings": "AACA,SAASA,YAAY,QAAQ,0CAAyC;AAEtE,oCAAoC;AACpC,OAAO,SAASC,cAAcC,IAA0B;IACtD,IAAIC,UAAU;IACd,MAAMC,QAAQC,MAAMC,OAAO,CAACJ,KAAKE,KAAK,IAAIF,KAAKE,KAAK,GAAG;QAACF,KAAKE,KAAK;KAAC;IACnE,KAAK,MAAMG,QAAQH,MAAO;QACxB,MAAMI,YAAYR,aAAaO,KAAKC,SAAS,IAAI;YAAC;SAAI;QACtD,KAAK,MAAMC,SAASD,UAAW;YAC7BL,WAAW,CAAC,YAAY,EAAEM,MAAM,EAAE,CAAC;QACrC;QACA,IAAIF,KAAKG,KAAK,EAAE;YACd,MAAMA,QAAQV,aAAaO,KAAKG,KAAK;YACrC,KAAK,MAAMC,QAAQD,MAAO;gBACxBP,WAAW,CAAC,OAAO,EAAEQ,KAAK,EAAE,CAAC;YAC/B;QACF;QACA,IAAIJ,KAAKK,QAAQ,EAAE;YACjB,MAAMA,WAAWZ,aAAaO,KAAKK,QAAQ;YAC3C,KAAK,MAAMD,QAAQC,SAAU;gBAC3BT,WAAW,CAAC,UAAU,EAAEQ,KAAK,EAAE,CAAC;YAClC;QACF;QACA,IAAIJ,KAAKM,UAAU,EAAE;YACnBV,WAAW,CAAC,aAAa,EAAEI,KAAKM,UAAU,CAAC,EAAE,CAAC;QAChD;QACAV,WAAW;IACb;IACA,IAAID,KAAKY,IAAI,EAAE;QACbX,WAAW,CAAC,MAAM,EAAED,KAAKY,IAAI,CAAC,EAAE,CAAC;IACnC;IACA,IAAIZ,KAAKa,OAAO,EAAE;QAChB,MAAMA,UAAUf,aAAaE,KAAKa,OAAO;QACzC,+DAA+D;QAC/DA,QAAQC,OAAO,CAAC,CAACL;YACfR,WAAW,CAAC,SAAS,EAAEQ,KAAK,EAAE,CAAC;QACjC;IACF;IAEA,OAAOR;AACT;AAEA,6CAA6C;AAC7C,qCAAqC;AACrC,OAAO,SAASc,eAAef,IAA2B;IACxD,IAAIC,UAAU;IACdA,WAAW;IACXA,WAAW;IAEX,KAAK,MAAMQ,QAAQT,KAAM;QACvBC,WAAW;QACXA,WAAW,CAAC,KAAK,EAAEQ,KAAKO,GAAG,CAAC,QAAQ,CAAC;QAErC,IAAIP,KAAKQ,YAAY,EAAE;YACrB,MAAMC,iBACJT,KAAKQ,YAAY,YAAYE,OACzBV,KAAKQ,YAAY,CAACG,WAAW,KAC7BX,KAAKQ,YAAY;YAEvBhB,WAAW,CAAC,SAAS,EAAEiB,eAAe,YAAY,CAAC;QACrD;QAEA,IAAIT,KAAKY,eAAe,EAAE;YACxBpB,WAAW,CAAC,YAAY,EAAEQ,KAAKY,eAAe,CAAC,eAAe,CAAC;QACjE;QAEA,IAAI,OAAOZ,KAAKa,QAAQ,KAAK,UAAU;YACrCrB,WAAW,CAAC,UAAU,EAAEQ,KAAKa,QAAQ,CAAC,aAAa,CAAC;QACtD;QAEArB,WAAW;IACb;IAEAA,WAAW;IAEX,OAAOA;AACT;AAEA,OAAO,SAASsB,gBAAgBvB,IAA4B;IAC1D,OAAOwB,KAAKC,SAAS,CAACzB;AACxB;AAEA,OAAO,SAAS0B,iBACd1B,IAA2E,EAC3E2B,QAA2C;IAE3C,IAAIA,aAAa,UAAU;QACzB,OAAO5B,cAAcC;IACvB;IACA,IAAI2B,aAAa,WAAW;QAC1B,OAAOZ,eAAef;IACxB;IACA,IAAI2B,aAAa,YAAY;QAC3B,OAAOJ,gBAAgBvB;IACzB;IACA,OAAO;AACT"}