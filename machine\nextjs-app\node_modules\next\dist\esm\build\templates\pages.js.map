{"version": 3, "sources": ["../../../src/build/templates/pages.ts"], "names": ["module", "RouteKind", "hoist", "Document", "App", "userland", "PagesRouteModule", "getStaticProps", "getStaticPaths", "getServerSideProps", "config", "reportWebVitals", "unstable_getStaticProps", "unstable_getStaticPaths", "unstable_getStaticParams", "unstable_getServerProps", "unstable_getServerSideProps", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "bundlePath", "filename", "components"], "mappings": "AAAA,oEAAoE;AACpE,YAAYA,YAAY,8DAA6D;AACrF,SAASC,SAAS,QAAQ,iCAAgC;AAC1D,SAASC,KAAK,QAAQ,YAAW;AAEjC,uCAAuC;AACvC,0DAA0D;AAC1D,OAAOC,cAAc,sBAAqB;AAC1C,0DAA0D;AAC1D,OAAOC,SAAS,iBAAgB;AAEhC,4BAA4B;AAC5B,0DAA0D;AAC1D,YAAYC,cAAc,eAAc;AAExC,MAAMC,mBACJN,OAAOM,gBAAgB;AAEzB,0DAA0D;AAC1D,eAAeJ,MAAMG,UAAU,WAAU;AAEzC,qBAAqB;AACrB,OAAO,MAAME,iBAAiBL,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAMG,iBAAiBN,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAMI,qBAAqBP,MAAMG,UAAU,sBAAqB;AACvE,OAAO,MAAMK,SAASR,MAAMG,UAAU,UAAS;AAC/C,OAAO,MAAMM,kBAAkBT,MAAMG,UAAU,mBAAkB;AAEjE,4BAA4B;AAC5B,OAAO,MAAMO,0BAA0BV,MACrCG,UACA,2BACD;AACD,OAAO,MAAMQ,0BAA0BX,MACrCG,UACA,2BACD;AACD,OAAO,MAAMS,2BAA2BZ,MACtCG,UACA,4BACD;AACD,OAAO,MAAMU,0BAA0Bb,MACrCG,UACA,2BACD;AACD,OAAO,MAAMW,8BAA8Bd,MACzCG,UACA,+BACD;AAED,4DAA4D;AAC5D,OAAO,MAAMY,cAAc,IAAIX,iBAAiB;IAC9CY,YAAY;QACVC,MAAMlB,UAAUmB,KAAK;QACrBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAC,YAAY;QACVrB;QACAD;IACF;IACAE;AACF,GAAE"}