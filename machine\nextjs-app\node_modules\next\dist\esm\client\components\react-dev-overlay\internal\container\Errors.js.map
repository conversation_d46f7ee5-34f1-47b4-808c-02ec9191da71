{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/Errors.tsx"], "names": ["React", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "LeftRightDialogHeader", "Overlay", "Toast", "getErrorByType", "getErrorSource", "noop", "css", "CloseIcon", "RuntimeError", "VersionStalenessInfo", "HotlinkedText", "getErrorSignature", "ev", "event", "type", "reason", "name", "message", "stack", "_", "Errors", "errors", "initialDisplayState", "versionInfo", "lookups", "setLookups", "useState", "readyErrors", "nextError", "useMemo", "ready", "next", "idx", "length", "e", "id", "push", "prev", "isLoading", "Boolean", "useEffect", "mounted", "then", "resolved", "m", "displayState", "setDisplayState", "activeIdx", "setActiveIndex", "previous", "useCallback", "preventDefault", "v", "Math", "max", "min", "activeError", "minimize", "hide", "fullscreen", "className", "onClick", "div", "svg", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "circle", "cx", "cy", "r", "line", "x1", "y1", "x2", "y2", "span", "button", "data-nextjs-toast-errors-hide-button", "stopPropagation", "aria-label", "isServerError", "includes", "error", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "undefined", "close", "small", "h1", "p", "text", "key", "toString", "styles"], "mappings": ";;;;;;;;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SACEC,sBAAsB,EACtBC,0BAA0B,QAGrB,2BAA0B;AACjC,SACEC,MAAM,EACNC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,uBAAsB;AAC7B,SAASC,qBAAqB,QAAQ,sCAAqC;AAC3E,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,KAAK,QAAQ,sBAAqB;AAC3C,SAASC,cAAc,QAA2B,4BAA2B;AAC7E,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AACtD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,qCAAoC;AAEzE,SAASC,aAAa,QAAQ,gCAA+B;AAgB7D,SAASC,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,OAAQC,MAAMC,IAAI;QAChB,KAAKpB;QACL,KAAKC;YAA4B;gBAC/B,OAAO,AAAGkB,MAAME,MAAM,CAACC,IAAI,GAAC,OAAIH,MAAME,MAAM,CAACE,OAAO,GAAC,OAAIJ,MAAME,MAAM,CAACG,KAAK;YAC7E;QACA;YAAS,CACT;IACF;IAEA,6DAA6D;IAC7D,MAAMC,IAAWN;IACjB,OAAO;AACT;AAEA,OAAO,MAAMO,SAAgC,SAASA,OAAO,KAI5D;IAJ4D,IAAA,EAC3DC,MAAM,EACNC,mBAAmB,EACnBC,WAAW,EACZ,GAJ4D;IAK3D,MAAM,CAACC,SAASC,WAAW,GAAGhC,MAAMiC,QAAQ,CAC1C,CAAC;IAGH,MAAM,CAACC,aAAaC,UAAU,GAAGnC,MAAMoC,OAAO,CAE5C;QACA,IAAIC,QAA2B,EAAE;QACjC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMX,OAAOY,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAIb,MAAM,CAACW,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMX,SAAS;gBACjBM,MAAMM,IAAI,CAACZ,OAAO,CAACW,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOhB,MAAM,CAACW,MAAM,EAAE;gBAC5B,IAAIrB,kBAAkB0B,UAAU1B,kBAAkBuB,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACV;QAAQG;KAAQ;IAEpB,MAAMc,YAAY7C,MAAMoC,OAAO,CAAU;QACvC,OAAOF,YAAYM,MAAM,GAAG,KAAKM,QAAQlB,OAAOY,MAAM;IACxD,GAAG;QAACZ,OAAOY,MAAM;QAAEN,YAAYM,MAAM;KAAC;IAEtCxC,MAAM+C,SAAS,CAAC;QACd,IAAIZ,aAAa,MAAM;YACrB;QACF;QACA,IAAIa,UAAU;QAEdtC,eAAeyB,WAAWc,IAAI,CAC5B,CAACC;YACC,sEAAsE;YACtE,uEAAuE;YACvE,kBAAkB;YAClB,IAAIF,SAAS;gBACXhB,WAAW,CAACmB,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAASR,EAAE,CAAC,EAAEQ;oBAAS,CAAA;YACrD;QACF,GACA;QACE,yCAAyC;QAC3C;QAGF,OAAO;YACLF,UAAU;QACZ;IACF,GAAG;QAACb;KAAU;IAEd,MAAM,CAACiB,cAAcC,gBAAgB,GACnCrD,MAAMiC,QAAQ,CAAeJ;IAC/B,MAAM,CAACyB,WAAWC,eAAe,GAAGvD,MAAMiC,QAAQ,CAAS;IAC3D,MAAMuB,WAAWxD,MAAMyD,WAAW,CAAC,CAAChB;QAClCA,qBAAAA,EAAGiB,cAAc;QACjBH,eAAe,CAACI,IAAMC,KAAKC,GAAG,CAAC,GAAGF,IAAI;IACxC,GAAG,EAAE;IACL,MAAMrB,OAAOtC,MAAMyD,WAAW,CAC5B,CAAChB;QACCA,qBAAAA,EAAGiB,cAAc;QACjBH,eAAe,CAACI,IACdC,KAAKC,GAAG,CAAC,GAAGD,KAAKE,GAAG,CAAC5B,YAAYM,MAAM,GAAG,GAAGmB,IAAI;IAErD,GACA;QAACzB,YAAYM,MAAM;KAAC;QAIdN;IADR,MAAM6B,cAAc/D,MAAMoC,OAAO,CAC/B,IAAMF,CAAAA,yBAAAA,WAAW,CAACoB,UAAU,YAAtBpB,yBAA0B,MAChC;QAACoB;QAAWpB;KAAY;IAG1B,kEAAkE;IAClE,gDAAgD;IAChDlC,MAAM+C,SAAS,CAAC;QACd,IAAInB,OAAOY,MAAM,GAAG,GAAG;YACrBR,WAAW,CAAC;YACZqB,gBAAgB;YAChBE,eAAe;QACjB;IACF,GAAG;QAAC3B,OAAOY,MAAM;KAAC;IAElB,MAAMwB,WAAWhE,MAAMyD,WAAW,CAAC,CAAChB;QAClCA,qBAAAA,EAAGiB,cAAc;QACjBL,gBAAgB;IAClB,GAAG,EAAE;IACL,MAAMY,OAAOjE,MAAMyD,WAAW,CAAC,CAAChB;QAC9BA,qBAAAA,EAAGiB,cAAc;QACjBL,gBAAgB;IAClB,GAAG,EAAE;IACL,MAAMa,aAAalE,MAAMyD,WAAW,CAClC,CAAChB;QACCA,qBAAAA,EAAGiB,cAAc;QACjBL,gBAAgB;IAClB,GACA,EAAE;IAGJ,2EAA2E;IAC3E,6CAA6C;IAC7C,IAAIzB,OAAOY,MAAM,GAAG,KAAKuB,eAAe,MAAM;QAC5C,OAAO;IACT;IAEA,IAAIlB,WAAW;QACb,6BAA6B;QAC7B,qBAAO,oBAACrC;IACV;IAEA,IAAI4C,iBAAiB,UAAU;QAC7B,OAAO;IACT;IAEA,IAAIA,iBAAiB,aAAa;QAChC,qBACE,oBAAC3C;YAAM0D,WAAU;YAA6BC,SAASF;yBACrD,oBAACG;YAAIF,WAAU;yBACb,oBAACG;YACCC,OAAM;YACNC,OAAM;YACNC,QAAO;YACPC,SAAQ;YACRC,MAAK;YACLC,QAAO;YACPC,aAAY;YACZC,eAAc;YACdC,gBAAe;yBAEf,oBAACC;YAAOC,IAAG;YAAKC,IAAG;YAAKC,GAAE;0BAC1B,oBAACC;YAAKC,IAAG;YAAKC,IAAG;YAAIC,IAAG;YAAKC,IAAG;0BAChC,oBAACJ;YAAKC,IAAG;YAAKC,IAAG;YAAKC,IAAG;YAAQC,IAAG;2BAEtC,oBAACC,cACEvD,YAAYM,MAAM,EAAC,UAAON,YAAYM,MAAM,GAAG,IAAI,MAAM,mBAE5D,oBAACkD;YACCC,wCAAAA;YACAxB,WAAU;YACV9C,MAAK;YACL+C,SAAS,CAAC3B;gBACRA,EAAEmD,eAAe;gBACjB3B;YACF;YACA4B,cAAW;yBAEX,oBAAC/E;IAKX;IAEA,MAAMgF,gBAAgB;QAAC;QAAU;KAAc,CAACC,QAAQ,CACtDpF,eAAeoD,YAAYiC,KAAK,KAAK;IAGvC,qBACE,oBAACxF,6BACC,oBAACL;QACCkB,MAAK;QACL4E,mBAAgB;QAChBC,oBAAiB;QACjBC,SAASL,gBAAgBM,YAAYpC;qBAErC,oBAAC3D,mCACC,oBAACC;QAAa6D,WAAU;qBACtB,oBAAC5D;QACCiD,UAAUF,YAAY,IAAIE,WAAW;QACrClB,MAAMgB,YAAYpB,YAAYM,MAAM,GAAG,IAAIF,OAAO;QAClD+D,OAAOP,gBAAgBM,YAAYpC;qBAEnC,oBAACsC,6BACC,oBAACb,cAAMnC,YAAY,IAAS,OAAI,mBAChC,oBAACmC,cAAMvD,YAAYM,MAAM,GAAQ,oBAChCN,YAAYM,MAAM,GAAG,IAAI,KAAK,MAEhCV,4BAAc,oBAACd,sBAAyBc,eAAkB,qBAE7D,oBAACyE;QAAG7D,IAAG;OACJoD,gBAAgB,iBAAiB,0CAEpC,oBAACU;QAAE9D,IAAG;OACHqB,YAAYiC,KAAK,CAACzE,IAAI,EAAC,KAAE,mBAC1B,oBAACN;QAAcwF,MAAM1C,YAAYiC,KAAK,CAACxE,OAAO;SAE/CsE,8BACC,oBAACzB,2BACC,oBAACiC,eAAM,gHAKPF,0BAEN,oBAAChG;QAAW+D,WAAU;qBACpB,oBAACpD;QAAa2F,KAAK3C,YAAYrB,EAAE,CAACiE,QAAQ;QAAIX,OAAOjC;;AAMjE,EAAC;AAED,OAAO,MAAM6C,SAAS/F,uBAqErB"}