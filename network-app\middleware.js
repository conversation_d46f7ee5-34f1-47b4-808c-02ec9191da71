import { NextResponse } from 'next/server';
export function middleware(request) {
  const { pathname } = request.nextUrl;
  const url = request.nextUrl.clone();
  const sessionToken = request.cookies.get('auth_token');
  const header = request.headers.get('x-middleware-subrequest');
if (header === 'middleware:middleware:middleware:middleware:middleware') {
  return NextResponse.next();
}
   if (!sessionToken) {
     url.pathname = '/';
    return NextResponse.redirect(url);
  }
   return NextResponse.next();
}
 export const config = {
  matcher: [
    '/dashboard',
    '/saved',
    '/settings',
    '/faq',
    '/user-management',
  ],
};
