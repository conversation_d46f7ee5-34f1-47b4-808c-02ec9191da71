/**
 * @license React
 * react-server-dom-webpack-server.edge.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("react"),ba=require("react-dom"),l=null,m=0;function q(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<m&&(a.enqueue(new Uint8Array(l.buffer,0,m)),l=new Uint8Array(512),m=0),a.enqueue(b);else{var d=l.length-m;d<b.byteLength&&(0===d?a.enqueue(l):(l.set(b.subarray(0,d),m),a.enqueue(l),b=b.subarray(d)),l=new Uint8Array(512),m=0);l.set(b,m);m+=b.byteLength}return!0}var r=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var t=Symbol.for("react.client.reference"),u=Symbol.for("react.server.reference");function v(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===u){var b=ea.call(arguments,1);a.$$typeof=u;a.$$id=this.$$id;a.$$bound=this.$$bound?this.$$bound.concat(b):b}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=v(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=v({},a.$$id,!0),e=new Proxy(c,ka);a.status="fulfilled";a.value=e;return a.then=v(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}c=a[b];c||(c=v(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},sa={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=w();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),x(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?x(d,"C",[a,b]):x(d,"C",a))}}}
function na(a,b,d){if("string"===typeof a){var c=w();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,h=d.imageSizes,k="";"string"===typeof g&&""!==g?(k+="["+g+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;f+="[image]"+k}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=y(d))?x(c,"L",[a,b,d]):x(c,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"m",[a,b]):x(d,"m",a)}}}
function pa(a,b,d){if("string"===typeof a){var c=w();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=y(d))?x(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?x(c,"S",[a,b]):x(c,"S",a)}}}function qa(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"X",[a,b]):x(d,"X",a)}}}function ra(a,b){if("string"===typeof a){var d=w();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"M",[a,b]):x(d,"M",a)}}}
function y(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ta=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,ua="function"===typeof AsyncLocalStorage,va=ua?new AsyncLocalStorage:null,z=Symbol.for("react.element"),wa=Symbol.for("react.fragment"),xa=Symbol.for("react.provider"),ya=Symbol.for("react.server_context"),za=Symbol.for("react.forward_ref"),Aa=Symbol.for("react.suspense"),Ba=Symbol.for("react.suspense_list"),Ca=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Da=Symbol.for("react.default_value"),Ea=Symbol.for("react.memo_cache_sentinel"),
Fa=Symbol.for("react.postpone"),Ga=Symbol.iterator,B=null;function D(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");D(a,d);b.context._currentValue=b.value}}}function Ha(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ha(a)}
function Ia(a){var b=a.parent;null!==b&&Ia(b);a.context._currentValue=a.value}function Ja(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?D(a,b):Ja(a,b)}
function Ka(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?D(a,d):Ka(a,d);b.context._currentValue=b.value}function La(a){var b=B;b!==a&&(null===b?Ia(a):null===a?Ha(b):b.depth===a.depth?D(b,a):b.depth>a.depth?Ja(b,a):Ka(b,a),B=a)}function Ma(a,b){var d=a._currentValue;a._currentValue=b;var c=B;return B=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Na=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Oa(){}function Pa(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Oa,Oa),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}E=b;throw Na;}}var E=null;
function Qa(){if(null===E)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=E;E=null;return a}var G=null,I=0,J=null;function Ra(){var a=J;J=null;return a}function Sa(a){return a._currentValue}
var Wa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:K,useTransition:K,readContext:Sa,useContext:Sa,useReducer:K,useRef:K,useState:K,useInsertionEffect:K,useLayoutEffect:K,useImperativeHandle:K,useEffect:K,useId:Ta,useSyncExternalStore:K,useCacheRefresh:function(){return Ua},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ea;return b},use:Va};
function K(){throw Error("This Hook is not supported in Server Components.");}function Ua(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ta(){if(null===G)throw Error("useId can only be used while React is rendering");var a=G.identifierCount++;return":"+G.identifierPrefix+"S"+a.toString(32)+":"}
function Va(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=I;I+=1;null===J&&(J=[]);return Pa(J,a,b)}if(a.$$typeof===ya)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Xa(){return(new AbortController).signal}function Ya(){var a=w();return a?a.cache:new Map}
var Za={getCacheSignal:function(){var a=Ya(),b=a.get(Xa);void 0===b&&(b=Xa(),a.set(Xa,b));return b},getCacheForType:function(a){var b=Ya(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},$a=Array.isArray;function ab(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function bb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if($a(a))return"[...]";a=ab(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function L(a){if("string"===typeof a)return a;switch(a){case Aa:return"Suspense";case Ba:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case za:return L(a.render);case Ca:return L(a.type);case A:var b=a._payload;a=a._init;try{return L(a(b))}catch(d){}}return""}
function M(a,b){var d=ab(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if($a(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?M(g):bb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===z)e="<"+L(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var h=f[g],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k="object"===typeof k&&null!==k?M(k):
bb(k);h===b?(d=e.length,c=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var cb=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,db=cb.ContextRegistry,N=JSON.stringify,eb=cb.ReactCurrentDispatcher,fb=cb.ReactCurrentCache;function gb(a){console.error(a)}function hb(){}
function ib(a,b,d,c,e,f){if(null!==fb.current&&fb.current!==Za)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=sa;fb.current=Za;var g=new Set,h=[],k=new Set,p={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:g,pingedTasks:h,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,identifierPrefix:e||"",identifierCount:1,onError:void 0===d?gb:d,onPostpone:void 0===f?hb:f,toJSON:function(n,F){return jb(p,this,n,F)}};p.pendingChunks++;b=kb(c);a=lb(p,a,b,g);h.push(a);return p}var O=null;function w(){if(O)return O;if(ua){var a=va.getStore();if(a)return a}return null}var mb={};
function nb(a,b){a.pendingChunks++;var d=lb(a,null,B,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,ob(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===Fa?(pb(a,c.message),qb(a,d.id)):(c=P(a,c),Q(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;ob(a,d)},function(e){d.status=4;e=P(a,e);Q(a,d.id,e);null!==a.destination&&R(a,a.destination)});return d.id}function x(a,b,d){d=N(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=r.encode(b+d+"\n");a.completedHintChunks.push(d);rb(a)}function sb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function tb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:sb}}
function S(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===t)return[z,b,d,e];I=0;J=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:tb(e):e}if("string"===typeof b)return[z,b,d,e];if("symbol"===typeof b)return b===wa?e.children:[z,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===t)return[z,b,d,e];switch(b.$$typeof){case A:var g=
b._init;b=g(b._payload);return S(a,b,d,c,e,f);case za:return a=b.render,I=0,J=f,a(e,void 0);case Ca:return S(a,b.type,d,c,e,f);case xa:return Ma(b._context,e.value),[z,b,d,{value:e.value,children:e.children,__pop:mb}]}}throw Error("Unsupported Server Component type: "+bb(b));}function ob(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return ub(a)},0))}
function lb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return ob(a,e)},thenableState:null};c.add(e);return e}function T(a){return"$"+a.toString(16)}function vb(a,b,d){a=N(d);b=b.toString(16)+":"+a+"\n";return r.encode(b)}
function wb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===z&&"1"===d?"$L"+g.toString(16):T(g);try{var h=a.bundlerConfig,k=c.$$id;g="";var p=h[k];if(p)g=p.name;else{var n=k.lastIndexOf("#");-1!==n&&(g=k.slice(n+1),p=h[k.slice(0,n)]);if(!p)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var F={id:p.id,chunks:p.chunks,name:g,async:!!c.$$async};
a.pendingChunks++;var H=a.nextChunkId++,C=N(F),Pb=H.toString(16)+":I"+C+"\n",Qb=r.encode(Pb);a.completedImportChunks.push(Qb);f.set(e,H);return b[0]===z&&"1"===d?"$L"+H.toString(16):T(H)}catch(Rb){return a.pendingChunks++,b=a.nextChunkId++,d=P(a,Rb),Q(a,b,d),T(b)}}function xb(a,b){a.pendingChunks++;var d=a.nextChunkId++;yb(a,d,b);return d}
function U(a,b,d){a.pendingChunks+=2;var c=a.nextChunkId++,e=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);d=512<d.byteLength?e.slice():e;e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";b=r.encode(b);a.completedRegularChunks.push(b,d);return T(c)}
function jb(a,b,d,c){switch(c){case z:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===z||c.$$typeof===A);)try{switch(c.$$typeof){case z:var e=c;c=S(a,e.type,e.key,e.ref,e.props,null);break;case A:var f=c._init;c=f(c._payload)}}catch(g){d=g===Na?Qa():g;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=lb(a,c,B,a.abortableTasks),c=a.ping,d.then(c,c),a.thenableState=Ra(),"$L"+a.id.toString(16);if(d.$$typeof===Fa)return c=d,a.pendingChunks++,d=a.nextChunkId++,
pb(a,c.message),qb(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=P(a,d);Q(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){if(c.$$typeof===t)return wb(a,b,d,c);if("function"===typeof c.then)return"$@"+nb(a,c).toString(16);if(c.$$typeof===xa)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=vb(a,d,"$P"+c),a.completedRegularChunks.push(c)),T(d);if(c===mb){a=B;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");
c=a.parentValue;a.context._currentValue=c===Da?a.context._defaultValue:c;B=a.parent;return}return c instanceof Map?"$Q"+xb(a,Array.from(c)).toString(16):c instanceof Set?"$W"+xb(a,Array.from(c)).toString(16):c instanceof ArrayBuffer?U(a,"A",new Uint8Array(c)):c instanceof Int8Array?U(a,"C",c):c instanceof Uint8Array?U(a,"c",c):c instanceof Uint8ClampedArray?U(a,"U",c):c instanceof Int16Array?U(a,"S",c):c instanceof Uint16Array?U(a,"s",c):c instanceof Int32Array?U(a,"L",c):c instanceof Uint32Array?
U(a,"l",c):c instanceof Float32Array?U(a,"F",c):c instanceof Float64Array?U(a,"D",c):c instanceof BigInt64Array?U(a,"N",c):c instanceof BigUint64Array?U(a,"m",c):c instanceof DataView?U(a,"V",c):!$a(c)&&(null===c||"object"!==typeof c?a=null:(a=Ga&&c[Ga]||c["@@iterator"],a="function"===typeof a?a:null),a)?Array.from(c):c}if("string"===typeof c){if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,c=r.encode(c),b=c.byteLength,b=d.toString(16)+
":T"+b.toString(16)+",",b=r.encode(b),a.completedRegularChunks.push(b,c),T(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){if(c.$$typeof===t)return wb(a,b,d,c);if(c.$$typeof===u)return d=a.writtenServerReferences,b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,
b={id:c.$$id,bound:b?Promise.resolve(b):null},a=xb(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+M(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+M(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return T(f);
f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+M(b,d));a.pendingChunks++;d=a.nextChunkId++;b=vb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return T(d)}if("bigint"===typeof c)return"$n"+c.toString(10);throw Error("Type "+typeof c+" is not supported in Client Component props."+M(b,d));}
function pb(a,b){a=a.onPostpone;a(b)}function P(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}function zb(a,b){null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}
function qb(a,b){b=b.toString(16)+":P\n";b=r.encode(b);a.completedErrorChunks.push(b)}function Q(a,b,d){d={digest:d};b=b.toString(16)+":E"+N(d)+"\n";b=r.encode(b);a.completedErrorChunks.push(b)}function yb(a,b,d){d=N(d,a.toJSON);b=b.toString(16)+":"+d+"\n";b=r.encode(b);a.completedRegularChunks.push(b)}
function ub(a){var b=eb.current;eb.current=Wa;var d=O;G=O=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++){var f=c[e];a:{var g=a;if(0===f.status){La(f.context);try{var h=f.model;if("object"===typeof h&&null!==h&&h.$$typeof===z){var k=h,p=f.thenableState;f.model=h;h=S(g,k.type,k.key,k.ref,k.props,p);for(f.thenableState=null;"object"===typeof h&&null!==h&&h.$$typeof===z;)k=h,f.model=h,h=S(g,k.type,k.key,k.ref,k.props,null)}yb(g,f.id,h);g.abortableTasks.delete(f);f.status=1}catch(C){var n=
C===Na?Qa():C;if("object"===typeof n&&null!==n)if("function"===typeof n.then){var F=f.ping;n.then(F,F);f.thenableState=Ra();break a}else if(n.$$typeof===Fa){g.abortableTasks.delete(f);f.status=4;pb(g,n.message);qb(g,f.id);break a}g.abortableTasks.delete(f);f.status=4;var H=P(g,n);Q(g,f.id,H)}}}}null!==a.destination&&R(a,a.destination)}catch(C){P(a,C),zb(a,C)}finally{eb.current=b,G=null,O=d}}
function R(a,b){l=new Uint8Array(512);m=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,q(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)q(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,q(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,q(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&(b.enqueue(new Uint8Array(l.buffer,0,m)),l=null,m=0)}0===a.pendingChunks&&
b.close()}function Ab(a){a.flushScheduled=null!==a.destination;ua?setTimeout(function(){return va.run(a,ub,a)},0):setTimeout(function(){return ub(a)},0)}function rb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return R(a,b)},0)}}
function Bb(a,b){try{var d=a.abortableTasks;if(0<d.size){var c=void 0===b?Error("The render was aborted by the server without a reason."):b,e=P(a,c);a.pendingChunks++;var f=a.nextChunkId++;Q(a,f,e,c);d.forEach(function(g){g.status=3;var h=T(f);g=vb(a,g.id,h);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&R(a,a.destination)}catch(g){P(a,g),zb(a,g)}}
function kb(a){if(a){var b=B;La(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];db[e]||(db[e]=aa.createServerContext(e,Da));Ma(db[e],c)}a=B;La(b);return a}return null}function Cb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return{id:c.id,chunks:c.chunks,name:d,async:!1}}
var V=new Map;function Db(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Eb(){}
function Fb(a){for(var b=a.chunks,d=[],c=0;c<b.length;c++){var e=b[c],f=V.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);d.push(f);var g=V.set.bind(V,e,null);f.then(g,Eb);V.set(e,f)}else null!==f&&d.push(f)}return a.async?0===d.length?Db(a.id):Promise.all(d).then(function(){return Db(a.id)}):0<d.length?Promise.all(d):null}
function W(a){var b=globalThis.__next_require__(a.id);if(a.async&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a.name?b:""===a.name?b.__esModule?b.default:b:b[a.name]}function Gb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Gb.prototype=Object.create(Promise.prototype);
Gb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Hb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Ib(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Jb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Ib(d,b)}}function Kb(a,b,d,c,e,f){var g=Cb(a._bundlerConfig,b);a=Fb(g);if(d)d=Promise.all([d,a]).then(function(h){h=h[0];var k=W(g);return k.bind.apply(k,[null].concat(h))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Lb(c,e,f),Mb(c));return null}var X=null,Y=null;
function Hb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Nb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Jb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Gb("resolved_model",c,null,a):new Gb("pending",null,null,a),d.set(b,c));return c}function Lb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Ib(e,c.value))}}function Mb(a){return function(b){return Jb(a,b)}}
function Ob(a,b){a=Z(a,b);"resolved_model"===a.status&&Hb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Sb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Ob(a,c),Kb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Ob(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Ob(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,h){h.startsWith(e)&&f.append(h.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Hb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Lb(c,b,d),Mb(c)),null;default:throw a.reason;}}return c}
function Tb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Sb(e,this,f,g):g}};return e}function Ub(a){Nb(a,Error("Connection closed."))}function Vb(a,b,d){var c=Cb(a,b);a=Fb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}
function Wb(a,b,d){a=Tb(b,d,a);Ub(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=v({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Wb(a,b,e),c=Vb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=Vb(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(g,h){h.startsWith("$ACTION_REF_")&&(g="$ACTION_"+h.slice(12)+":",e=Wb(b,d,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,c,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Tb(b,"",a);Ub(a);return Z(a,0)};
exports.registerClientReference=function(a,b,d){return v(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:u},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=ib(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)Bb(c,e.reason);else{var f=function(){Bb(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){Ab(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===c.destination){c.destination=g;try{R(c,
g)}catch(h){P(c,h),zb(c,h)}}},cancel:function(){}},{highWaterMark:0})};
