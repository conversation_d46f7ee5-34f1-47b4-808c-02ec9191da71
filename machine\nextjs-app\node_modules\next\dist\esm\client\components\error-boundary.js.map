{"version": 3, "sources": ["../../../src/client/components/error-boundary.tsx"], "names": ["React", "usePathname", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "state", "pathname", "previousPathname", "render", "errorStyles", "this", "errorComponent", "reset", "children", "constructor", "setState", "GlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "p", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": "AAAA;AAEA,OAAOA,WAAW,QAAO;AACzB,SAASC,WAAW,QAAQ,eAAc;AAE1C,MAAMC,SAAS;IACbC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,UAAU;QACVC,YAAY;QACZC,YAAY;QACZC,QAAQ;IACV;AACF;AAsBA,OAAO,MAAMC,6BAA6BhB,MAAMiB,SAAS;IASvD,OAAOC,yBAAyBf,KAAY,EAAE;QAC5C,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOgB,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC;;;;;KAKC,GACD,IAAID,MAAME,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMlB,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACPoB,kBAAkBH,MAAME,QAAQ;YAClC;QACF;QACA,OAAO;YACLnB,OAAOkB,MAAMlB,KAAK;YAClBoB,kBAAkBH,MAAME,QAAQ;QAClC;IACF;IAMAE,SAAS;QACP,IAAI,IAAI,CAACH,KAAK,CAAClB,KAAK,EAAE;YACpB,qBACE,0CACG,IAAI,CAACiB,KAAK,CAACK,WAAW,gBACvB,oBAACC,IAAI,CAACN,KAAK,CAACO,cAAc;gBACxBxB,OAAO,IAAI,CAACkB,KAAK,CAAClB,KAAK;gBACvByB,OAAO,IAAI,CAACA,KAAK;;QAIzB;QAEA,OAAO,IAAI,CAACR,KAAK,CAACS,QAAQ;IAC5B;IAjDAC,YAAYV,KAAgC,CAAE;QAC5C,KAAK,CAACA;aA8BRQ,QAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAE5B,OAAO;YAAK;QAC9B;QA/BE,IAAI,CAACkB,KAAK,GAAG;YAAElB,OAAO;YAAMoB,kBAAkB,IAAI,CAACH,KAAK,CAACE,QAAQ;QAAC;IACpE;AA+CF;AAEA,OAAO,SAASU,YAAY,KAAyB;IAAzB,IAAA,EAAE7B,KAAK,EAAkB,GAAzB;IAC1B,MAAM8B,SAA6B9B,yBAAAA,MAAO8B,MAAM;IAChD,qBACE,oBAACC;QAAKC,IAAG;qBACP,oBAACC,6BACD,oBAACC,4BACC,oBAACC;QAAIC,OAAOrC,OAAOC,KAAK;qBACtB,oBAACmC,2BACC,oBAACE;QAAGD,OAAOrC,OAAOS,IAAI;OACnB,AAAC,0BACAsB,CAAAA,SAAS,WAAW,QAAO,IAC5B,2CACCA,CAAAA,SAAS,gBAAgB,iBAAgB,IAC1C,4BAEFA,uBAAS,oBAACQ;QAAEF,OAAOrC,OAAOS,IAAI;OAAG,AAAC,aAAUsB,UAAgB;AAMzE;AAEA,gFAAgF;AAChF,2CAA2C;AAC3C,eAAeD,YAAW;AAE1B;;;CAGC,GAED;;;CAGC,GACD,OAAO,SAASU,cAAc,KAIuB;IAJvB,IAAA,EAC5Bf,cAAc,EACdF,WAAW,EACXI,QAAQ,EAC2C,GAJvB;IAK5B,MAAMP,WAAWrB;IACjB,IAAI0B,gBAAgB;QAClB,qBACE,oBAACX;YACCM,UAAUA;YACVK,gBAAgBA;YAChBF,aAAaA;WAEZI;IAGP;IAEA,qBAAO,0CAAGA;AACZ"}