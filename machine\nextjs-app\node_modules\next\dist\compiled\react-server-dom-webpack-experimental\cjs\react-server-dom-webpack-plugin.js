/**
 * @license React
 * react-server-dom-webpack-plugin.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

'use strict';

'use strict';var r=require("path"),u=require("url"),x=require("neo-async"),A=require("acorn-loose"),B=require("webpack/lib/dependencies/ModuleDependency"),C=require("webpack/lib/dependencies/NullDependency"),D=require("webpack/lib/Template"),E=require("webpack");const F=Array.isArray;class G extends B{constructor(b){super(b)}get type(){return"client-reference"}}const H=require.resolve("../client.browser.js");
class I{constructor(b){this.ssrManifestFilename=this.clientManifestFilename=this.chunkName=this.clientReferences=void 0;if(!b||"boolean"!==typeof b.isServer)throw Error("React Server Plugin: You must specify the isServer option as a boolean.");if(b.isServer)throw Error("TODO: Implement the server compiler.");b.clientReferences?"string"!==typeof b.clientReferences&&F(b.clientReferences)?this.clientReferences=b.clientReferences:this.clientReferences=[b.clientReferences]:this.clientReferences=[{directory:".",
recursive:!0,include:/\.(js|ts|jsx|tsx)$/}];"string"===typeof b.chunkName?(this.chunkName=b.chunkName,/\[(index|request)\]/.test(this.chunkName)||(this.chunkName+="[index]")):this.chunkName="client[index]";this.clientManifestFilename=b.clientManifestFilename||"react-client-manifest.json";this.ssrManifestFilename=b.ssrManifestFilename||"react-ssr-manifest.json"}apply(b){const n=this;let p,t=!1;b.hooks.beforeCompile.tapAsync("React Server Plugin",(e,g)=>{e=e.contextModuleFactory;const l=b.resolverFactory.get("context",
{}),a=b.resolverFactory.get("normal");n.resolveAllClientFiles(b.context,l,a,b.inputFileSystem,e,function(c,d){c?g(c):(p=d,g())})});b.hooks.thisCompilation.tap("React Server Plugin",(e,g)=>{g=g.normalModuleFactory;e.dependencyFactories.set(G,g);e.dependencyTemplates.set(G,new C.Template);e=l=>{l.hooks.program.tap("React Server Plugin",()=>{const a=l.state.module;if(a.resource===H&&(t=!0,p))for(let d=0;d<p.length;d++){const m=p[d];var c=n.chunkName.replace(/\[index\]/g,""+d).replace(/\[request\]/g,
D.toPath(m.userRequest));c=new E.AsyncDependenciesBlock({name:c},null,m.request);c.addDependency(m);a.addBlock(c)}})};g.hooks.parser.for("javascript/auto").tap("HarmonyModulesPlugin",e);g.hooks.parser.for("javascript/esm").tap("HarmonyModulesPlugin",e);g.hooks.parser.for("javascript/dynamic").tap("HarmonyModulesPlugin",e)});b.hooks.make.tap("React Server Plugin",e=>{e.hooks.processAssets.tap({name:"React Server Plugin",stage:E.Compilation.PROCESS_ASSETS_STAGE_REPORT},function(){if(!1===t)e.warnings.push(new E.WebpackError("Client runtime at react-server-dom-webpack/client was not found. React Server Components module map file "+
n.clientManifestFilename+" was not created."));else{var g=new Set((p||[]).map(d=>d.request)),l={},a={};e.chunkGroups.forEach(function(d){function m(k,f){if(g.has(f.resource)&&(f=u.pathToFileURL(f.resource).href,void 0!==f)){const h={};l[f]={id:k,chunks:q,name:"*"};h["*"]={specifier:f,name:"*"};a[k]=h}}const q=d.chunks.map(function(k){return k.id});d.chunks.forEach(function(k){k=e.chunkGraph.getChunkModulesIterable(k);Array.from(k).forEach(function(f){const h=e.chunkGraph.getModuleId(f);m(h,f);f.modules&&
f.modules.forEach(v=>{m(h,v)})})})});var c=JSON.stringify(l,null,2);e.emitAsset(n.clientManifestFilename,new E.sources.RawSource(c,!1));c=JSON.stringify(a,null,2);e.emitAsset(n.ssrManifestFilename,new E.sources.RawSource(c,!1))}})})}resolveAllClientFiles(b,n,p,t,e,g){function l(a){if(-1===a.indexOf("use client"))return!1;let c;try{c=A.parse(a,{ecmaVersion:"2024",sourceType:"module"}).body}catch(d){return!1}for(a=0;a<c.length;a++){const d=c[a];if("ExpressionStatement"!==d.type||!d.directive)break;
if("use client"===d.directive)return!0}return!1}x.map(this.clientReferences,(a,c)=>{"string"===typeof a?c(null,[new G(a)]):n.resolve({},b,a.directory,{},(d,m)=>{if(d)return c(d);e.resolveDependencies(t,{resource:m,resourceQuery:"",recursive:void 0===a.recursive?!0:a.recursive,regExp:a.include,include:void 0,exclude:a.exclude},(q,k)=>{if(q)return c(q);q=k.map(f=>{var h=r.join(m,f.userRequest);h=new G(h);h.userRequest=f.userRequest;return h});x.filter(q,(f,h)=>{p.resolve({},b,f.request,{},(v,y)=>{if(v||
"string"!==typeof y)return h(null,!1);t.readFile(y,"utf-8",(w,z)=>{if(w||"string"!==typeof z)return h(null,!1);w=l(z);h(null,w)})})},c)})})},(a,c)=>{if(a)return g(a);a=[];for(let d=0;d<c.length;d++)a.push.apply(a,c[d]);g(null,a)})}}module.exports=I;
