{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-manifest-plugin.ts"], "names": ["path", "webpack", "sources", "CLIENT_REFERENCE_MANIFEST", "SYSTEM_ENTRYPOINTS", "relative", "getProxiedPluginState", "nonNullable", "WEBPACK_LAYERS", "normalizePagePath", "pluginState", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "getAppPathRequiredChunks", "chunkGroup", "chunks", "map", "requiredChunk", "has", "name", "files", "file", "endsWith", "id", "flat", "filter", "entryNameToGroupName", "entryName", "groupName", "slice", "lastIndexOf", "replace", "test", "mergeManifest", "manifest", "manifestToMerge", "Object", "assign", "clientModules", "ssrModuleMapping", "edgeSSRModuleMapping", "entryCSSFiles", "PLUGIN_NAME", "ClientReferenceManifestPlugin", "constructor", "options", "dev", "appDir", "appDirBase", "dirname", "sep", "Set", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createAsset", "context", "manifestsPerGroup", "Map", "manifestEntryFiles", "chunkGroups", "for<PERSON>ach", "chunkEntryName", "getFiles", "f", "startsWith", "requiredChunks", "recordModule", "mod", "layer", "appPagesBrowser", "resource", "type", "_identifier", "moduleReferences", "moduleIdMapping", "edgeModuleIdMapping", "ssrNamedModuleId", "resourceResolveData", "isAsyncModule", "esmResource", "addClientReference", "exportName", "async", "edgeExportName", "addSSRIdMapping", "chunk", "entryMods", "chunkGraph", "getChunkEntryModulesIterable", "request", "includes", "connections", "moduleGraph", "getOutgoingConnections", "connection", "dependency", "clientEntryMod", "getResolvedModule", "modId", "getModuleId", "module", "concatenatedMod", "concatenatedModId", "push", "get", "pageName", "mergedManifest", "segments", "split", "group", "segment", "json", "JSON", "stringify", "pagePath", "pageBundlePath", "length", "RawSource"], "mappings": "AAAA;;;;;CAKC,GAED,OAAOA,UAAU,OAAM;AACvB,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,yBAAyB,EACzBC,kBAAkB,QACb,gCAA+B;AACtC,SAASC,QAAQ,QAAQ,OAAM;AAC/B,SAASC,qBAAqB,QAAQ,sBAAqB;AAE3D,SAASC,WAAW,QAAQ,4BAA2B;AACvD,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,iBAAiB,QAAQ,oDAAmD;AAerF,MAAMC,cAAcJ,sBAAsB;IACxCK,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IACtBC,sBAAsB,EAAE;AAC1B;AAqCA,SAASC,yBAAyBC,UAA8B;IAC9D,OAAOA,WAAWC,MAAM,CACrBC,GAAG,CAAC,CAACC;QACJ,IAAId,mBAAmBe,GAAG,CAACD,cAAcE,IAAI,IAAI,KAAK;YACpD,OAAO;QACT;QAEA,4DAA4D;QAC5D,+DAA+D;QAC/D,+DAA+D;QAC/D,mCAAmC;QACnC,OAAO;eAAIF,cAAcG,KAAK;SAAC,CAACJ,GAAG,CAAC,CAACK;YACnC,6DAA6D;YAC7D,0BAA0B;YAC1B,IAAI,CAACA,KAAKC,QAAQ,CAAC,QAAQ,OAAO;YAClC,IAAID,KAAKC,QAAQ,CAAC,mBAAmB,OAAO;YAE5C,OAAOL,cAAcM,EAAE,GAAG,MAAMF;QAClC;IACF,GACCG,IAAI,GACJC,MAAM,CAACnB;AACZ;AAEA,8EAA8E;AAC9E,6EAA6E;AAC7E,YAAY;AACZ,+BAA+B;AAC/B,4BAA4B;AAC5B,2CAA2C;AAC3C,0CAA0C;AAC1C,SAASoB,qBAAqBC,SAAiB;IAC7C,IAAIC,YAAYD,UACbE,KAAK,CAAC,GAAGF,UAAUG,WAAW,CAAC,MAC/BC,OAAO,CAAC,aAAa,GACtB,2EAA2E;KAC1EA,OAAO,CAAC,0BAA0B;IAErC,sBAAsB;IACtBH,YAAYA,UACTG,OAAO,CAAC,oBAAoB,QAC5BA,OAAO,CAAC,aAAa;IAExB,kCAAkC;IAClC,MAAO,oBAAoBC,IAAI,CAACJ,WAAY;QAC1CA,YAAYA,UAAUG,OAAO,CAAC,sBAAsB;IACtD;IAEA,OAAOH;AACT;AAEA,SAASK,cACPC,QAAiC,EACjCC,eAAwC;IAExCC,OAAOC,MAAM,CAACH,SAASI,aAAa,EAAEH,gBAAgBG,aAAa;IACnEF,OAAOC,MAAM,CAACH,SAASK,gBAAgB,EAAEJ,gBAAgBI,gBAAgB;IACzEH,OAAOC,MAAM,CACXH,SAASM,oBAAoB,EAC7BL,gBAAgBK,oBAAoB;IAEtCJ,OAAOC,MAAM,CAACH,SAASO,aAAa,EAAEN,gBAAgBM,aAAa;AACrE;AAEA,MAAMC,cAAc;AAEpB,OAAO,MAAMC;IAMXC,YAAYC,OAAgB,CAAE;aAL9BC,MAAsB;QAMpB,IAAI,CAACA,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAGjD,KAAKkD,OAAO,CAAC,IAAI,CAACF,MAAM,IAAIhD,KAAKmD,GAAG;QACtD,IAAI,CAACtC,oBAAoB,GAAG,IAAIuC,IAAI1C,YAAYG,oBAAoB;IACtE;IAEAwC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5Bd,aACA,CAACa,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjC3D,QAAQ4D,YAAY,CAACC,gBAAgB,EACrCJ;YAEFF,YAAYO,mBAAmB,CAACH,GAAG,CACjC3D,QAAQ4D,YAAY,CAACC,gBAAgB,EACrC,IAAI7D,QAAQ4D,YAAY,CAACG,cAAc,CAACC,QAAQ;YAElDT,YAAYD,KAAK,CAACW,aAAa,CAACT,GAAG,CACjC;gBACErC,MAAMuB;gBACN,iEAAiE;gBACjE,0CAA0C;gBAC1CwB,OAAOlE,QAAQmE,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,WAAW,CAACD,QAAQd,aAAaF,SAASkB,OAAO;QAEtE;IAEJ;IAEAD,YACED,MAAqC,EACrCd,WAAgC,EAChCgB,OAAe,EACf;QACA,MAAMC,oBAAoB,IAAIC;QAC9B,MAAMC,qBAA+B,EAAE;QAEvCnB,YAAYoB,WAAW,CAACC,OAAO,CAAC,CAAC9D;YAC/B,mEAAmE;YACnE,IAAIa,YAAY;YAChB,MAAMO,WAAoC;gBACxCK,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,IAAI3B,WAAWK,IAAI,IAAI,YAAYa,IAAI,CAAClB,WAAWK,IAAI,GAAG;gBACxD,sCAAsC;gBACtC,MAAM0D,iBAAiB,AAAC,CAAA,IAAI,CAAC7B,UAAU,GAAGlC,WAAWK,IAAI,AAAD,EAAGY,OAAO,CAChE,UACAhC,KAAKmD,GAAG;gBAEVhB,SAASO,aAAa,CAACoC,eAAe,GAAG/D,WACtCgE,QAAQ,GACRrD,MAAM,CACL,CAACsD,IAAM,CAACA,EAAEC,UAAU,CAAC,wBAAwBD,EAAEzD,QAAQ,CAAC;gBAG5DK,YAAYb,WAAWK,IAAI;YAC7B;YAEA,MAAM8D,iBAAiBpE,yBAAyBC;YAChD,MAAMoE,eAAe,CAAC3D,IAAc4D;oBAyBhCA;gBAxBF,0CAA0C;gBAC1C,IAAIA,IAAIC,KAAK,KAAK7E,eAAe8E,eAAe,EAAE;oBAChD;gBACF;gBAEA,MAAMC,WACJH,IAAII,IAAI,KAAK,qBAETJ,IAAIK,WAAW,CAAC3D,KAAK,CAACsD,IAAIK,WAAW,CAAC1D,WAAW,CAAC,OAAO,KACzDqD,IAAIG,QAAQ;gBAElB,IAAI,CAACA,UAAU;oBACb;gBACF;gBAEA,MAAMG,mBAAmBvD,SAASI,aAAa;gBAC/C,MAAMoD,kBAAkBxD,SAASK,gBAAgB;gBACjD,MAAMoD,sBAAsBzD,SAASM,oBAAoB;gBAEzD,4EAA4E;gBAC5E,6EAA6E;gBAC7E,sBAAsB;gBACtB,IAAIoD,mBAAmBxF,SACrBmE,SACAY,EAAAA,2BAAAA,IAAIU,mBAAmB,qBAAvBV,yBAAyBpF,IAAI,KAAIuF;gBAGnC,IAAI,CAACM,iBAAiBZ,UAAU,CAAC,MAC/BY,mBAAmB,CAAC,EAAE,EAAEA,iBAAiB7D,OAAO,CAAC,OAAO,KAAK,CAAC;gBAEhE,MAAM+D,gBAAgB,IAAI,CAAClF,oBAAoB,CAACM,GAAG,CAACiE,IAAIG,QAAQ;gBAEhE,wEAAwE;gBACxE,oEAAoE;gBACpE,MAAMS,cAAc,0BAA0B/D,IAAI,CAACsD,YAC/CA,SAASvD,OAAO,CACd,2BACA,kBAAkBA,OAAO,CAAC,OAAOhC,KAAKmD,GAAG,KAE3C;gBAEJ,SAAS8C;oBACP,MAAMC,aAAaX;oBACnBpD,SAASI,aAAa,CAAC2D,WAAW,GAAG;wBACnC1E;wBACAJ,MAAM;wBACNJ,QAAQkE;wBACRiB,OAAOJ;oBACT;oBACA,IAAIC,aAAa;wBACf,MAAMI,iBAAiBJ;wBACvB7D,SAASI,aAAa,CAAC6D,eAAe,GACpCjE,SAASI,aAAa,CAAC2D,WAAW;oBACtC;gBACF;gBAEA,SAASG;oBACP,MAAMH,aAAaX;oBACnB,IACE,OAAO7E,YAAYC,eAAe,CAACkF,iBAAiB,KAAK,aACzD;wBACAF,eAAe,CAACnE,GAAG,GAAGmE,eAAe,CAACnE,GAAG,IAAI,CAAC;wBAC9CmE,eAAe,CAACnE,GAAG,CAAC,IAAI,GAAG;4BACzB,GAAGW,SAASI,aAAa,CAAC2D,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvClF,QAAQ,EAAE;4BACVQ,IAAId,YAAYC,eAAe,CAACkF,iBAAiB;wBACnD;oBACF;oBAEA,IACE,OAAOnF,YAAYE,mBAAmB,CAACiF,iBAAiB,KACxD,aACA;wBACAD,mBAAmB,CAACpE,GAAG,GAAGoE,mBAAmB,CAACpE,GAAG,IAAI,CAAC;wBACtDoE,mBAAmB,CAACpE,GAAG,CAAC,IAAI,GAAG;4BAC7B,GAAGW,SAASI,aAAa,CAAC2D,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvClF,QAAQ,EAAE;4BACVQ,IAAId,YAAYE,mBAAmB,CAACiF,iBAAiB;wBACvD;oBACF;gBACF;gBAEAI;gBACAI;gBAEAlE,SAASI,aAAa,GAAGmD;gBACzBvD,SAASK,gBAAgB,GAAGmD;gBAC5BxD,SAASM,oBAAoB,GAAGmD;YAClC;YAEA,0EAA0E;YAC1E,oEAAoE;YACpE,oEAAoE;YACpE,qDAAqD;YACrD,6CAA6C;YAC7C7E,WAAWC,MAAM,CAAC6D,OAAO,CAAC,CAACyB;gBACzB,MAAMC,YACJ/C,YAAYgD,UAAU,CAACC,4BAA4B,CAACH;gBACtD,KAAK,MAAMlB,OAAOmB,UAAW;oBAC3B,IAAInB,IAAIC,KAAK,KAAK7E,eAAe8E,eAAe,EAAE;oBAElD,MAAMoB,UAAU,AAACtB,IAA6BsB,OAAO;oBAErD,IACE,CAACA,WACD,CAACA,QAAQC,QAAQ,CAAC,wCAClB;wBACA;oBACF;oBAEA,MAAMC,cACJpD,YAAYqD,WAAW,CAACC,sBAAsB,CAAC1B;oBAEjD,KAAK,MAAM2B,cAAcH,YAAa;wBACpC,MAAMI,aAAaD,WAAWC,UAAU;wBACxC,IAAI,CAACA,YAAY;wBAEjB,MAAMC,iBAAiBzD,YAAYqD,WAAW,CAACK,iBAAiB,CAC9DF;wBAEF,MAAMG,QAAQ3D,YAAYgD,UAAU,CAACY,WAAW,CAACH;wBAKjD,IAAIE,UAAU,MAAM;4BAClBhC,aAAagC,OAAOF;wBACtB,OAAO;gCAGHF;4BAFF,oEAAoE;4BACpE,IACEA,EAAAA,qBAAAA,WAAWM,MAAM,qBAAjBN,mBAAmBlE,WAAW,CAACzB,IAAI,MAAK,sBACxC;gCACA,MAAMkG,kBAAkBP,WAAWM,MAAM;gCACzC,MAAME,oBACJ/D,YAAYgD,UAAU,CAACY,WAAW,CAACE;gCACrCnC,aAAaoC,mBAAmBN;4BAClC;wBACF;oBACF;gBACF;YACF;YAEA,8EAA8E;YAC9E,iBAAiB;YACjB,sBAAsB;YACtB,IAAI,oBAAoBhF,IAAI,CAACL,YAAY;gBACvC+C,mBAAmB6C,IAAI,CAAC5F,UAAUI,OAAO,CAAC,qBAAqB;YACjE;YAEA,4CAA4C;YAC5C,qBAAqB;YACrB,uBAAuB;YACvB,IAAI,+BAA+BC,IAAI,CAACL,YAAY;gBAClD+C,mBAAmB6C,IAAI,CAAC,IAAI,CAACzE,GAAG,GAAG,kBAAkB;YACvD;YAEA,MAAMlB,YAAYF,qBAAqBC;YACvC,IAAI,CAAC6C,kBAAkBtD,GAAG,CAACU,YAAY;gBACrC4C,kBAAkBb,GAAG,CAAC/B,WAAW,EAAE;YACrC;YACA4C,kBAAkBgD,GAAG,CAAC5F,WAAY2F,IAAI,CAACrF;QACzC;QAEA,+BAA+B;QAC/B,KAAK,MAAMuF,YAAY/C,mBAAoB;YACzC,MAAMgD,iBAA0C;gBAC9CnF,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,MAAMkF,WAAW;mBAAIjG,qBAAqB+F,UAAUG,KAAK,CAAC;gBAAM;aAAO;YACvE,IAAIC,QAAQ;YACZ,KAAK,MAAMC,WAAWH,SAAU;gBAC9B,KAAK,MAAMzF,YAAYsC,kBAAkBgD,GAAG,CAACK,UAAU,EAAE,CAAE;oBACzD5F,cAAcyF,gBAAgBxF;gBAChC;gBACA2F,SAAS,AAACA,CAAAA,QAAQ,MAAM,EAAC,IAAKC;YAChC;YAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACP;YAE5B,MAAMQ,WAAWT,SAAS1F,OAAO,CAAC,QAAQ;YAC1C,MAAMoG,iBAAiB3H,kBAAkB0H,SAASrG,KAAK,CAAC,MAAMuG,MAAM;YACpE/D,MAAM,CACJ,eAAe8D,iBAAiB,MAAMjI,4BAA4B,MACnE,GAAG,IAAID,QAAQoI,SAAS,CACvB,CAAC,oFAAoF,EAAEL,KAAKC,SAAS,CACnGC,SAASrG,KAAK,CAAC,MAAMuG,MAAM,GAC3B,EAAE,EAAEL,KAAK,CAAC;YAGd,IAAIG,aAAa,iBAAiB;gBAChC,kEAAkE;gBAClE7D,MAAM,CAAC,2BAA2BnE,4BAA4B,MAAM,GAClE,IAAID,QAAQoI,SAAS,CACnB,CAAC,oFAAoF,EAAEL,KAAKC,SAAS,CACnG,eACA,EAAE,EAAEF,KAAK,CAAC;YAElB;QACF;QAEAtH,YAAYG,oBAAoB,GAAG,EAAE;IACvC;AACF"}