{"version": 3, "sources": ["../../../src/build/output/log.ts"], "names": ["chalk", "prefixes", "wait", "white", "bold", "error", "red", "warn", "yellow", "ready", "info", "event", "green", "trace", "magenta", "LOGGING_METHOD", "log", "prefixedLog", "prefixType", "message", "undefined", "length", "shift", "consoleMethod", "prefix", "console", "bootstrap", "warnOnceMessages", "Set", "warnOnce", "has", "add", "join"], "mappings": "AAAA,OAAOA,WAAW,kBAAiB;AAEnC,OAAO,MAAMC,WAAW;IACtBC,MAAMF,MAAMG,KAAK,CAACH,MAAMI,IAAI,CAAC;IAC7BC,OAAOL,MAAMM,GAAG,CAACN,MAAMI,IAAI,CAAC;IAC5BG,MAAMP,MAAMQ,MAAM,CAACR,MAAMI,IAAI,CAAC;IAC9BK,OAAOT,MAAMI,IAAI,CAAC;IAClBM,MAAMV,MAAMG,KAAK,CAACH,MAAMI,IAAI,CAAC;IAC7BO,OAAOX,MAAMY,KAAK,CAACZ,MAAMI,IAAI,CAAC;IAC9BS,OAAOb,MAAMc,OAAO,CAACd,MAAMI,IAAI,CAAC;AAClC,EAAU;AAEV,MAAMW,iBAAiB;IACrBC,KAAK;IACLT,MAAM;IACNF,OAAO;AACT;AAEA,SAASY,YAAYC,UAAiC,EAAE,GAAGC,OAAc;IACvE,IAAI,AAACA,CAAAA,OAAO,CAAC,EAAE,KAAK,MAAMA,OAAO,CAAC,EAAE,KAAKC,SAAQ,KAAMD,QAAQE,MAAM,KAAK,GAAG;QAC3EF,QAAQG,KAAK;IACf;IAEA,MAAMC,gBACJL,cAAcH,iBACVA,cAAc,CAACG,WAA0C,GACzD;IAEN,MAAMM,SAASvB,QAAQ,CAACiB,WAAW;IACnC,+DAA+D;IAC/D,IAAIC,QAAQE,MAAM,KAAK,GAAG;QACxBI,OAAO,CAACF,cAAc,CAAC;IACzB,OAAO;QACLE,OAAO,CAACF,cAAc,CAAC,MAAMC,WAAWL;IAC1C;AACF;AAEA,OAAO,SAASO,UAAU,GAAGP,OAAc;IACzCM,QAAQT,GAAG,CAAC,QAAQG;AACtB;AAEA,OAAO,SAASjB,KAAK,GAAGiB,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASd,MAAM,GAAGc,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAASZ,KAAK,GAAGY,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASV,MAAM,GAAGU,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAAST,KAAK,GAAGS,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASR,MAAM,GAAGQ,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAASN,MAAM,GAAGM,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,MAAMQ,mBAAmB,IAAIC;AAC7B,OAAO,SAASC,SAAS,GAAGV,OAAc;IACxC,IAAI,CAACQ,iBAAiBG,GAAG,CAACX,OAAO,CAAC,EAAE,GAAG;QACrCQ,iBAAiBI,GAAG,CAACZ,QAAQa,IAAI,CAAC;QAElCzB,QAAQY;IACV;AACF"}