{"version": 3, "sources": ["../../../src/client/components/static-generation-bailout.ts"], "names": ["DynamicServerError", "staticGenerationAsyncStorage", "StaticGenBailoutError", "Error", "code", "formatErrorMessage", "reason", "opts", "dynamic", "link", "suffix", "staticGenerationBailout", "staticGenerationStore", "getStore", "forceStatic", "dynamicShouldError", "revalidate", "isStaticGeneration", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,4BAA4B,QAAQ,6CAA4C;AAEzF,MAAMC,8BAA8BC;;;aAClCC,OAAO;;AACT;AASA,SAASC,mBAAmBC,MAAc,EAAEC,IAAkB;IAC5D,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGF,QAAQ,CAAC;IACnC,MAAMG,SAASD,OAAO,AAAC,0BAAuBA,OAAS;IACvD,OAAO,AAAC,SACND,CAAAA,UAAU,AAAC,uBAAqBA,UAAQ,OAAO,EAAC,IACjD,uDAAqDF,SAAO,OAAKI;AACpE;AAEA,OAAO,MAAMC,0BAAmD,CAC9DL,QACAC;IAEA,MAAMK,wBAAwBX,6BAA6BY,QAAQ;IAEnE,IAAID,yCAAAA,sBAAuBE,WAAW,EAAE;QACtC,OAAO;IACT;IAEA,IAAIF,yCAAAA,sBAAuBG,kBAAkB,EAAE;YAEIR;QADjD,MAAM,IAAIL,sBACRG,mBAAmBC,QAAQ;YAAE,GAAGC,IAAI;YAAEC,SAASD,CAAAA,gBAAAA,wBAAAA,KAAMC,OAAO,YAAbD,gBAAiB;QAAQ;IAE5E;IAEA,IAAIK,uBAAuB;QACzBA,sBAAsBI,UAAU,GAAG;IACrC;IAEA,IAAIJ,yCAAAA,sBAAuBK,kBAAkB,EAAE;QAC7C,MAAMC,MAAM,IAAIlB,mBACdK,mBAAmBC,QAAQ;YACzB,GAAGC,IAAI;YACP,uEAAuE;YACvE,8EAA8E;YAC9EE,MAAM;QACR;QAEFG,sBAAsBO,uBAAuB,GAAGb;QAChDM,sBAAsBQ,iBAAiB,GAAGF,IAAIG,KAAK;QAEnD,MAAMH;IACR;IAEA,OAAO;AACT,EAAC"}