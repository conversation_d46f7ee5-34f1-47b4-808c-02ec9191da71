{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-font-manifest-plugin.ts"], "names": ["webpack", "sources", "getRouteFromEntrypoint", "NEXT_FONT_MANIFEST", "traverseModules", "path", "PLUGIN_NAME", "getPreloadedFontFiles", "fontFiles", "filter", "file", "test", "getPageIsUsingSizeAdjust", "some", "includes", "NextFontManifestPlugin", "constructor", "options", "appDir", "apply", "compiler", "hooks", "make", "tap", "compilation", "processAssets", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "assets", "nextFontManifest", "pages", "app", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "appDirBase", "dirname", "sep", "mod", "_chunk", "chunkGroup", "request", "buildInfo", "chunkEntryName", "replace", "modAssets", "Object", "keys", "preloadedFontFiles", "length", "push", "startsWith", "entrypoint", "entrypoints", "values", "pagePath", "chunks", "flatMap", "chunk", "auxiliaryFiles", "manifest", "JSON", "stringify", "RawSource"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,kBAAkB,QAAQ,gCAA+B;AAClE,SAASC,eAAe,QAAQ,WAAU;AAC1C,OAAOC,UAAU,OAAM;AAYvB,MAAMC,cAAc;AAEpB;;;;;;CAMC,GACD,SAASC,sBAAsBC,SAAmB;IAChD,OAAOA,UAAUC,MAAM,CAAC,CAACC,OACvB,iCAAiCC,IAAI,CAACD;AAE1C;AAEA;;;;CAIC,GACD,SAASE,yBAAyBJ,SAAmB;IACnD,OAAOA,UAAUK,IAAI,CAAC,CAACH,OAASA,KAAKI,QAAQ,CAAC;AAChD;AAEA;;;;;;;;;;;;;CAaC,GACD,OAAO,MAAMC;IAGXC,YAAYC,OAAuC,CAAE;QACnD,IAAI,CAACC,MAAM,GAAGD,QAAQC,MAAM;IAC9B;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACjB,aAAa,CAACkB;YACpC,4GAA4G;YAC5GA,YAAYH,KAAK,CAACI,aAAa,CAACF,GAAG,CACjC;gBACEG,MAAMpB;gBACNqB,OAAO3B,QAAQ4B,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACC;gBACC,MAAMC,mBAAqC;oBACzCC,OAAO,CAAC;oBACRC,KAAK,CAAC;oBACNC,oBAAoB;oBACpBC,sBAAsB;gBACxB;gBAEA,IAAI,IAAI,CAACjB,MAAM,EAAE;oBACf,MAAMkB,aAAa/B,KAAKgC,OAAO,CAAC,IAAI,CAACnB,MAAM,IAAIb,KAAKiC,GAAG;oBAEvD,8FAA8F;oBAC9FlC,gBACEoB,aACA,CAACe,KAAKC,QAAQC;4BACRF;wBAAJ,IAAIA,wBAAAA,eAAAA,IAAKG,OAAO,qBAAZH,aAAczB,QAAQ,CAAC,gCAAgC;gCACpDyB;4BAAL,IAAI,GAACA,iBAAAA,IAAII,SAAS,qBAAbJ,eAAeT,MAAM,GAAE;4BAE5B,MAAMc,iBAAiB,AAACR,CAAAA,aAAaK,WAAWf,IAAI,AAAD,EAAGmB,OAAO,CAC3D,UACAxC,KAAKiC,GAAG;4BAGV,MAAMQ,YAAYC,OAAOC,IAAI,CAACT,IAAII,SAAS,CAACb,MAAM;4BAClD,MAAMtB,YAAsBsC,UAAUrC,MAAM,CAAC,CAACC,OAC5C,8BAA8BC,IAAI,CAACD;4BAGrC,kDAAkD;4BAClD,IAAI,CAACqB,iBAAiBG,kBAAkB,EAAE;gCACxCH,iBAAiBG,kBAAkB,GACjCtB,yBAAyBJ;4BAC7B;4BAEA,MAAMyC,qBAAqB1C,sBAAsBC;4BAEjD,2DAA2D;4BAC3D,sDAAsD;4BACtD,uGAAuG;4BACvG,IAAIA,UAAU0C,MAAM,GAAG,GAAG;gCACxB,IAAI,CAACnB,iBAAiBE,GAAG,CAACW,eAAe,EAAE;oCACzCb,iBAAiBE,GAAG,CAACW,eAAe,GAAG,EAAE;gCAC3C;gCACAb,iBAAiBE,GAAG,CAACW,eAAe,CAACO,IAAI,IACpCF;4BAEP;wBACF;oBACF,GACA,CAACR;4BAEUA;wBADT,qDAAqD;wBACrD,OAAO,CAAC,GAACA,mBAAAA,WAAWf,IAAI,qBAAfe,iBAAiBW,UAAU,CAAC;oBACvC;gBAEJ;gBAEA,kDAAkD;gBAClD,KAAK,MAAMC,cAAc7B,YAAY8B,WAAW,CAACC,MAAM,GAAI;oBACzD,MAAMC,WAAWtD,uBAAuBmD,WAAW3B,IAAI;oBAEvD,IAAI,CAAC8B,UAAU;wBACb;oBACF;oBAEA,6DAA6D;oBAC7D,MAAMhD,YAAsB6C,WAAWI,MAAM,CAC1CC,OAAO,CAAC,CAACC,QAAe;+BAAIA,MAAMC,cAAc;yBAAC,EACjDnD,MAAM,CAAC,CAACC,OACP,8BAA8BC,IAAI,CAACD;oBAGvC,kDAAkD;oBAClD,IAAI,CAACqB,iBAAiBI,oBAAoB,EAAE;wBAC1CJ,iBAAiBI,oBAAoB,GACnCvB,yBAAyBJ;oBAC7B;oBAEA,MAAMyC,qBAAqB1C,sBAAsBC;oBAEjD,0DAA0D;oBAC1D,sDAAsD;oBACtD,uGAAuG;oBACvG,IAAIA,UAAU0C,MAAM,GAAG,GAAG;wBACxBnB,iBAAiBC,KAAK,CAACwB,SAAS,GAAGP;oBACrC;gBACF;gBAEA,MAAMY,WAAWC,KAAKC,SAAS,CAAChC,kBAAkB;gBAClD,2BAA2B;gBAC3BD,MAAM,CAAC,CAAC,OAAO,EAAE3B,mBAAmB,GAAG,CAAC,CAAC,GAAG,IAAIF,QAAQ+D,SAAS,CAC/D,CAAC,0BAA0B,EAAEF,KAAKC,SAAS,CAACF,UAAU,CAAC;gBAEzD,6BAA6B;gBAC7B/B,MAAM,CAAC,CAAC,OAAO,EAAE3B,mBAAmB,KAAK,CAAC,CAAC,GAAG,IAAIF,QAAQ+D,SAAS,CACjEH;YAEJ;QAEJ;QACA;IACF;AACF"}