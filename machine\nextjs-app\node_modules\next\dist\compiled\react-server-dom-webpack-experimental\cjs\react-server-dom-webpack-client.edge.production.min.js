/**
 * @license React
 * react-server-dom-webpack-client.edge.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var p=require("react-dom"),q=require("react"),r={stream:!0};function u(a,b){if(a){var c=a[b.id];if(a=c[b.name])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b.id+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b.name}return{id:a.id,chunks:a.chunks,name:c,async:!!b.async}}return b}var v=new Map;
function x(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function y(){}
function z(a){for(var b=a.chunks,c=[],d=0;d<b.length;d++){var e=b[d],h=v.get(e);if(void 0===h){h=globalThis.__next_chunk_load__(e);c.push(h);var f=v.set.bind(v,e,null);h.then(f,y);v.set(e,h)}else null!==h&&c.push(h)}return a.async?0===c.length?x(a.id):Promise.all(c).then(function(){return x(a.id)}):0<c.length?Promise.all(c):null}
var A=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),aa=Symbol.for("react.lazy"),ba=Symbol.for("react.default_value"),ca=Symbol.for("react.postpone"),C=Symbol.iterator;function da(a){if(null===a||"object"!==typeof a)return null;a=C&&a[C]||a["@@iterator"];return"function"===typeof a?a:null}var ea=Array.isArray,D=new WeakMap;
function fa(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ha(a,b,c,d){function e(k,g){if(null===g)return null;if("object"===typeof g){if("function"===typeof g.then){null===l&&(l=new FormData);f++;var n=h++;g.then(function(m){m=JSON.stringify(m,e);var t=l;t.append(b+n,m);f--;0===f&&c(t)},function(m){d(m)});return"$@"+n.toString(16)}if(g instanceof FormData){null===l&&(l=new FormData);var w=l;k=h++;var E=b+k+"_";g.forEach(function(m,t){w.append(E+t,m)});return"$K"+k.toString(16)}return g instanceof Map?(g=JSON.stringify(Array.from(g),e),null===l&&
(l=new FormData),k=h++,l.append(b+k,g),"$Q"+k.toString(16)):g instanceof Set?(g=JSON.stringify(Array.from(g),e),null===l&&(l=new FormData),k=h++,l.append(b+k,g),"$W"+k.toString(16)):!ea(g)&&da(g)?Array.from(g):g}if("string"===typeof g){if("Z"===g[g.length-1]&&this[k]instanceof Date)return"$D"+g;g="$"===g[0]?"$"+g:g;return g}if("boolean"===typeof g)return g;if("number"===typeof g)return fa(g);if("undefined"===typeof g)return"$undefined";if("function"===typeof g){g=D.get(g);if(void 0!==g)return g=JSON.stringify(g,
e),null===l&&(l=new FormData),k=h++,l.set(b+k,g),"$F"+k.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof g){k=g.description;if(Symbol.for(k)!==g)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+(g.description+") cannot be found among global symbols."));return"$S"+k}if("bigint"===typeof g)return"$n"+
g.toString(10);throw Error("Type "+typeof g+" is not supported as an argument to a Server Function.");}var h=1,f=0,l=null;a=JSON.stringify(a,e);null===l?c(a):(l.set(b+"0",a),0===f&&c(l))}var F=new WeakMap;function ia(a){var b,c,d=new Promise(function(e,h){b=e;c=h});ha(a,"",function(e){if("string"===typeof e){var h=new FormData;h.append("0",e);e=h}d.status="fulfilled";d.value=e;b(e)},function(e){d.status="rejected";d.reason=e;c(e)});return d}
function ja(a){var b=D.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=F.get(b);c||(c=ia(b),F.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(e,h){d.append("$ACTION_"+a+":"+h,e)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function ka(a,b){var c=D.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(e){d.status="fulfilled";d.value=e},function(e){d.status="rejected";d.reason=e})),d;}}
function G(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:ja},$$IS_SIGNATURE_EQUAL:{value:ka},bind:{value:la}});D.set(a,b)}var ma=Function.prototype.bind,na=Array.prototype.slice;function la(){var a=ma.apply(this,arguments),b=D.get(this);if(b){var c=na.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(e){return e.concat(c)}):Promise.resolve(c);G(a,{id:b.id,bound:d})}return a}
function oa(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}G(c,{id:a,bound:null});return c}var H=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function I(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}I.prototype=Object.create(Promise.prototype);
I.prototype.then=function(a,b){switch(this.status){case "resolved_model":J(this);break;case "resolved_module":K(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function pa(a){switch(a.status){case "resolved_model":J(a);break;case "resolved_module":K(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function L(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function M(a,b,c){switch(a.status){case "fulfilled":L(b,a.value);break;case "pending":case "blocked":a.value=b;a.reason=c;break;case "rejected":c&&L(c,a.reason)}}
function N(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&L(c,b)}}function O(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(K(a),M(a,c,d))}}var P=null,Q=null;
function J(a){var b=P,c=Q;P=a;Q=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==Q&&0<Q.deps?(Q.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(e){a.status="rejected",a.reason=e}finally{P=b,Q=c}}
function K(a){try{var b=a.value,c=globalThis.__next_require__(b.id);if(b.async&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b.name?c:""===b.name?c.__esModule?c.default:c:c[b.name];a.status="fulfilled";a.value=d}catch(e){a.status="rejected",a.reason=e}}function R(a,b){a._chunks.forEach(function(c){"pending"===c.status&&N(c,b)})}function S(a,b){var c=a._chunks,d=c.get(b);d||(d=new I("pending",null,null,a),c.set(b,d));return d}
function qa(a,b,c){if(Q){var d=Q;d.deps++}else d=Q={deps:1,value:null};return function(e){b[c]=e;d.deps--;0===d.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=d.value,null!==e&&L(e,d.value))}}function ra(a){return function(b){return N(a,b)}}
function sa(a,b){function c(){var e=Array.prototype.slice.call(arguments),h=b.bound;return h?"fulfilled"===h.status?d(b.id,h.value.concat(e)):Promise.resolve(h).then(function(f){return d(b.id,f.concat(e))}):d(b.id,e)}var d=a._callServer;G(c,b);return c}function T(a,b){a=S(a,b);switch(a.status){case "resolved_model":J(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ta(a,b,c,d){if("$"===d[0]){if("$"===d)return B;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=S(a,b),{$$typeof:aa,_payload:a,_init:pa};case "@":return b=parseInt(d.slice(2),16),S(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),H[a]||(H[a]=q.createServerContext(a,ba)),H[a].Provider;case "F":return b=parseInt(d.slice(2),16),b=T(a,b),sa(a,b);case "Q":return b=parseInt(d.slice(2),16),a=T(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),
16),a=T(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=S(a,d);switch(a.status){case "resolved_model":J(a);break;case "resolved_module":K(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=P,a.then(qa(d,b,c),ra(d)),null;default:throw a.reason;}}}return d}
function ua(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function U(a,b){var c=new Map;a={_bundlerConfig:a,_callServer:void 0!==b?b:ua,_chunks:c,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=va(a);return a}function V(a,b,c){a._chunks.set(b,new I("fulfilled",c,null,a))}
function wa(a,b,c){var d=a._chunks,e=d.get(b);c=JSON.parse(c,a._fromJSON);var h=u(a._bundlerConfig,c);if(c=z(h)){if(e){var f=e;f.status="blocked"}else f=new I("blocked",null,null,a),d.set(b,f);c.then(function(){return O(f,h)},function(l){return N(f,l)})}else e?O(e,h):d.set(b,new I("resolved_module",h,null,a))}function W(a,b){for(var c=a.length,d=b.length,e=0;e<c;e++)d+=a[e].byteLength;d=new Uint8Array(d);for(var h=e=0;h<c;h++){var f=a[h];d.set(f,e);e+=f.byteLength}d.set(b,e);return d}
function X(a,b,c,d,e,h){c=0===c.length&&0===d.byteOffset%h?d:W(c,d);e=new e(c.buffer,c.byteOffset,c.byteLength/h);V(a,b,e)}
function xa(a,b,c,d,e){switch(c){case 65:V(a,b,W(d,e).buffer);return;case 67:X(a,b,d,e,Int8Array,1);return;case 99:V(a,b,0===d.length?e:W(d,e));return;case 85:X(a,b,d,e,Uint8ClampedArray,1);return;case 83:X(a,b,d,e,Int16Array,2);return;case 115:X(a,b,d,e,Uint16Array,2);return;case 76:X(a,b,d,e,Int32Array,4);return;case 108:X(a,b,d,e,Uint32Array,4);return;case 70:X(a,b,d,e,Float32Array,4);return;case 68:X(a,b,d,e,Float64Array,8);return;case 78:X(a,b,d,e,BigInt64Array,8);return;case 109:X(a,b,d,e,BigUint64Array,
8);return;case 86:X(a,b,d,e,DataView,1);return}for(var h=a._stringDecoder,f="",l=0;l<d.length;l++)f+=h.decode(d[l],r);f+=h.decode(e);switch(c){case 73:wa(a,b,f);break;case 72:b=f[0];f=f.slice(1);a=JSON.parse(f,a._fromJSON);if(f=A.current)switch(b){case "D":f.prefetchDNS(a);break;case "C":"string"===typeof a?f.preconnect(a):f.preconnect(a[0],a[1]);break;case "L":b=a[0];c=a[1];3===a.length?f.preload(b,c,a[2]):f.preload(b,c);break;case "m":"string"===typeof a?f.preloadModule(a):f.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?f.preinitStyle(a):f.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?f.preinitScript(a):f.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?f.preinitModuleScript(a):f.preinitModuleScript(a[0],a[1])}break;case 69:c=JSON.parse(f).digest;f=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
f.stack="Error: "+f.message;f.digest=c;c=a._chunks;(d=c.get(b))?N(d,f):c.set(b,new I("rejected",null,f,a));break;case 84:a._chunks.set(b,new I("fulfilled",f,null,a));break;case 80:f=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");f.$$typeof=ca;f.stack="Error: "+f.message;c=a._chunks;(d=c.get(b))?N(d,f):c.set(b,new I("rejected",null,f,a));break;default:d=a._chunks,(c=d.get(b))?"pending"===c.status&&(a=c.value,b=c.reason,c.status=
"resolved_model",c.value=f,null!==a&&(J(c),M(c,a,b))):d.set(b,new I("resolved_model",f,null,a))}}function va(a){return function(b,c){return"string"===typeof c?ta(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===B?{$$typeof:B,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function Y(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
function Z(a,b){function c(h){var f=h.value;if(h.done)R(a,Error("Connection closed."));else{var l=0,k=a._rowState;h=a._rowID;for(var g=a._rowTag,n=a._rowLength,w=a._buffer,E=f.length;l<E;){var m=-1;switch(k){case 0:m=f[l++];58===m?k=1:h=h<<4|(96<m?m-87:m-48);continue;case 1:k=f[l];84===k||65===k||67===k||99===k||85===k||83===k||115===k||76===k||108===k||70===k||68===k||78===k||109===k||86===k?(g=k,k=2,l++):64<k&&91>k?(g=k,k=3,l++):(g=0,k=3);continue;case 2:m=f[l++];44===m?k=4:n=n<<4|(96<m?m-87:m-
48);continue;case 3:m=f.indexOf(10,l);break;case 4:m=l+n,m>f.length&&(m=-1)}var t=f.byteOffset+l;if(-1<m)n=new Uint8Array(f.buffer,t,m-l),xa(a,h,g,w,n),l=m,3===k&&l++,n=h=g=k=0,w.length=0;else{f=new Uint8Array(f.buffer,t,f.byteLength-l);w.push(f);n-=f.byteLength;break}}a._rowState=k;a._rowID=h;a._rowTag=g;a._rowLength=n;return e.read().then(c).catch(d)}}function d(h){R(a,h)}var e=b.getReader();e.read().then(c).catch(d)}
exports.createFromFetch=function(a,b){var c=U(b&&b.moduleMap?b.moduleMap:null,Y);a.then(function(d){Z(c,d.body)},function(d){R(c,d)});return S(c,0)};exports.createFromReadableStream=function(a,b){b=U(b&&b.moduleMap?b.moduleMap:null,Y);Z(b,a);return S(b,0)};exports.createServerReference=function(a){return oa(a,Y)};
