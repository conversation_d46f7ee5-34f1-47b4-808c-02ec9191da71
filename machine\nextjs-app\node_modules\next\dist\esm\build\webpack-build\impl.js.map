{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "names": ["chalk", "formatWebpackMessages", "nonNullable", "COMPILER_NAMES", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "PHASE_PRODUCTION_BUILD", "runCompiler", "Log", "getBaseWebpackConfig", "loadProjectInfo", "TelemetryPlugin", "NextBuildContext", "resumePluginState", "getPluginState", "createEntrypoints", "loadConfig", "trace", "WEBPACK_LAYERS", "TraceEntryPointsPlugin", "pagesPluginModule", "origDebug", "debug", "isTelemetryPlugin", "plugin", "isTraceEntryPointsPlugin", "webpackBuildImpl", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "dir", "config", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "dev", "Promise", "all", "middlewareMatchers", "compilerType", "client", "server", "edgeServer", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "warn", "process", "hrtime", "clientResult", "serverResult", "edgeServerResult", "inputFileSystem", "pluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "import", "layer", "appPagesBrowser", "dependOn", "purge", "concat", "filter", "traceFn", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "red", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "buildSpinner", "stopAndPersist", "event", "duration", "turbotraceContext", "serializedPagesManifestEntries", "edgeServerPages", "edgeServerAppPaths", "nodeServerPages", "nodeServerAppPaths", "worker<PERSON>ain", "workerData", "Object", "assign", "buildContext", "keys", "entriesTrace", "entryNameMap", "depModArray", "entryEntries", "Array", "from", "entries"], "mappings": "AACA,OAAOA,WAAW,2BAA0B;AAC5C,OAAOC,2BAA2B,yDAAwD;AAC1F,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SACEC,cAAc,EACdC,oCAAoC,EACpCC,oBAAoB,EACpBC,sBAAsB,QAEjB,6BAA4B;AACnC,SAASC,WAAW,QAAQ,cAAa;AACzC,YAAYC,SAAS,gBAAe;AACpC,OAAOC,wBAAwBC,eAAe,QAAQ,oBAAmB;AAEzE,SAASC,eAAe,QAAQ,sCAAqC;AACrE,SACEC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,QACT,mBAAkB;AACzB,SAASC,iBAAiB,QAAQ,aAAY;AAC9C,OAAOC,gBAAgB,sBAAqB;AAC5C,SAASC,KAAK,QAAQ,cAAa;AACnC,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SACEC,sBAAsB,QAEjB,mDAAkD;AAEzD,YAAYC,uBAAuB,2CAA0C;AAE7E,OAAOC,eAAe,2BAA0B;AAEhD,MAAMC,QAAQD,UAAU;AAcxB,SAASE,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBb;AAC3B;AAEA,SAASc,yBACPD,MAAe;IAEf,OAAOA,kBAAkBL;AAC3B;AAEA,OAAO,eAAeO,iBACpBC,YAA4C;QA0MT,uBAIJ;IAvM/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBrB,iBAAiBqB,aAAa;IACpD,MAAMC,MAAMtB,iBAAiBsB,GAAG;IAChC,MAAMC,SAASvB,iBAAiBuB,MAAM;IAEtC,MAAMC,iBAAiBH,cAAcI,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAML,cACvBI,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZxB,kBAAkB;YAChByB,SAAS5B,iBAAiB4B,OAAO;YACjCL,QAAQA;YACRM,UAAU7B,iBAAiB8B,cAAc;YACzCC,OAAO;YACPC,SAASV;YACTW,gBAAgBV,OAAOU,cAAc;YACrCC,UAAUlC,iBAAiBkC,QAAQ;YACnCC,QAAQnC,iBAAiBmC,MAAM;YAC/BC,OAAOpC,iBAAiBqC,WAAW;YACnCC,UAAUtC,iBAAiBuC,cAAc;YACzCC,aAAaxC,iBAAiByC,YAAY;YAC1CC,WAAW1C,iBAAiB2C,eAAe;YAC3CC,wBAAwB5C,iBAAiB4C,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVlB,SAAS5B,iBAAiB4B,OAAO;QACjCL,QAAQA;QACRY,QAAQnC,iBAAiBmC,MAAM;QAC/BD,UAAUlC,iBAAiBkC,QAAQ;QACnCa,UAAU/C,iBAAiB+C,QAAQ;QACnCC,kBAAkBhD,iBAAiBgD,gBAAgB;QACnDC,mBAAmBjD,iBAAiBiD,iBAAiB;QACrDC,0BAA0BlD,iBAAiBkD,wBAAwB;QACnEC,YAAYnD,iBAAiBmD,UAAU;QACvCC,qBAAqBpD,iBAAiBoD,mBAAmB;QACzDC,eAAerD,iBAAiBqD,aAAa;QAC7CC,6BAA6BtD,iBAAiBsD,2BAA2B;QACzEC,qBAAqBvD,iBAAiBuD,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMhC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAM8B,OAAO,MAAM3D,gBAAgB;YACjCwB;YACAC,QAAQsB,qBAAqBtB,MAAM;YACnCmC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjB/D,qBAAqByB,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBgB,oBAAoBnC,YAAYmC,kBAAkB;gBAClDrC;gBACAsC,cAAcvE,eAAewE,MAAM;gBACnCrC,aAAaA,YAAYqC,MAAM;gBAC/B,GAAGN,IAAI;YACT;YACA5D,qBAAqByB,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAqC,oBAAoBnC,YAAYmC,kBAAkB;gBAClDC,cAAcvE,eAAeyE,MAAM;gBACnCtC,aAAaA,YAAYsC,MAAM;gBAC/B,GAAGP,IAAI;YACT;YACA5D,qBAAqByB,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAqC,oBAAoBnC,YAAYmC,kBAAkB;gBAClDC,cAAcvE,eAAe0E,UAAU;gBACvCvC,aAAaA,YAAYuC,UAAU;gBACnC,GAAGR,IAAI;YACT;SACD;IACH;IAEF,MAAMS,eAAeV,OAAO,CAAC,EAAE;IAC/B,MAAMW,eAAeX,OAAO,CAAC,EAAE;IAC/B,MAAMY,aAAaZ,OAAO,CAAC,EAAE;IAE7B,IACEU,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACA5E,IAAI6E,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEArD,oBAAoBsD,QAAQC,MAAM;IAElCjE,MAAM,CAAC,iBAAiB,CAAC,EAAEK;IAC3B,+EAA+E;IAC/E,MAAMS,eAAeG,YAAY,CAAC;QAChC,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIiD,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIC;QAEJ,IAAI,CAAChE,gBAAgBA,iBAAiB,UAAU;YAC7C,CAAC8D,cAAcE,gBAAgB,GAAG,MAAMpF,YAAYwE,cAAc;gBACjE3C;gBACAuD;YACF;YACArE,MAAM,iBAAiBmE;QACzB;QAEA,IAAI,CAAC9D,gBAAgBA,iBAAiB,eAAe;YAClD,CAAC+D,kBAAkBC,gBAAgB,GAAGX,aACnC,MAAMzE,YAAYyE,YAAY;gBAAE5C;gBAAgBuD;YAAgB,KAChE;gBAAC;aAAK;YACVrE,MAAM,sBAAsBoE;QAC9B;QAEA,wCAAwC;QACxC,IAAI,EAACD,gCAAAA,aAAc3D,MAAM,CAACsD,MAAM,KAAI,EAACM,oCAAAA,iBAAkB5D,MAAM,CAACsD,MAAM,GAAE;YACpE,MAAMQ,cAAc9E;YACpB,IAAK,MAAM+E,OAAOD,YAAYE,qBAAqB,CAAE;gBACnD,MAAMC,QAAQH,YAAYE,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAclB,aAAamB,KAAK;gBACtC,IAAIJ,QAAQxF,sBAAsB;oBAChC2F,WAAW,CAAC5F,qCAAqC,GAAG;wBAClD8F,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFF,WAAW,CAAC5F,qCAAqC,CAAC8F,MAAM;4BAC3DH;yBACD;wBACDI,OAAOjF,eAAekF,eAAe;oBACvC;gBACF,OAAO;oBACLJ,WAAW,CAACH,IAAI,GAAG;wBACjBQ,UAAU;4BAACjG;yBAAqC;wBAChD8F,QAAQH;wBACRI,OAAOjF,eAAekF,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAACzE,gBAAgBA,iBAAiB,UAAU;gBAC7C,CAAC6D,cAAcG,gBAAgB,GAAG,MAAMpF,YAAYuE,cAAc;oBACjE1C;oBACAuD;gBACF;gBACArE,MAAM,iBAAiBkE;YACzB;QACF;QAEAG,gBAAgBW,KAAK;QAErB1E,SAAS;YACPC,UAAU,AAAC,EAAE,CACV0E,MAAM,CACLf,gCAAAA,aAAc3D,QAAQ,EACtB4D,gCAAAA,aAAc5D,QAAQ,EACtB6D,oCAAAA,iBAAkB7D,QAAQ,EAE3B2E,MAAM,CAACtG;YACV4B,QAAQ,AAAC,EAAE,CACRyE,MAAM,CACLf,gCAAAA,aAAc1D,MAAM,EACpB2D,gCAAAA,aAAc3D,MAAM,EACpB4D,oCAAAA,iBAAkB5D,MAAM,EAEzB0E,MAAM,CAACtG;YACV6B,OAAO;gBACLyD,gCAAAA,aAAczD,KAAK;gBACnB0D,gCAAAA,aAAc1D,KAAK;gBACnB2D,oCAAAA,iBAAkB3D,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNI,UAAU,CAAC,2BACXoE,OAAO,CAAC,IAAMxG,sBAAsB2B,QAAQ;IAE/ChB,iBAAiB8F,eAAe,IAAG,wBAAA,AACjC5B,aACA6B,OAAO,qBAF0B,sBAExBC,IAAI,CAACrF;IAEhB,MAAMsF,0BAAyB,wBAAA,AAC7B9B,aACA4B,OAAO,qBAFsB,sBAEpBC,IAAI,CAACnF;IAEhB,MAAMqF,kBAAkBxB,QAAQC,MAAM,CAACvD;IAEvC,IAAIJ,OAAOE,MAAM,CAACsD,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAIxD,OAAOE,MAAM,CAACsD,MAAM,GAAG,GAAG;YAC5BxD,OAAOE,MAAM,CAACsD,MAAM,GAAG;QACzB;QACA,IAAI2B,QAAQnF,OAAOE,MAAM,CAAC0E,MAAM,CAACQ,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAAC/G,MAAMmH,GAAG,CAAC;QAExB,IACEJ,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACR;YACpC,MAAMS,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC;QAE5L;QAEAN,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,IAAID,MACd;YAEFC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,IAAID,MAAM;QACtBC,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,IAAI/F,OAAOC,QAAQ,CAACuD,MAAM,GAAG,GAAG;YAC9B5E,IAAI6E,IAAI,CAAC;YACT6B,QAAQ7B,IAAI,CAACzD,OAAOC,QAAQ,CAAC2E,MAAM,CAACQ,SAASC,IAAI,CAAC;YAClDC,QAAQ7B,IAAI;QACd,OAAO,IAAI,CAAC1D,cAAc;gBACxBf;aAAAA,iCAAAA,iBAAiBiH,YAAY,qBAA7BjH,+BAA+BkH,cAAc;YAC7CtH,IAAIuH,KAAK,CAAC;QACZ;QAEA,OAAO;YACLC,UAAUlB,eAAe,CAAC,EAAE;YAC5BmB,iBAAiB,EAAEpB,0CAAAA,uBAAwBoB,iBAAiB;YAC5DrC,aAAa9E;YACboH,gCAAgC;gBAC9BC,iBAAiB/G,kBAAkB+G,eAAe;gBAClDC,oBAAoBhH,kBAAkBgH,kBAAkB;gBACxDC,iBAAiBjH,kBAAkBiH,eAAe;gBAClDC,oBAAoBlH,kBAAkBkH,kBAAkB;YAC1D;QACF;IACF;AACF;AAEA,sDAAsD;AACtD,OAAO,eAAeC,WAAWC,UAGhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAAC9H,kBAAkB4H,WAAWG,YAAY;IAEvD,sBAAsB;IACtB9H,kBAAkBD,iBAAiBgF,WAAW;IAE9C,+CAA+C;IAC/C,MAAM,EAAEsC,8BAA8B,EAAE,GAAGtH;IAE3C,KAAK,MAAMiF,OAAO4C,OAAOG,IAAI,CAACV,kCAAkC,CAAC,GAAI;QACnEO,OAAOC,MAAM,CACX,AAACtH,iBAAyB,CAACyE,IAAI,EAC9BqC,kDAAD,AAACA,8BAAwC,CAACrC,IAAI;IAElD;IAEA,iDAAiD;IACjDjF,iBAAiBuB,MAAM,GAAG,MAAMnB,WAC9BV,wBACAM,iBAAiBsB,GAAG;IAEtBtB,iBAAiBqB,aAAa,GAAGhB,MAAM;IAEvC,MAAMW,SAAS,MAAMF,iBAAiB8G,WAAW7G,YAAY;IAC7D,MAAM,EAAEkH,YAAY,EAAE,GAAGjH,OAAOqG,iBAAiB,IAAI,CAAC;IACtD,IAAIY,cAAc;QAChB,MAAM,EAAEC,YAAY,EAAEC,WAAW,EAAE,GAAGF;QACtC,IAAIE,aAAa;YACfnH,OAAOqG,iBAAiB,CAAEY,YAAY,CAAEE,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeC,MAAMC,IAAI,CAACJ,CAAAA,gCAAAA,aAAcK,OAAO,OAAM,EAAE;YAC7D,mBAAmB;YACnBvH,OAAOqG,iBAAiB,CAACY,YAAY,CAACC,YAAY,GAAGE;QACvD;IACF;IACA,OAAOpH;AACT"}