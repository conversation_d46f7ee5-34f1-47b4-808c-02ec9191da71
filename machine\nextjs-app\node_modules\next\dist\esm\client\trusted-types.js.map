{"version": 3, "sources": ["../../src/client/trusted-types.ts"], "names": ["policy", "getPolicy", "window", "trustedTypes", "createPolicy", "createHTML", "input", "createScript", "createScriptURL", "__unsafeCreateTrustedScriptURL", "url"], "mappings": "AAAA;;;CAGC,GACD,IAAIA;AAEJ;;;CAGC,GACD,SAASC;IACP,IAAI,OAAOD,WAAW,eAAe,OAAOE,WAAW,aAAa;YAEhEA;QADFF,SACEE,EAAAA,uBAAAA,OAAOC,YAAY,qBAAnBD,qBAAqBE,YAAY,CAAC,UAAU;YAC1CC,YAAY,CAACC,QAAUA;YACvBC,cAAc,CAACD,QAAUA;YACzBE,iBAAiB,CAACF,QAAUA;QAC9B,OAAM;IACV;IAEA,OAAON;AACT;AAEA;;;;;;;;CAQC,GACD,OAAO,SAASS,+BACdC,GAAW;QAEJT;IAAP,OAAOA,EAAAA,aAAAA,gCAAAA,WAAaO,eAAe,CAACE,SAAQA;AAC9C"}