{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/getModuleTrace.ts"], "names": ["loaderUtils", "relative", "formatModule", "compiler", "module", "relativePath", "context", "resource", "replace", "isUrlRequest", "urlToRequest", "formatModuleTrace", "moduleTrace", "importTrace", "firstExternalModule", "i", "length", "mod", "includes", "unshift", "invalidImportMessage", "firstExternalPackageName", "resourceResolveData", "descriptionFileData", "name", "formattedExternalFile", "split", "slice", "lastInternalFileName", "formattedModuleTrace", "map", "join", "getModuleTrace", "compilation", "visitedModules", "Set", "current", "isPagesDir", "has", "test", "add", "push", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>"], "mappings": "AACA,OAAOA,iBAAiB,mCAAkC;AAC1D,SAASC,QAAQ,QAAQ,OAAM;AAE/B,SAASC,aAAaC,QAA0B,EAAEC,MAAW;IAC3D,MAAMC,eAAeJ,SAASE,SAASG,OAAO,EAAEF,OAAOG,QAAQ,EAAEC,OAAO,CACtE,SACA;IAEF,OAAOR,YAAYS,YAAY,CAACJ,gBAC5BL,YAAYU,YAAY,CAACL,gBACzBA;AACN;AAEA,OAAO,SAASM,kBACdR,QAA0B,EAC1BS,WAAkB;IAElB,IAAIC,cAAwB,EAAE;IAC9B,IAAIC;IACJ,IAAK,IAAIC,IAAIH,YAAYI,MAAM,GAAG,GAAGD,KAAK,GAAGA,IAAK;QAChD,MAAME,MAAML,WAAW,CAACG,EAAE;QAC1B,IAAI,CAACE,IAAIV,QAAQ,EAAE;QAEnB,IAAI,CAACU,IAAIV,QAAQ,CAACW,QAAQ,CAAC,kBAAkB;YAC3CL,YAAYM,OAAO,CAACjB,aAAaC,UAAUc;QAC7C,OAAO;YACLH,sBAAsBG;YACtB;QACF;IACF;IAEA,IAAIG,uBAAuB;IAC3B,IAAIN,qBAAqB;YAErBA,8DAAAA;QADF,MAAMO,4BACJP,2CAAAA,oBAAoBQ,mBAAmB,sBAAvCR,+DAAAA,yCAAyCS,mBAAmB,qBAA5DT,6DAA8DU,IAAI;QAEpE,IAAIH,6BAA6B,cAAc;YAC7CD,wBAAwB,CAAC,mDAAmD,EAAEP,WAAW,CAAC,EAAE,CAAC,qIAAqI,CAAC;QACrO,OAAO;YACL,IAAIY,wBACFX,oBAAoBP,QAAQ,CAACmB,KAAK,CAAC;YACrCD,wBACEA,qBAAqB,CAACA,sBAAsBT,MAAM,GAAG,EAAE;YAEzDI,wBAAwB,CAAC,uCAAuC,EAAEK,sBAAsBE,KAAK,CAC3F,GACA,MAAM,EAAEd,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;QAC9B;IACF;IAEA,OAAO;QACLe,sBAAsBf,WAAW,CAAC,EAAE;QACpCO;QACAS,sBAAsB,CAAC,EAAEhB,YAAYiB,GAAG,CAAC,CAACb,MAAQ,OAAOA,KAAKc,IAAI,CAAC,MAAM,CAAC;IAC5E;AACF;AAEA,OAAO,SAASC,eACd5B,MAAW,EACX6B,WAAgC,EAChC9B,QAA0B;IAE1B,wBAAwB;IACxB,kIAAkI;IAClI,MAAM+B,iBAAiB,IAAIC;IAC3B,MAAMvB,cAAc,EAAE;IAEtB,IAAIwB,UAAUhC;IACd,IAAIiC,aAAa;IACjB,MAAOD,QAAS;QACd,IAAIF,eAAeI,GAAG,CAACF,UAAU;QACjC,IAAI,aAAaG,IAAI,CAACH,QAAQ7B,QAAQ,CAACC,OAAO,CAACL,SAASG,OAAO,EAAE,MAAM;YACrE+B,aAAa;QACf;QACAH,eAAeM,GAAG,CAACJ;QACnBxB,YAAY6B,IAAI,CAACL;QACjB,MAAMM,SAAST,YAAYU,WAAW,CAACC,SAAS,CAACR;QACjD,IAAI,CAACM,QAAQ;QACbN,UAAUM;IACZ;IAEA,OAAO;QACL9B;QACAyB;IACF;AACF"}