{% extends "base.html" %}
{% block title %}Profile{% endblock %}
{% block content %}
<h2>Update Password</h2>
<form method="POST">
  <label>New Password:</label>
  <input type="password" name="new_password" required>
  <button type="submit">Update Password</button>
</form>
<p><a href="{{ url_for('dashboard') }}">Back to Dashboard</a></p>

{% with messages = get_flashed_messages() %}
  {% if messages %}
    <div class="flash">
      <ul>
      {% for message in messages %}
        <li>{{ message }}</li>
      {% endfor %}
      </ul>
    </div>
  {% endif %}
{% endwith %}
{% endblock %}
