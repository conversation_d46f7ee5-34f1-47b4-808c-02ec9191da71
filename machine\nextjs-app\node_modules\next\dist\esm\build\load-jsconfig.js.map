{"version": 3, "sources": ["../../src/build/load-jsconfig.ts"], "names": ["path", "fileExists", "Log", "getTypeScriptConfiguration", "readFileSync", "isError", "hasNecessaryDependencies", "TSCONFIG_WARNED", "parseJsonFile", "filePath", "JSON5", "require", "contents", "trim", "parse", "err", "codeFrameColumns", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "loadJsConfig", "dir", "config", "typeScriptPath", "deps", "pkg", "file", "exportsRestrict", "resolved", "get", "tsConfigPath", "join", "typescript", "tsconfigPath", "useTypeScript", "Boolean", "implicit<PERSON><PERSON><PERSON>l", "jsConfig", "info", "ts", "Promise", "resolve", "tsConfig", "compilerOptions", "options", "dirname", "jsConfigPath", "resolvedBaseUrl", "baseUrl"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,UAAU,QAAQ,qBAAoB;AAE/C,YAAYC,SAAS,eAAc;AACnC,SAASC,0BAA0B,QAAQ,+CAA8C;AACzF,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,aAAa,kBAAiB;AACrC,SAASC,wBAAwB,QAAQ,oCAAmC;AAE5E,IAAIC,kBAAkB;AAEtB,SAASC,cAAcC,QAAgB;IACrC,MAAMC,QAAQC,QAAQ;IACtB,MAAMC,WAAWR,aAAaK,UAAU;IAExC,6BAA6B;IAC7B,IAAIG,SAASC,IAAI,OAAO,IAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOH,MAAMI,KAAK,CAACF;IACrB,EAAE,OAAOG,KAAK;QACZ,IAAI,CAACV,QAAQU,MAAM,MAAMA;QACzB,MAAM,EAAEC,gBAAgB,EAAE,GAAGL,QAAQ;QACrC,MAAMM,YAAYD,iBAChBE,OAAON,WACP;YACEO,OAAO;gBACLC,MAAM,AAACL,IAAwCM,UAAU,IAAI;gBAC7DC,QAAQ,AAACP,IAA0CQ,YAAY,IAAI;YACrE;QACF,GACA;YAAEC,SAAST,IAAIS,OAAO;YAAEC,eAAe;QAAK;QAE9C,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAEjB,SAAS,IAAI,EAAEQ,UAAU,CAAC;IAChE;AACF;AAEA,eAAe,eAAeU,aAC5BC,GAAW,EACXC,MAA0B;IAE1B,IAAIC;IACJ,IAAI;QACF,MAAMC,OAAO,MAAMzB,yBAAyBsB,KAAK;YAC/C;gBACEI,KAAK;gBACLC,MAAM;gBACNC,iBAAiB;YACnB;SACD;QACDJ,iBAAiBC,KAAKI,QAAQ,CAACC,GAAG,CAAC;IACrC,EAAE,OAAM,CAAC;IACT,MAAMC,eAAerC,KAAKsC,IAAI,CAACV,KAAKC,OAAOU,UAAU,CAACC,YAAY;IAClE,MAAMC,gBAAgBC,QACpBZ,kBAAmB,MAAM7B,WAAWoC;IAGtC,IAAIM;IACJ,IAAIC;IACJ,mCAAmC;IACnC,IAAIH,eAAe;QACjB,IACEZ,OAAOU,UAAU,CAACC,YAAY,KAAK,mBACnCjC,oBAAoB,OACpB;YACAA,kBAAkB;YAClBL,IAAI2C,IAAI,CAAC,CAAC,qBAAqB,EAAEhB,OAAOU,UAAU,CAACC,YAAY,CAAC,CAAC;QACnE;QAEA,MAAMM,KAAM,MAAMC,QAAQC,OAAO,CAC/BrC,QAAQmB;QAEV,MAAMmB,WAAW,MAAM9C,2BAA2B2C,IAAIT,cAAc;QACpEO,WAAW;YAAEM,iBAAiBD,SAASE,OAAO;QAAC;QAC/CR,kBAAkB3C,KAAKoD,OAAO,CAACf;IACjC;IAEA,MAAMgB,eAAerD,KAAKsC,IAAI,CAACV,KAAK;IACpC,IAAI,CAACa,iBAAkB,MAAMxC,WAAWoD,eAAgB;QACtDT,WAAWpC,cAAc6C;QACzBV,kBAAkB3C,KAAKoD,OAAO,CAACC;IACjC;IAEA,IAAIC;IACJ,IAAIV,UAAU;YACRA;QAAJ,KAAIA,4BAAAA,SAASM,eAAe,qBAAxBN,0BAA0BW,OAAO,EAAE;YACrCD,kBAAkBtD,KAAKgD,OAAO,CAACpB,KAAKgB,SAASM,eAAe,CAACK,OAAO;QACtE,OAAO;YACLD,kBAAkBX;QACpB;IACF;IAEA,OAAO;QACLF;QACAG;QACAU;IACF;AACF"}