{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.ts"], "names": ["getFramework", "sourcePackage", "undefined", "test", "groupStackFramesByFramework", "stackFrames", "stackFramesGroupedByFramework", "stackFrame", "currentGroup", "length", "framework", "push"], "mappings": "AAOA;;CAEC,GACD,SAASA,aACPC,aAAiC;IAEjC,IAAI,CAACA,eAAe,OAAOC;IAE3B,IACE,gFAAgFC,IAAI,CAClFF,gBAEF;QACA,OAAO;IACT,OAAO,IAAIA,kBAAkB,QAAQ;QACnC,OAAO;IACT;IAEA,OAAOC;AACT;AAEA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,OAAO,SAASE,4BACdC,WAAiC;IAEjC,MAAMC,gCAAoD,EAAE;IAE5D,KAAK,MAAMC,cAAcF,YAAa;QACpC,MAAMG,eACJF,6BAA6B,CAACA,8BAA8BG,MAAM,GAAG,EAAE;QACzE,MAAMC,YAAYV,aAAaO,WAAWN,aAAa;QAEvD,IAAIO,gBAAgBA,aAAaE,SAAS,KAAKA,WAAW;YACxDF,aAAaH,WAAW,CAACM,IAAI,CAACJ;QAChC,OAAO;YACLD,8BAA8BK,IAAI,CAAC;gBACjCD,WAAWA;gBACXL,aAAa;oBAACE;iBAAW;YAC3B;QACF;IACF;IAEA,OAAOD;AACT"}