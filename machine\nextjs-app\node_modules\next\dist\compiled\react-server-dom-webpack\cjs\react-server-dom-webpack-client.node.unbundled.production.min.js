/**
 * @license React
 * react-server-dom-webpack-client.node.unbundled.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var p=require("util"),q=require("react-dom"),t=require("react"),u={stream:!0};function v(a,c){var b=a[c.id];if(a=b[c.name])b=a.name;else{a=b["*"];if(!a)throw Error('Could not find the module "'+c.id+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');b=c.name}return{specifier:a.specifier,name:b,async:c.async}}var y=new Map;
function z(a){var c=y.get(a.specifier);if(c)return"fulfilled"===c.status?null:c;var b=import(a.specifier);a.async&&(b=b.then(function(d){return d.default}));b.then(function(d){var f=b;f.status="fulfilled";f.value=d},function(d){var f=b;f.status="rejected";f.reason=d});y.set(a.specifier,b);return b}var A=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),C=Symbol.for("react.lazy"),D=Symbol.for("react.default_value"),E=Symbol.iterator;
function F(a){if(null===a||"object"!==typeof a)return null;a=E&&a[E]||a["@@iterator"];return"function"===typeof a?a:null}var G=Array.isArray,H=new WeakMap;function I(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function J(a,c,b,d){function f(h,g){if(null===g)return null;if("object"===typeof g){if("function"===typeof g.then){null===e&&(e=new FormData);l++;var w=k++;g.then(function(n){n=JSON.stringify(n,f);var x=e;x.append(c+w,n);l--;0===l&&b(x)},function(n){d(n)});return"$@"+w.toString(16)}if(g instanceof FormData){null===e&&(e=new FormData);var m=e;h=k++;var r=c+h+"_";g.forEach(function(n,x){m.append(r+x,n)});return"$K"+h.toString(16)}return g instanceof Map?(g=JSON.stringify(Array.from(g),f),null===e&&
(e=new FormData),h=k++,e.append(c+h,g),"$Q"+h.toString(16)):g instanceof Set?(g=JSON.stringify(Array.from(g),f),null===e&&(e=new FormData),h=k++,e.append(c+h,g),"$W"+h.toString(16)):!G(g)&&F(g)?Array.from(g):g}if("string"===typeof g){if("Z"===g[g.length-1]&&this[h]instanceof Date)return"$D"+g;g="$"===g[0]?"$"+g:g;return g}if("boolean"===typeof g)return g;if("number"===typeof g)return I(g);if("undefined"===typeof g)return"$undefined";if("function"===typeof g){g=H.get(g);if(void 0!==g)return g=JSON.stringify(g,
f),null===e&&(e=new FormData),h=k++,e.set(c+h,g),"$F"+h.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof g){h=g.description;if(Symbol.for(h)!==g)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+(g.description+") cannot be found among global symbols."));return"$S"+h}if("bigint"===typeof g)return"$n"+
g.toString(10);throw Error("Type "+typeof g+" is not supported as an argument to a Server Function.");}var k=1,l=0,e=null;a=JSON.stringify(a,f);null===e?b(a):(e.set(c+"0",a),0===l&&b(e))}var K=new WeakMap;function aa(a){var c,b,d=new Promise(function(f,k){c=f;b=k});J(a,"",function(f){if("string"===typeof f){var k=new FormData;k.append("0",f);f=k}d.status="fulfilled";d.value=f;c(f)},function(f){d.status="rejected";d.reason=f;b(f)});return d}
function ba(a){var c=H.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var b=null;if(null!==c.bound){b=K.get(c);b||(b=aa(c),K.set(c,b));if("rejected"===b.status)throw b.reason;if("fulfilled"!==b.status)throw b;c=b.value;var d=new FormData;c.forEach(function(f,k){d.append("$ACTION_"+a+":"+k,f)});b=d;c="$ACTION_REF_"+a}else c="$ACTION_ID_"+c.id;return{name:c,method:"POST",encType:"multipart/form-data",data:b}}
function ca(a,c){var b=H.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(b.id!==a)return!1;var d=b.bound;if(null===d)return 0===c;switch(d.status){case "fulfilled":return d.value.length===c;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(f){d.status="fulfilled";d.value=f},function(f){d.status="rejected";d.reason=f})),d;}}
function L(a,c){Object.defineProperties(a,{$$FORM_ACTION:{value:ba},$$IS_SIGNATURE_EQUAL:{value:ca},bind:{value:da}});H.set(a,c)}var ea=Function.prototype.bind,fa=Array.prototype.slice;function da(){var a=ea.apply(this,arguments),c=H.get(this);if(c){var b=fa.call(arguments,1),d=null;d=null!==c.bound?Promise.resolve(c.bound).then(function(f){return f.concat(b)}):Promise.resolve(b);L(a,{id:c.id,bound:d})}return a}
function ha(a,c){function b(){var d=Array.prototype.slice.call(arguments);return c(a,d)}L(b,{id:a,bound:null});return b}var M=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function N(a,c,b,d){this.status=a;this.value=c;this.reason=b;this._response=d}N.prototype=Object.create(Promise.prototype);
N.prototype.then=function(a,c){switch(this.status){case "resolved_model":O(this);break;case "resolved_module":P(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));c&&(null===this.reason&&(this.reason=[]),this.reason.push(c));break;default:c(this.reason)}};
function ia(a){switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function Q(a,c){for(var b=0;b<a.length;b++)(0,a[b])(c)}function R(a,c,b){switch(a.status){case "fulfilled":Q(c,a.value);break;case "pending":case "blocked":a.value=c;a.reason=b;break;case "rejected":b&&Q(b,a.reason)}}
function S(a,c){if("pending"===a.status||"blocked"===a.status){var b=a.reason;a.status="rejected";a.reason=c;null!==b&&Q(b,c)}}function T(a,c){if("pending"===a.status||"blocked"===a.status){var b=a.value,d=a.reason;a.status="resolved_module";a.value=c;null!==b&&(P(a),R(a,b,d))}}var U=null,V=null;
function O(a){var c=U,b=V;U=a;V=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==V&&0<V.deps?(V.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(f){a.status="rejected",a.reason=f}finally{U=c,V=b}}function P(a){try{var c=a.value,b=y.get(c.specifier);if("fulfilled"===b.status)var d=b.value;else throw b.reason;var f="*"===c.name?d:""===c.name?d.default:d[c.name];a.status="fulfilled";a.value=f}catch(k){a.status="rejected",a.reason=k}}
function W(a,c){a._chunks.forEach(function(b){"pending"===b.status&&S(b,c)})}function X(a,c){var b=a._chunks,d=b.get(c);d||(d=new N("pending",null,null,a),b.set(c,d));return d}function ja(a,c,b){if(V){var d=V;d.deps++}else d=V={deps:1,value:null};return function(f){c[b]=f;d.deps--;0===d.deps&&"blocked"===a.status&&(f=a.value,a.status="fulfilled",a.value=d.value,null!==f&&Q(f,d.value))}}function ka(a){return function(c){return S(a,c)}}
function la(a,c){function b(){var f=Array.prototype.slice.call(arguments),k=c.bound;return k?"fulfilled"===k.status?d(c.id,k.value.concat(f)):Promise.resolve(k).then(function(l){return d(c.id,l.concat(f))}):d(c.id,f)}var d=a._callServer;L(b,c);return b}function Y(a,c){a=X(a,c);switch(a.status){case "resolved_model":O(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ma(a,c,b,d){if("$"===d[0]){if("$"===d)return B;switch(d[1]){case "$":return d.slice(1);case "L":return c=parseInt(d.slice(2),16),a=X(a,c),{$$typeof:C,_payload:a,_init:ia};case "@":return c=parseInt(d.slice(2),16),X(a,c);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),M[a]||(M[a]=t.createServerContext(a,D)),M[a].Provider;case "F":return c=parseInt(d.slice(2),16),c=Y(a,c),la(a,c);case "Q":return c=parseInt(d.slice(2),16),a=Y(a,c),new Map(a);case "W":return c=parseInt(d.slice(2),
16),a=Y(a,c),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=X(a,d);switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=U,a.then(ja(d,c,b),ka(d)),null;default:throw a.reason;}}}return d}
function na(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function oa(a,c){var b=new Map;a={_bundlerConfig:a,_callServer:void 0!==c?c:na,_chunks:b,_stringDecoder:new p.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=pa(a);return a}
function qa(a,c,b){var d=a._chunks,f=d.get(c);b=JSON.parse(b,a._fromJSON);var k=v(a._bundlerConfig,b);if(b=z(k)){if(f){var l=f;l.status="blocked"}else l=new N("blocked",null,null,a),d.set(c,l);b.then(function(){return T(l,k)},function(e){return S(l,e)})}else f?T(f,k):d.set(c,new N("resolved_module",k,null,a))}
function pa(a){return function(c,b){return"string"===typeof b?ma(a,this,c,b):"object"===typeof b&&null!==b?(c=b[0]===B?{$$typeof:B,type:b[1],key:b[2],ref:null,props:b[3],_owner:null}:b,c):b}}function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,c){var b=oa(c,Z);a.on("data",function(d){for(var f=0,k=b._rowState,l=b._rowID,e=b._rowTag,h=b._rowLength,g=b._buffer,w=d.length;f<w;){var m=-1;switch(k){case 0:m=d[f++];58===m?k=1:l=l<<4|(96<m?m-87:m-48);continue;case 1:k=d[f];84===k?(e=k,k=2,f++):64<k&&91>k?(e=k,k=3,f++):(e=0,k=3);continue;case 2:m=d[f++];44===m?k=4:h=h<<4|(96<m?m-87:m-48);continue;case 3:m=d.indexOf(10,f);break;case 4:m=f+h,m>d.length&&(m=-1)}var r=d.byteOffset+f;if(-1<m){h=new Uint8Array(d.buffer,
r,m-f);f=e;r=b._stringDecoder;e="";for(var n=0;n<g.length;n++)e+=r.decode(g[n],u);e+=r.decode(h);switch(f){case 73:qa(b,l,e);break;case 72:l=e[0];e=e.slice(1);e=JSON.parse(e,b._fromJSON);if(h=A.current)switch(l){case "D":h.prefetchDNS(e);break;case "C":"string"===typeof e?h.preconnect(e):h.preconnect(e[0],e[1]);break;case "L":l=e[0];f=e[1];3===e.length?h.preload(l,f,e[2]):h.preload(l,f);break;case "m":"string"===typeof e?h.preloadModule(e):h.preloadModule(e[0],e[1]);break;case "S":"string"===typeof e?
h.preinitStyle(e):h.preinitStyle(e[0],0===e[1]?void 0:e[1],3===e.length?e[2]:void 0);break;case "X":"string"===typeof e?h.preinitScript(e):h.preinitScript(e[0],e[1]);break;case "M":"string"===typeof e?h.preinitModuleScript(e):h.preinitModuleScript(e[0],e[1])}break;case 69:e=JSON.parse(e);h=e.digest;e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
e.stack="Error: "+e.message;e.digest=h;h=b._chunks;(f=h.get(l))?S(f,e):h.set(l,new N("rejected",null,e,b));break;case 84:b._chunks.set(l,new N("fulfilled",e,null,b));break;default:h=b._chunks,(f=h.get(l))?(l=f,"pending"===l.status&&(h=l.value,f=l.reason,l.status="resolved_model",l.value=e,null!==h&&(O(l),R(l,h,f)))):h.set(l,new N("resolved_model",e,null,b))}f=m;3===k&&f++;h=l=e=k=0;g.length=0}else{d=new Uint8Array(d.buffer,r,d.byteLength-f);g.push(d);h-=d.byteLength;break}}b._rowState=k;b._rowID=
l;b._rowTag=e;b._rowLength=h});a.on("error",function(d){W(b,d)});a.on("end",function(){W(b,Error("Connection closed."))});return X(b,0)};exports.createServerReference=function(a){return ha(a,Z)};
