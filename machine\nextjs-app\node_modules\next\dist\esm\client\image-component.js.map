{"version": 3, "sources": ["../../src/client/image-component.tsx"], "names": ["React", "useRef", "useEffect", "useCallback", "useContext", "useMemo", "useState", "forwardRef", "version", "ReactDOM", "Head", "getImgProps", "imageConfigDefault", "ImageConfigContext", "warnOnce", "RouterContext", "defaultLoader", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "placeholder", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "unoptimized", "src", "p", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "current", "event", "Event", "Object", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "NODE_ENV", "origSrc", "URL", "searchParams", "get", "getAttribute", "widthViewportRatio", "getBoundingClientRect", "width", "innerWidth", "position", "getComputedStyle", "valid", "includes", "map", "String", "join", "height", "heightModified", "toString", "widthModified", "getDynamicProps", "fetchPriority", "majorStr", "minorStr", "split", "major", "parseInt", "minor", "fetchpriority", "ImageElement", "forwardedRef", "srcSet", "sizes", "decoding", "className", "style", "loading", "fill", "setShowAltText", "onLoad", "onError", "rest", "data-nimg", "ref", "console", "error", "complete", "ImagePreload", "isAppRouter", "imgAttributes", "opts", "as", "imageSrcSet", "imageSizes", "crossOrigin", "referrerPolicy", "preload", "link", "key", "rel", "href", "undefined", "Image", "props", "pagesRouter", "configContext", "config", "c", "allSizes", "deviceSizes", "sort", "a", "b", "onLoadingComplete", "blurComplete", "showAltText", "meta", "imgMeta", "imgConf", "priority"], "mappings": "AAAA;AAEA,OAAOA,SACLC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,QAAO;AACd,OAAOC,cAAc,YAAW;AAChC,OAAOC,UAAU,qBAAoB;AACrC,SAASC,WAAW,QAAQ,8BAA6B;AAYzD,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,SAASC,QAAQ,QAAQ,gCAA+B;AACxD,SAASC,aAAa,QAAQ,8CAA6C;AAE3E,iDAAiD;AACjD,OAAOC,mBAAmB,oCAAmC;AAE7D,4CAA4C;AAC5C,MAAMC,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAE/C,IAAI,OAAOC,WAAW,aAAa;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAkBA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASC,cACPC,GAA2B,EAC3BC,WAA6B,EAC7BC,SAAqD,EACrDC,oBAA2E,EAC3EC,eAAqC,EACrCC,WAAoB;IAEpB,MAAMC,MAAMN,uBAAAA,IAAKM,GAAG;IACpB,IAAI,CAACN,OAAOA,GAAG,CAAC,kBAAkB,KAAKM,KAAK;QAC1C;IACF;IACAN,GAAG,CAAC,kBAAkB,GAAGM;IACzB,MAAMC,IAAI,YAAYP,MAAMA,IAAIQ,MAAM,KAAKC,QAAQC,OAAO;IAC1DH,EAAEI,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACZ,IAAIa,aAAa,IAAI,CAACb,IAAIc,WAAW,EAAE;YAC1C,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACA,IAAIb,gBAAgB,SAAS;YAC3BG,gBAAgB;QAClB;QACA,IAAIF,6BAAAA,UAAWa,OAAO,EAAE;YACtB,+CAA+C;YAC/C,0CAA0C;YAC1C,2CAA2C;YAC3C,MAAMC,QAAQ,IAAIC,MAAM;YACxBC,OAAOC,cAAc,CAACH,OAAO,UAAU;gBAAEI,UAAU;gBAAOC,OAAOrB;YAAI;YACrE,IAAIsB,YAAY;YAChB,IAAIC,UAAU;YACdrB,UAAUa,OAAO,CAAC;gBAChB,GAAGC,KAAK;gBACRQ,aAAaR;gBACbS,eAAezB;gBACf0B,QAAQ1B;gBACR2B,oBAAoB,IAAML;gBAC1BM,sBAAsB,IAAML;gBAC5BM,SAAS,KAAO;gBAChBC,gBAAgB;oBACdR,YAAY;oBACZN,MAAMc,cAAc;gBACtB;gBACAC,iBAAiB;oBACfR,UAAU;oBACVP,MAAMe,eAAe;gBACvB;YACF;QACF;QACA,IAAI5B,wCAAAA,qBAAsBY,OAAO,EAAE;YACjCZ,qBAAqBY,OAAO,CAACf;QAC/B;QACA,IAAIP,QAAQC,GAAG,CAACsC,QAAQ,KAAK,cAAc;YACzC,MAAMC,UAAU,IAAIC,IAAI5B,KAAK,YAAY6B,YAAY,CAACC,GAAG,CAAC,UAAU9B;YACpE,IAAIN,IAAIqC,YAAY,CAAC,iBAAiB,QAAQ;gBAC5C,IACE,CAAChC,eACA,CAAA,CAACL,IAAIqC,YAAY,CAAC,YAAYrC,IAAIqC,YAAY,CAAC,aAAa,OAAM,GACnE;oBACA,IAAIC,qBACFtC,IAAIuC,qBAAqB,GAAGC,KAAK,GAAG5C,OAAO6C,UAAU;oBACvD,IAAIH,qBAAqB,KAAK;wBAC5BjD,SACE,AAAC,qBAAkB4C,UAAQ;oBAE/B;gBACF;gBACA,IAAIjC,IAAIa,aAAa,EAAE;oBACrB,MAAM,EAAE6B,QAAQ,EAAE,GAAG9C,OAAO+C,gBAAgB,CAAC3C,IAAIa,aAAa;oBAC9D,MAAM+B,QAAQ;wBAAC;wBAAY;wBAAS;qBAAW;oBAC/C,IAAI,CAACA,MAAMC,QAAQ,CAACH,WAAW;wBAC7BrD,SACE,AAAC,qBAAkB4C,UAAQ,wEAAqES,WAAS,wBAAqBE,MAC3HE,GAAG,CAACC,QACJC,IAAI,CAAC,OAAK;oBAEjB;gBACF;gBACA,IAAIhD,IAAIiD,MAAM,KAAK,GAAG;oBACpB5D,SACE,AAAC,qBAAkB4C,UAAQ;gBAE/B;YACF;YAEA,MAAMiB,iBACJlD,IAAIiD,MAAM,CAACE,QAAQ,OAAOnD,IAAIqC,YAAY,CAAC;YAC7C,MAAMe,gBAAgBpD,IAAIwC,KAAK,CAACW,QAAQ,OAAOnD,IAAIqC,YAAY,CAAC;YAChE,IACE,AAACa,kBAAkB,CAACE,iBACnB,CAACF,kBAAkBE,eACpB;gBACA/D,SACE,AAAC,qBAAkB4C,UAAQ;YAE/B;QACF;IACF;AACF;AAEA,SAASoB,gBACPC,aAAsB;IAEtB,MAAM,CAACC,UAAUC,SAAS,GAAGzE,QAAQ0E,KAAK,CAAC;IAC3C,MAAMC,QAAQC,SAASJ,UAAU;IACjC,MAAMK,QAAQD,SAASH,UAAU;IACjC,IAAIE,QAAQ,MAAOA,UAAU,MAAME,SAAS,GAAI;QAC9C,kDAAkD;QAClD,iDAAiD;QACjD,mDAAmD;QACnD,OAAO;YAAEN;QAAc;IACzB;IACA,uDAAuD;IACvD,4CAA4C;IAC5C,OAAO;QAAEO,eAAeP;IAAc;AACxC;AAEA,MAAMQ,6BAAehF,WACnB,QAuBEiF;QAtBA,EACEzD,GAAG,EACH0D,MAAM,EACNC,KAAK,EACLhB,MAAM,EACNT,KAAK,EACL0B,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLd,aAAa,EACbrD,WAAW,EACXoE,OAAO,EACPhE,WAAW,EACXiE,IAAI,EACJpE,SAAS,EACTC,oBAAoB,EACpBC,eAAe,EACfmE,cAAc,EACdC,MAAM,EACNC,OAAO,EACP,GAAGC,MACJ;IAGD,qBACE,oBAAC1E;QACE,GAAG0E,IAAI;QACP,GAAGrB,gBAAgBC,cAAc;QAClC,qEAAqE;QACrE,wEAAwE;QACxE,qDAAqD;QACrDe,SAASA;QACT7B,OAAOA;QACPS,QAAQA;QACRiB,UAAUA;QACVS,aAAWL,OAAO,SAAS;QAC3BH,WAAWA;QACXC,OAAOA;QACP,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtDH,OAAOA;QACPD,QAAQA;QACR1D,KAAKA;QACLsE,KAAKlG,YACH,CAACsB;YACC,IAAI+D,cAAc;gBAChB,IAAI,OAAOA,iBAAiB,YAAYA,aAAa/D;qBAChD,IAAI,OAAO+D,iBAAiB,UAAU;oBACzC,+EAA+E;oBAC/EA,aAAahD,OAAO,GAAGf;gBACzB;YACF;YACA,IAAI,CAACA,KAAK;gBACR;YACF;YACA,IAAIyE,SAAS;gBACX,2EAA2E;gBAC3E,iFAAiF;gBACjF,kFAAkF;gBAClF,0CAA0C;gBAC1CzE,IAAIM,GAAG,GAAGN,IAAIM,GAAG;YACnB;YACA,IAAIb,QAAQC,GAAG,CAACsC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAAC1B,KAAK;oBACRuE,QAAQC,KAAK,CAAE,6CAA4C9E;gBAC7D;gBACA,IAAIA,IAAIqC,YAAY,CAAC,WAAW,MAAM;oBACpCwC,QAAQC,KAAK,CACV;gBAEL;YACF;YACA,IAAI9E,IAAI+E,QAAQ,EAAE;gBAChBhF,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC;YAEJ;QACF,GACA;YACEC;YACAL;YACAC;YACAC;YACAC;YACAqE;YACApE;YACA0D;SACD;QAEHS,QAAQ,CAACxD;YACP,MAAMhB,MAAMgB,MAAMS,aAAa;YAC/B1B,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC;QAEJ;QACAoE,SAAS,CAACzD;YACR,qEAAqE;YACrEuD,eAAe;YACf,IAAItE,gBAAgB,SAAS;gBAC3B,2EAA2E;gBAC3EG,gBAAgB;YAClB;YACA,IAAIqE,SAAS;gBACXA,QAAQzD;YACV;QACF;;AAGN;AAGF,SAASgE,aAAa,KAMrB;IANqB,IAAA,EACpBC,WAAW,EACXC,aAAa,EAId,GANqB;IAOpB,MAAMC,OAAO;QACXC,IAAI;QACJC,aAAaH,cAAclB,MAAM;QACjCsB,YAAYJ,cAAcjB,KAAK;QAC/BsB,aAAaL,cAAcK,WAAW;QACtCC,gBAAgBN,cAAcM,cAAc;QAC5C,GAAGnC,gBAAgB6B,cAAc5B,aAAa,CAAC;IACjD;IAEA,IAAI2B,eAAejG,SAASyG,OAAO,EAAE;QACnC,mDAAmD;QACnDzG,SAASyG,OAAO,CACdP,cAAc5E,GAAG,EACjB,8DAA8D;QAC9D6E;QAEF,OAAO;IACT;IAEA,qBACE,oBAAClG,0BACC,oBAACyG;QACCC,KACE,YACAT,cAAc5E,GAAG,GACjB4E,cAAclB,MAAM,GACpBkB,cAAcjB,KAAK;QAErB2B,KAAI;QACJ,sEAAsE;QACtE,qEAAqE;QACrE,sDAAsD;QACtD,EAAE;QACF,8EAA8E;QAC9EC,MAAMX,cAAclB,MAAM,GAAG8B,YAAYZ,cAAc5E,GAAG;QACzD,GAAG6E,IAAI;;AAIhB;AAEA,OAAO,MAAMY,sBAAQjH,WACnB,CAACkH,OAAOjC;IACN,MAAMkC,cAActH,WAAWW;IAC/B,0DAA0D;IAC1D,MAAM2F,cAAc,CAACgB;IAErB,MAAMC,gBAAgBvH,WAAWS;IACjC,MAAM+G,SAASvH,QAAQ;QACrB,MAAMwH,IAAI5G,aAAa0G,iBAAiB/G;QACxC,MAAMkH,WAAW;eAAID,EAAEE,WAAW;eAAKF,EAAEd,UAAU;SAAC,CAACiB,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMH,cAAcF,EAAEE,WAAW,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,OAAO;YAAE,GAAGL,CAAC;YAAEC;YAAUC;QAAY;IACvC,GAAG;QAACJ;KAAc;IAElB,MAAM,EAAE1B,MAAM,EAAEkC,iBAAiB,EAAE,GAAGV;IACtC,MAAM9F,YAAY1B,OAAOgG;IAEzB/F,UAAU;QACRyB,UAAUa,OAAO,GAAGyD;IACtB,GAAG;QAACA;KAAO;IAEX,MAAMrE,uBAAuB3B,OAAOkI;IAEpCjI,UAAU;QACR0B,qBAAqBY,OAAO,GAAG2F;IACjC,GAAG;QAACA;KAAkB;IAEtB,MAAM,CAACC,cAAcvG,gBAAgB,GAAGvB,SAAS;IACjD,MAAM,CAAC+H,aAAarC,eAAe,GAAG1F,SAAS;IAE/C,MAAM,EAAEmH,OAAOd,aAAa,EAAE2B,MAAMC,OAAO,EAAE,GAAG5H,YAAY8G,OAAO;QACjEzG;QACAwH,SAASZ;QACTQ;QACAC;IACF;IAEA,qBACE,wDAEI,oBAAC9C;QACE,GAAGoB,aAAa;QACjB7E,aAAayG,QAAQzG,WAAW;QAChCJ,aAAa6G,QAAQ7G,WAAW;QAChCqE,MAAMwC,QAAQxC,IAAI;QAClBpE,WAAWA;QACXC,sBAAsBA;QACtBC,iBAAiBA;QACjBmE,gBAAgBA;QAChBK,KAAKb;QAGR+C,QAAQE,QAAQ,iBACf,oBAAChC;QACCC,aAAaA;QACbC,eAAeA;SAEf;AAGV,GACD"}