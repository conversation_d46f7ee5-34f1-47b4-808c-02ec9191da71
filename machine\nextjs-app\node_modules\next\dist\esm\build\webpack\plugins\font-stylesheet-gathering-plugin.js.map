{"version": 3, "sources": ["../../../../src/build/webpack/plugins/font-stylesheet-gathering-plugin.ts"], "names": ["webpack", "BasicEvaluatedExpression", "sources", "getFontDefinitionFromNetwork", "getFontOverrideCss", "postcss", "minifier", "FONT_MANIFEST", "OPTIMIZED_FONT_PROVIDERS", "Log", "minifyCss", "css", "excludeAll", "discardComments", "normalizeWhitespace", "exclude", "process", "from", "undefined", "then", "res", "isNodeCreatingLinkElement", "node", "callee", "type", "componentNode", "arguments", "name", "value", "FontStylesheetGatheringPlugin", "constructor", "adjustFontFallbacks", "adjustFontFallbacksWithSizeAdjust", "gatheredStylesheets", "manifestContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "factory", "JS_TYPES", "hooks", "parser", "for", "tap", "evaluate", "state", "module", "resource", "includes", "result", "setRang<PERSON>", "range", "setExpression", "setIdentifier", "getMembers", "jsxNodeHandler", "length", "arg1", "propsNode", "props", "properties", "for<PERSON>ach", "prop", "key", "rel", "href", "some", "url", "startsWith", "push", "buildInfo", "valueDependencies", "set", "call", "apply", "compiler", "normalModuleFactory", "make", "tapAsync", "compilation", "cb", "finishModules", "modules", "modulesFinished", "fontStylesheets", "fontUrls", "Set", "fontDependencies", "get", "v", "add", "Array", "fontDefinitionPromises", "map", "promiseIndex", "content", "err", "warn", "console", "error", "assets", "RawSource", "JSON", "stringify", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA,SACEA,OAAO,EACPC,wBAAwB,EACxBC,OAAO,QACF,qCAAoC;AAC3C,SACEC,4BAA4B,EAC5BC,kBAAkB,QAEb,6BAA4B;AACnC,OAAOC,aAAa,UAAS;AAC7B,OAAOC,cAAc,oCAAmC;AACxD,SACEC,aAAa,EACbC,wBAAwB,QACnB,gCAA+B;AACtC,YAAYC,SAAS,mBAAkB;AAEvC,SAASC,UAAUC,GAAW;IAC5B,OAAON,QAAQ;QACbC,SACE;YACEM,YAAY;YACZC,iBAAiB;YACjBC,qBAAqB;gBAAEC,SAAS;YAAM;QACxC,GACAV;KAEH,EACEW,OAAO,CAACL,KAAK;QAAEM,MAAMC;IAAU,GAC/BC,IAAI,CAAC,CAACC,MAAQA,IAAIT,GAAG;AAC1B;AAEA,SAASU,0BAA0BC,IAAS;IAC1C,MAAMC,SAASD,KAAKC,MAAM;IAC1B,IAAIA,OAAOC,IAAI,KAAK,cAAc;QAChC,OAAO;IACT;IACA,MAAMC,gBAAgBH,KAAKI,SAAS,CAAC,EAAE;IACvC,IAAID,cAAcD,IAAI,KAAK,WAAW;QACpC,OAAO;IACT;IACA,0BAA0B;IAC1B,0BAA0B;IAC1B,OACE,AAACD,CAAAA,OAAOI,IAAI,KAAK,UAAUJ,OAAOI,IAAI,KAAK,OAAM,KACjDF,cAAcG,KAAK,KAAK;AAE5B;AAEA,OAAO,MAAMC;IAOXC,YAAY,EACVC,mBAAmB,EACnBC,iCAAiC,EAIlC,CAAE;aAXHC,sBAAqC,EAAE;aACvCC,kBAAgC,EAAE;aAe1BC,gBAAgB,CACtBC;YAEA,MAAMC,WAAW;gBAAC;gBAAQ;gBAAO;aAAU;YAC3C,uEAAuE;YACvE,KAAK,MAAMb,QAAQa,SAAU;gBAC3BD,QAAQE,KAAK,CAACC,MAAM,CACjBC,GAAG,CAAC,gBAAgBhB,MACpBiB,GAAG,CAAC,IAAI,CAACX,WAAW,CAACH,IAAI,EAAE,CAACY;oBAC3B;;;;;;;;WAQC,GACDA,OAAOD,KAAK,CAACI,QAAQ,CAClBF,GAAG,CAAC,cACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACH,IAAI,EAAE,CAACL;4BAEvBiB,sBAAAA;wBADJ,qDAAqD;wBACrD,IAAIA,2BAAAA,gBAAAA,OAAQI,KAAK,sBAAbJ,uBAAAA,cAAeK,MAAM,qBAArBL,qBAAuBM,QAAQ,CAACC,QAAQ,CAAC,iBAAiB;4BAC5D;wBACF;wBACA,IAAIC;wBACJ,IAAIzB,KAAKK,IAAI,KAAK,UAAUL,KAAKK,IAAI,KAAK,SAAS;4BACjDoB,SAAS,IAAI9C;4BACb,aAAa;4BACb8C,OAAOC,QAAQ,CAAC1B,KAAK2B,KAAK;4BAC1BF,OAAOG,aAAa,CAAC5B;4BACrByB,OAAOI,aAAa,CAAC7B,KAAKK,IAAI;4BAE9B,+BAA+B;4BAC/BoB,OAAOK,UAAU,GAAG,IAAM,EAAE;wBAC9B;wBACA,OAAOL;oBACT;oBAEF,MAAMM,iBAAiB,CAAC/B;4BA0CJiB,sBAAAA;wBAzClB,IAAIjB,KAAKI,SAAS,CAAC4B,MAAM,KAAK,GAAG;4BAC/B,uEAAuE;4BACvE;wBACF;wBACA,IAAI,CAACjC,0BAA0BC,OAAO;4BACpC;wBACF;wBAEA,kEAAkE;wBAClE,MAAMiC,OAAOjC,KAAKI,SAAS,CAAC,EAAE;wBAE9B,MAAM8B,YACJD,KAAK/B,IAAI,KAAK,qBAAsB+B,OAAerC;wBACrD,MAAMuC,QAAmC,CAAC;wBAC1C,IAAID,WAAW;4BACbA,UAAUE,UAAU,CAACC,OAAO,CAAC,CAACC;gCAC5B,IAAIA,KAAKpC,IAAI,KAAK,YAAY;oCAC5B;gCACF;gCACA,IACEoC,KAAKC,GAAG,CAACrC,IAAI,KAAK,gBAClBoC,KAAKhC,KAAK,CAACJ,IAAI,KAAK,WACpB;oCACAiC,KAAK,CAACG,KAAKC,GAAG,CAAClC,IAAI,CAAC,GAAGiC,KAAKhC,KAAK,CAACA,KAAK;gCACzC;4BACF;wBACF;wBAEA,IACE,CAAC6B,MAAMK,GAAG,IACVL,MAAMK,GAAG,KAAK,gBACd,CAACL,MAAMM,IAAI,IACX,CAACvD,yBAAyBwD,IAAI,CAAC,CAAC,EAAEC,GAAG,EAAE,GACrCR,MAAMM,IAAI,CAACG,UAAU,CAACD,OAExB;4BACA,OAAO;wBACT;wBAEA,IAAI,CAAChC,mBAAmB,CAACkC,IAAI,CAACV,MAAMM,IAAI;wBAExC,MAAMK,YAAY7B,2BAAAA,gBAAAA,OAAQI,KAAK,sBAAbJ,uBAAAA,cAAeK,MAAM,qBAArBL,qBAAuB6B,SAAS;wBAElD,IAAIA,WAAW;4BACbA,UAAUC,iBAAiB,CAACC,GAAG,CAC7B/D,eACA,IAAI,CAAC0B,mBAAmB;wBAE5B;oBACF;oBAEA,uBAAuB;oBACvBM,OAAOD,KAAK,CAACiC,IAAI,CACd/B,GAAG,CAAC,QACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACH,IAAI,EAAE0B;oBAC9B,yBAAyB;oBACzBd,OAAOD,KAAK,CAACiC,IAAI,CACd/B,GAAG,CAAC,SACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACH,IAAI,EAAE0B;oBAC9B,2BAA2B;oBAC3Bd,OAAOD,KAAK,CAACiC,IAAI,CACd/B,GAAG,CAAC,gBACJC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACH,IAAI,EAAE0B;gBAChC;YACJ;QACF;QA7GE,IAAI,CAACtB,mBAAmB,GAAGA;QAC3B,IAAI,CAACC,iCAAiC,GAAGA;IAC3C;IA6GOwC,MAAMC,QAA0B,EAAE;QACvC,IAAI,CAACA,QAAQ,GAAGA;QAChBA,SAASnC,KAAK,CAACoC,mBAAmB,CAACjC,GAAG,CACpC,IAAI,CAACX,WAAW,CAACH,IAAI,EACrB,IAAI,CAACQ,aAAa;QAEpBsC,SAASnC,KAAK,CAACqC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC9C,WAAW,CAACH,IAAI,EAAE,CAACkD,aAAaC;YAChED,YAAYvC,KAAK,CAACyC,aAAa,CAACH,QAAQ,CACtC,IAAI,CAAC9C,WAAW,CAACH,IAAI,EACrB,OAAOqD,SAAcC;gBACnB,IAAIC,kBAAkB,IAAI,CAACjD,mBAAmB;gBAE9C,MAAMkD,WAAW,IAAIC;gBACrBJ,QAAQrB,OAAO,CAAC,CAACf;wBAEbA,qCAAAA;oBADF,MAAMyC,mBACJzC,2BAAAA,oBAAAA,OAAQwB,SAAS,sBAAjBxB,sCAAAA,kBAAmByB,iBAAiB,qBAApCzB,oCAAsC0C,GAAG,CAAC/E;oBAC5C,IAAI8E,kBAAkB;wBACpBA,iBAAiB1B,OAAO,CAAC,CAAC4B,IAAcJ,SAASK,GAAG,CAACD;oBACvD;gBACF;gBAEAL,kBAAkBO,MAAMxE,IAAI,CAACkE;gBAE7B,MAAMO,yBAAyBR,gBAAgBS,GAAG,CAAC,CAAC1B,MAClD9D,6BAA6B8D;gBAG/B,IAAI,CAAC/B,eAAe,GAAG,EAAE;gBACzB,IAAK,IAAI0D,gBAAgBF,uBAAwB;oBAC/C,IAAI/E,MAAM,MAAM+E,sBAAsB,CAACE,aAAa;oBAEpD,IAAI,IAAI,CAAC7D,mBAAmB,EAAE;wBAC5BpB,OAAOP,mBACL8E,eAAe,CAACU,aAAa,EAC7BjF,KACA,IAAI,CAACqB,iCAAiC;oBAE1C;oBAEA,IAAIrB,KAAK;wBACP,IAAI;4BACF,MAAMkF,UAAU,MAAMnF,UAAUC;4BAChC,IAAI,CAACuB,eAAe,CAACiC,IAAI,CAAC;gCACxBF,KAAKiB,eAAe,CAACU,aAAa;gCAClCC;4BACF;wBACF,EAAE,OAAOC,KAAK;4BACZrF,IAAIsF,IAAI,CACN,CAAC,oCAAoC,EAAEb,eAAe,CAACU,aAAa,CAAC,+BAA+B,CAAC;4BAEvGI,QAAQC,KAAK,CAACH;wBAChB;oBACF;gBACF;gBAEA,uCAAuC;gBACvCjB,YAAYqB,MAAM,CAAC3F,cAAc,GAAG,IAAIL,QAAQiG,SAAS,CACvDC,KAAKC,SAAS,CAAC,IAAI,CAACnE,eAAe,EAAE,MAAM;gBAG7C+C;YACF;YAEFH;QACF;QAEAL,SAASnC,KAAK,CAACqC,IAAI,CAAClC,GAAG,CAAC,IAAI,CAACX,WAAW,CAACH,IAAI,EAAE,CAACkD;YAC9CA,YAAYvC,KAAK,CAACgE,aAAa,CAAC7D,GAAG,CACjC;gBACEd,MAAM,IAAI,CAACG,WAAW,CAACH,IAAI;gBAC3B4E,OAAOvG,QAAQwG,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACP;gBACCA,MAAM,CAAC,QAAQ3F,cAAc,GAAG,IAAIL,QAAQiG,SAAS,CACnDC,KAAKC,SAAS,CAAC,IAAI,CAACnE,eAAe,EAAE,MAAM;YAE/C;QAEJ;IACF;AACF"}