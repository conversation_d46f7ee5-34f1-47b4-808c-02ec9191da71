/**
 * @license React
 * react-server-dom-webpack-client.browser.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var r=require("react-dom"),t=require("react"),u={stream:!0};function v(a,b){if(a){var c=a[b.id];if(a=c[b.name])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b.id+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b.name}return{id:a.id,chunks:a.chunks,name:c,async:!!b.async}}return b}var w=new Map;
function x(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function y(){}
function z(a){for(var b=a.chunks,c=[],d=0;d<b.length;d++){var m=b[d],k=w.get(m);if(void 0===k){k=globalThis.__next_chunk_load__(m);c.push(k);var n=w.set.bind(w,m,null);k.then(n,y);w.set(m,k)}else null!==k&&c.push(k)}return a.async?0===c.length?x(a.id):Promise.all(c).then(function(){return x(a.id)}):0<c.length?Promise.all(c):null}var A=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),D=Symbol.for("react.lazy"),E=Symbol.for("react.default_value"),F=Symbol.iterator;
function H(a){if(null===a||"object"!==typeof a)return null;a=F&&a[F]||a["@@iterator"];return"function"===typeof a?a:null}var aa=Array.isArray,I=new WeakMap;function ba(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ca(a,b,c,d){function m(l,e){if(null===e)return null;if("object"===typeof e){if("function"===typeof e.then){null===g&&(g=new FormData);n++;var h=k++;e.then(function(p){p=JSON.stringify(p,m);var q=g;q.append(b+h,p);n--;0===n&&c(q)},function(p){d(p)});return"$@"+h.toString(16)}if(e instanceof FormData){null===g&&(g=new FormData);var f=g;l=k++;var C=b+l+"_";e.forEach(function(p,q){f.append(C+q,p)});return"$K"+l.toString(16)}return e instanceof Map?(e=JSON.stringify(Array.from(e),m),null===g&&
(g=new FormData),l=k++,g.append(b+l,e),"$Q"+l.toString(16)):e instanceof Set?(e=JSON.stringify(Array.from(e),m),null===g&&(g=new FormData),l=k++,g.append(b+l,e),"$W"+l.toString(16)):!aa(e)&&H(e)?Array.from(e):e}if("string"===typeof e){if("Z"===e[e.length-1]&&this[l]instanceof Date)return"$D"+e;e="$"===e[0]?"$"+e:e;return e}if("boolean"===typeof e)return e;if("number"===typeof e)return ba(e);if("undefined"===typeof e)return"$undefined";if("function"===typeof e){e=I.get(e);if(void 0!==e)return e=JSON.stringify(e,
m),null===g&&(g=new FormData),l=k++,g.set(b+l,e),"$F"+l.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof e){l=e.description;if(Symbol.for(l)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+(e.description+") cannot be found among global symbols."));return"$S"+l}if("bigint"===typeof e)return"$n"+
e.toString(10);throw Error("Type "+typeof e+" is not supported as an argument to a Server Function.");}var k=1,n=0,g=null;a=JSON.stringify(a,m);null===g?c(a):(g.set(b+"0",a),0===n&&c(g))}function J(a,b){I.set(a,b)}var K=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function L(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}L.prototype=Object.create(Promise.prototype);
L.prototype.then=function(a,b){switch(this.status){case "resolved_model":M(this);break;case "resolved_module":N(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function da(a){switch(a.status){case "resolved_model":M(a);break;case "resolved_module":N(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function O(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function P(a,b,c){switch(a.status){case "fulfilled":O(b,a.value);break;case "pending":case "blocked":a.value=b;a.reason=c;break;case "rejected":c&&O(c,a.reason)}}
function R(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&O(c,b)}}function S(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(N(a),P(a,c,d))}}var T=null,U=null;
function M(a){var b=T,c=U;T=a;U=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==U&&0<U.deps?(U.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(m){a.status="rejected",a.reason=m}finally{T=b,U=c}}
function N(a){try{var b=a.value,c=globalThis.__next_require__(b.id);if(b.async&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b.name?c:""===b.name?c.__esModule?c.default:c:c[b.name];a.status="fulfilled";a.value=d}catch(m){a.status="rejected",a.reason=m}}function V(a,b){a._chunks.forEach(function(c){"pending"===c.status&&R(c,b)})}function W(a,b){var c=a._chunks,d=c.get(b);d||(d=new L("pending",null,null,a),c.set(b,d));return d}
function ea(a,b,c){if(U){var d=U;d.deps++}else d=U={deps:1,value:null};return function(m){b[c]=m;d.deps--;0===d.deps&&"blocked"===a.status&&(m=a.value,a.status="fulfilled",a.value=d.value,null!==m&&O(m,d.value))}}function fa(a){return function(b){return R(a,b)}}
function ha(a,b){function c(){var m=Array.prototype.slice.call(arguments),k=b.bound;return k?"fulfilled"===k.status?d(b.id,k.value.concat(m)):Promise.resolve(k).then(function(n){return d(b.id,n.concat(m))}):d(b.id,m)}var d=a._callServer;J(c,b);return c}function X(a,b){a=W(a,b);switch(a.status){case "resolved_model":M(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ia(a,b,c,d){if("$"===d[0]){if("$"===d)return B;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=W(a,b),{$$typeof:D,_payload:a,_init:da};case "@":return b=parseInt(d.slice(2),16),W(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),K[a]||(K[a]=t.createServerContext(a,E)),K[a].Provider;case "F":return b=parseInt(d.slice(2),16),b=X(a,b),ha(a,b);case "Q":return b=parseInt(d.slice(2),16),a=X(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),
16),a=X(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=W(a,d);switch(a.status){case "resolved_model":M(a);break;case "resolved_module":N(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=T,a.then(ea(d,b,c),fa(d)),null;default:throw a.reason;}}}return d}
function ja(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function Y(a,b){var c=new Map;a={_bundlerConfig:a,_callServer:void 0!==b?b:ja,_chunks:c,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=ka(a);return a}
function la(a,b,c){var d=a._chunks,m=d.get(b);c=JSON.parse(c,a._fromJSON);var k=v(a._bundlerConfig,c);if(c=z(k)){if(m){var n=m;n.status="blocked"}else n=new L("blocked",null,null,a),d.set(b,n);c.then(function(){return S(n,k)},function(g){return R(n,g)})}else m?S(m,k):d.set(b,new L("resolved_module",k,null,a))}
function ka(a){return function(b,c){return"string"===typeof c?ia(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===B?{$$typeof:B,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}
function Z(a,b){function c(k){var n=k.value;if(k.done)V(a,Error("Connection closed."));else{var g=0,l=a._rowState,e=a._rowID,h=a._rowTag,f=a._rowLength;k=a._buffer;for(var C=n.length;g<C;){var p=-1;switch(l){case 0:p=n[g++];58===p?l=1:e=e<<4|(96<p?p-87:p-48);continue;case 1:l=n[g];84===l?(h=l,l=2,g++):64<l&&91>l?(h=l,l=3,g++):(h=0,l=3);continue;case 2:p=n[g++];44===p?l=4:f=f<<4|(96<p?p-87:p-48);continue;case 3:p=n.indexOf(10,g);break;case 4:p=g+f,p>n.length&&(p=-1)}var q=n.byteOffset+g;if(-1<p){g=
new Uint8Array(n.buffer,q,p-g);f=a;q=h;var Q=f._stringDecoder;h="";for(var G=0;G<k.length;G++)h+=Q.decode(k[G],u);h+=Q.decode(g);switch(q){case 73:la(f,e,h);break;case 72:e=h[0];h=h.slice(1);f=JSON.parse(h,f._fromJSON);if(h=A.current)switch(e){case "D":h.prefetchDNS(f);break;case "C":"string"===typeof f?h.preconnect(f):h.preconnect(f[0],f[1]);break;case "L":e=f[0];g=f[1];3===f.length?h.preload(e,g,f[2]):h.preload(e,g);break;case "m":"string"===typeof f?h.preloadModule(f):h.preloadModule(f[0],f[1]);
break;case "S":"string"===typeof f?h.preinitStyle(f):h.preinitStyle(f[0],0===f[1]?void 0:f[1],3===f.length?f[2]:void 0);break;case "X":"string"===typeof f?h.preinitScript(f):h.preinitScript(f[0],f[1]);break;case "M":"string"===typeof f?h.preinitModuleScript(f):h.preinitModuleScript(f[0],f[1])}break;case 69:h=JSON.parse(h);g=h.digest;h=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
h.stack="Error: "+h.message;h.digest=g;g=f._chunks;(q=g.get(e))?R(q,h):g.set(e,new L("rejected",null,h,f));break;case 84:f._chunks.set(e,new L("fulfilled",h,null,f));break;default:g=f._chunks,(q=g.get(e))?(f=q,e=h,"pending"===f.status&&(h=f.value,g=f.reason,f.status="resolved_model",f.value=e,null!==h&&(M(f),P(f,h,g)))):g.set(e,new L("resolved_model",h,null,f))}g=p;3===l&&g++;f=e=h=l=0;k.length=0}else{n=new Uint8Array(n.buffer,q,n.byteLength-g);k.push(n);f-=n.byteLength;break}}a._rowState=l;a._rowID=
e;a._rowTag=h;a._rowLength=f;return m.read().then(c).catch(d)}}function d(k){V(a,k)}var m=b.getReader();m.read().then(c).catch(d)}exports.createFromFetch=function(a,b){var c=Y(null,b&&b.callServer?b.callServer:void 0);a.then(function(d){Z(c,d.body)},function(d){V(c,d)});return W(c,0)};exports.createFromReadableStream=function(a,b){b=Y(null,b&&b.callServer?b.callServer:void 0);Z(b,a);return W(b,0)};
exports.createServerReference=function(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}J(c,{id:a,bound:null});return c};exports.encodeReply=function(a){return new Promise(function(b,c){ca(a,"",b,c)})};
