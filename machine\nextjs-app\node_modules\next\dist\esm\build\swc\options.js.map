{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "names": ["nextDistPath", "regeneratorRuntimePath", "require", "resolve", "getParserOptions", "filename", "jsConfig", "rest", "isTSFile", "endsWith", "isTypeScript", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "swcCacheDir", "isServerLayer", "bundleTarget", "hasServerComponents", "isServerActionsEnabled", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragma", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "serverComponents", "isServer", "serverActions", "enabled", "styledComponentsConfig", "displayName", "emotionConfig", "autoLabel", "sourcemap", "importMap", "labelFormat", "sourceMap", "getJestSWCOptions", "esm", "pagesDir", "baseOptions", "isNextDist", "test", "env", "targets", "node", "module", "type", "disableNextSsg", "disablePageConfig", "getLoaderSWCOptions", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "supportedBrowsers", "relativeFilePathFromRoot", "optimizeBarrelExports", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isDevelopment", "length", "target"], "mappings": "AASA,MAAMA,eACJ;AAEF,MAAMC,yBAAyBC,QAAQC,OAAO,CAC5C;AAGF,OAAO,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWH,SAASI,QAAQ,CAAC;IACnC,MAAMC,eAAeF,YAAYH,SAASI,QAAQ,CAAC;IACnD,MAAME,mBAAmBC,QACvBN,6BAAAA,4BAAAA,SAAUO,eAAe,qBAAzBP,0BAA2BQ,sBAAsB;IAEnD,OAAO;QACL,GAAGP,IAAI;QACPQ,QAAQL,eAAe,eAAe;QACtCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,eAAe,QAAQ,MAAM,EAAE,CAACF;QACjCU,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBd,QAAQ,EACRe,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,iBAAiB,EACjBC,UAAU,EACVZ,eAAe,EACfa,eAAe,EACfpB,QAAQ,EACRqB,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,mBAAmB,EACnBC,sBAAsB,EAiBvB;QAEezB,2BAEZA,4BAGAA,4BAGAA,4BAoCQA;IA7CV,MAAM0B,eAAe5B,iBAAiB;QAAEC;QAAUC;IAAS;IAC3D,MAAM2B,QAAQ3B,6BAAAA,4BAAAA,SAAUO,eAAe,qBAAzBP,0BAA2B2B,KAAK;IAC9C,MAAMtB,mBAAmBC,QACvBN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2BQ,sBAAsB;IAEnD,MAAMoB,wBAAwBtB,QAC5BN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2B4B,qBAAqB;IAElD,MAAMC,0BAA0BvB,QAC9BN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2B6B,uBAAuB;IAEpD,MAAMC,UAAU,AAACX,CAAAA,cAAc,EAAE,AAAD,EAC7BY,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAACxC,QAAQC,OAAO,CAACsC;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAIjB,mBAAmBO,QACnB;gBACEW,SAASlB;gBACTO;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAAC5B;YAC3C6B,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAW1B;YACb;YACA2B,WAAW;gBACT,sIAAsI;gBACtI,GAAIlC,OACA;oBACEmC,QAAQ;wBACNnC,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACNoC,iBAAiB7C;gBACjB8C,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACErD,CAAAA,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2BsD,eAAe,KACzC/C,CAAAA,CAAAA,mCAAAA,gBAAiBgD,OAAO,KAAI,CAACjC,gBAC1B,mBACA,OAAM;oBACZkC,SAAS;oBACTC,QAAQ;oBACRC,YAAY;oBACZC,kBAAkB;oBAClB5C,aAAa,CAAC,CAACA;oBACf6C,aAAa;oBACbC,SAAS,CAAC,CAAC7C;gBACb;gBACA8C,WAAW;oBACTC,UAAU;oBACVC,SAASlD,OACL,OACA;wBACEmD,SAAS;4BACPC,QAAQjD,eAAe,WAAW;wBACpC;wBACAkD,MAAM;4BACJC,UAAUrD,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACAsD,aAAa;oBACXC,YAAY3E;gBACd;YACF;QACF;QACA4E,YAAYzD,OAAO,WAAW0D;QAC9BC,aAAa,EAAElE,mCAAAA,gBAAiBkE,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuB5D,OACnB,QACAP,mCAAAA,gBAAiBmE,qBAAqB;QAC1C,wCAAwC;QACxCxD,mBAAmBA,oBACfyD,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAAC3D,mBAAmBgB,GAAG,CAAC,CAAC,CAAC4C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT/B,WACE,OAAO+B,OAAO/B,SAAS,KAAK,WACxB+B,OAAO/B,SAAS,GAChB2B,OAAOE,OAAO,CAACE,OAAO/B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC8C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAE3E,mCAAAA,gBAAiB2E,KAAK;QAC7B,kFAAkF;QAClFC,WAAW,CAAC;QACZ,2GAA2G;QAC3G,GAAI,CAAC7D,iBAAiB;YACpB,mEAAmE;YACnEiC,SAAS6B,kBAAkB7E,mCAAAA,gBAAiBgD,OAAO,EAAExC;YACrD,mEAAmE;YACnEsE,kBAAkBC,2BAChB/E,mCAAAA,gBAAiB8E,gBAAgB,EACjCtE;QAEJ,CAAC;QACDwE,kBAAkB/D,sBACd;YAAEgE,UAAU,CAAC,CAAClE;QAAc,IAC5BkD;QACJiB,eAAejE,sBACX;YACE,wEAAwE;YACxEkE,SAAS,CAAC,CAACjE;YACX+D,UAAU,CAAC,CAAClE;QACd,IACAkD;QACJjD;IACF;AACF;AAEA,SAAS+D,2BACPK,sBAAoE,EACpE5E,WAAgB;IAEhB,IAAI,CAAC4E,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAItF,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACL6E,aAAatF,QAAQS;QACvB;IACF;AACF;AAEA,SAASqE,kBACPS,aAAkD,EAClD9E,WAAoB;IAEpB,IAAI,CAAC8E,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAAC/E;IAClB,OAAQ,OAAO8E,kBAAkB,YAAYA,cAAcC,SAAS;QAClE,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;QACL;YACE;IACJ;IACA,OAAO;QACLJ,SAAS;QACTI;QACAC,WAAWhF;QACX,GAAI,OAAO8E,kBAAkB,YAAY;YACvCG,WAAWH,cAAcG,SAAS;YAClCC,aAAaJ,cAAcI,WAAW;YACtCF,WAAWhF,eAAe8E,cAAcK,SAAS;QACnD,CAAC;IACH;AACF;AAEA,OAAO,SAASC,kBAAkB,EAChCX,QAAQ,EACRzF,QAAQ,EACRqG,GAAG,EACHlF,iBAAiB,EACjBC,UAAU,EACVZ,eAAe,EACfP,QAAQ,EACRoB,eAAe,EACfiF,QAAQ,EACR7E,mBAAmB,EAYpB;IACC,IAAI8E,cAAczF,kBAAkB;QAClCd;QACAe,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAACuE;QACftE;QACAC;QACAZ;QACAP;QACAwB;QACAJ;QACA,oDAAoD;QACpDE,eAAe;QACf,oDAAoD;QACpDC,cAAc;IAChB;IAEA,MAAMgF,aAAa7G,aAAa8G,IAAI,CAACzG;IAErC,OAAO;QACL,GAAGuG,WAAW;QACdG,KAAK;YACHC,SAAS;gBACP,yCAAyC;gBACzCC,MAAMnE,QAAQC,QAAQ,CAACkE,IAAI;YAC7B;QACF;QACAC,QAAQ;YACNC,MAAMT,OAAO,CAACG,aAAa,QAAQ;QACrC;QACAO,gBAAgB;QAChBC,mBAAmB;QACnBV;IACF;AACF;AAEA,OAAO,SAASW,oBAAoB,EAClCjH,QAAQ,EACRgB,WAAW,EACXyE,QAAQ,EACRa,QAAQ,EACRY,MAAM,EACNC,UAAU,EACVlG,eAAe,EACfE,iBAAiB,EACjBiG,mBAAmB,EACnBC,sBAAsB,EACtBjG,UAAU,EACVZ,eAAe,EACfP,QAAQ,EACRqH,iBAAiB,EACjBhG,WAAW,EACXiG,wBAAwB,EACxB9F,mBAAmB,EACnBF,aAAa,EACbG,sBAAsB,EACtB8F,qBAAqB,EACrBhG,eAAe,QAAQ,EA2BxB;IACC,IAAI+E,cAAmBzF,kBAAkB;QACvCd;QACAgB;QACAE,cAAc,CAACuE;QACfxE;QACAE;QACAC;QACAZ;QACAP;QACA,mBAAmB;QACnBqB;QACAG;QACAF;QACAG;QACAF;IACF;IACA+E,YAAYkB,WAAW,GAAG;QACxBA,aAAa;YACX;YACA;YAEA,8CAA8C;YAC9C;YACA;SACD;QACDF;IACF;IACAhB,YAAYmB,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIb,uBAAuB3B,YAAY,CAACzE,aAAa;QACnDuF,YAAYa,mBAAmB,GAAG;YAChCc,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIb,wBAAwB;QAC1Bd,YAAY4B,qBAAqB,GAAG;YAClCR,UAAUN;QACZ;IACF;IACA,IAAIG,uBAAuB;QACzBjB,YAAYiB,qBAAqB,GAAGA;IACtC;IAEA,MAAMhB,aAAa7G,aAAa8G,IAAI,CAACzG;IAErC,IAAIyF,UAAU;QACZ,OAAO;YACL,GAAGc,WAAW;YACd,8FAA8F;YAC9FQ,gBAAgB;YAChBC,mBAAmB;YACnBoB,eAAepH;YACfyE;YACAa;YACAY;YACAC;YACAT,KAAK;gBACHC,SAAS;oBACP,yCAAyC;oBACzCC,MAAMnE,QAAQC,QAAQ,CAACkE,IAAI;gBAC7B;YACF;QACF;IACF,OAAO;QACL,MAAMvE,UAAU;YACd,GAAGkE,WAAW;YACd,0DAA0D;YAC1D,GAAIC,aACA;gBACEK,QAAQ;oBACNC,MAAM;gBACR;YACF,IACA,CAAC,CAAC;YACNC,gBAAgB,CAACI;YACjBiB,eAAepH;YACfyE;YACAa;YACAY;YACAC;YACA,GAAIG,qBAAqBA,kBAAkBe,MAAM,GAAG,IAChD;gBACE3B,KAAK;oBACHC,SAASW;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAACjF,QAAQqE,GAAG,EAAE;YAChB,6CAA6C;YAC7CrE,QAAQC,GAAG,CAACgG,MAAM,GAAG;QACvB;QACA,OAAOjG;IACT;AACF"}