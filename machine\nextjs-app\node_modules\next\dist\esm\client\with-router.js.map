{"version": 3, "sources": ["../../src/client/with-router.tsx"], "names": ["React", "useRouter", "with<PERSON><PERSON><PERSON>", "ComposedComponent", "WithRouterWrapper", "props", "router", "getInitialProps", "origGetInitialProps", "process", "env", "NODE_ENV", "name", "displayName"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAOzB,SAASC,SAAS,QAAQ,WAAU;AAWpC,eAAe,SAASC,WAItBC,iBAA+C;IAE/C,SAASC,kBAAkBC,KAAU;QACnC,qBAAO,oBAACF;YAAkBG,QAAQL;YAAc,GAAGI,KAAK;;IAC1D;IAEAD,kBAAkBG,eAAe,GAAGJ,kBAAkBI,eAAe;IAEnEH,kBAA0BI,mBAAmB,GAAG,AAChDL,kBACAK,mBAAmB;IACrB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,OACJT,kBAAkBU,WAAW,IAAIV,kBAAkBS,IAAI,IAAI;QAC7DR,kBAAkBS,WAAW,GAAG,AAAC,gBAAaD,OAAK;IACrD;IAEA,OAAOR;AACT"}