{"version": 3, "sources": ["../../../../src/client/components/router-reducer/invalidate-cache-by-router-state.ts"], "names": ["createRouterCache<PERSON>ey", "invalidateCacheByRouterState", "newCache", "existingCache", "routerState", "key", "segmentForParallelRoute", "cache<PERSON>ey", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "parallelRouteCacheNode", "Map", "delete", "set"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,6BACdC,QAAmB,EACnBC,aAAwB,EACxBC,WAA8B;IAE9B,+FAA+F;IAC/F,IAAK,MAAMC,OAAOD,WAAW,CAAC,EAAE,CAAE;QAChC,MAAME,0BAA0BF,WAAW,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE;QACtD,MAAME,WAAWP,qBAAqBM;QACtC,MAAME,kCACJL,cAAcM,cAAc,CAACC,GAAG,CAACL;QACnC,IAAIG,iCAAiC;YACnC,IAAIG,yBAAyB,IAAIC,IAAIJ;YACrCG,uBAAuBE,MAAM,CAACN;YAC9BL,SAASO,cAAc,CAACK,GAAG,CAACT,KAAKM;QACnC;IACF;AACF"}