{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseBabel.ts"], "names": ["Chalk", "SimpleWebpackError", "chalk", "constructor", "enabled", "getBabelError", "fileName", "err", "code", "loc", "lineNumber", "Math", "max", "line", "column", "message", "replace", "RegExp", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": "AAAA,OAAOA,WAAW,2BAA0B;AAC5C,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,MAAMC,QAAQ,IAAIF,MAAMG,WAAW,CAAC;IAAEC,SAAS;AAAK;AAEpD,OAAO,SAASC,cACdC,QAAgB,EAChBC,GAGC;IAED,IAAIA,IAAIC,IAAI,KAAK,qBAAqB;QACpC,OAAO;IACT;IAEA,uHAAuH;IACvH,IAAID,IAAIE,GAAG,EAAE;QACX,MAAMC,aAAaC,KAAKC,GAAG,CAAC,GAAGL,IAAIE,GAAG,CAACI,IAAI;QAC3C,MAAMC,SAASH,KAAKC,GAAG,CAAC,GAAGL,IAAIE,GAAG,CAACK,MAAM;QAEzC,IAAIC,UAAUR,IAAIQ,OAAO,AACvB,iEAAiE;SAChEC,OAAO,CAAC,UAAU,GACnB,yCAAyC;SACxCA,OAAO,CACN,IAAIC,OAAO,CAAC,gBAAgB,EAAEP,WAAW,CAAC,EAAEI,OAAO,gBAAgB,CAAC,GACpE;QAGJ,OAAO,IAAIb,mBACT,CAAC,EAAEC,MAAMgB,IAAI,CAACZ,UAAU,CAAC,EAAEJ,MAAMiB,MAAM,CACrCT,WAAWU,QAAQ,IACnB,CAAC,EAAElB,MAAMiB,MAAM,CAACL,OAAOM,QAAQ,IAAI,CAAC,EACtClB,MAAMmB,GAAG,CAACC,IAAI,CAAC,gBAAgBC,MAAM,CAAC,CAAC,EAAE,EAAER,QAAQ,CAAC;IAExD;IAEA,OAAO;AACT"}