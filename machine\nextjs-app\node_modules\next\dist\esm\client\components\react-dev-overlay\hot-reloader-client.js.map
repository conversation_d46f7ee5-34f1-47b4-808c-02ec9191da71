{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/hot-reloader-client.tsx"], "names": ["React", "useCallback", "useEffect", "useReducer", "useMemo", "startTransition", "stripAnsi", "formatWebpackMessages", "useRouter", "ACTION_VERSION_INFO", "INITIAL_OVERLAY_STATE", "errorOverlayReducer", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "parseStack", "ReactDevOverlay", "RuntimeError<PERSON>andler", "useErrorHandler", "useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "parseComponentStack", "HMR_ACTIONS_SENT_TO_BROWSER", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "onBeforeFastRefresh", "dispatcher", "hasUpdates", "onBeforeRefresh", "onFastRefresh", "onBuildOk", "onRefresh", "handleAvailableHash", "hash", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "sendMessage", "stackTrace", "stack", "split", "slice", "join", "message", "JSON", "stringify", "event", "hadRuntimeError", "window", "location", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "updatedModules", "console", "warn", "Boolean", "length", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "router", "handleErrors", "errors", "formatted", "warnings", "onBuildError", "i", "error", "action", "BUILDING", "log", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "isHotUpdate", "formattedMessages", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "__NEXT_DATA__", "page", "SERVER_COMPONENT_CHANGES", "fastRefresh", "RELOAD_PAGE", "REMOVED_PAGE", "ADDED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "HotReload", "assetPrefix", "children", "state", "dispatch", "type", "handleOnUnhandledError", "componentStack", "_componentStack", "reason", "frames", "componentStackFrames", "handleOnUnhandledRejection", "handleOnReactError", "webSocketRef", "processTurbopackMessage", "data", "handledByTurbopack", "websocket", "current", "addEventListener", "removeEventListener", "onReactError"], "mappings": "AACA,OAAOA,SACLC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,eAAe,QACV,QAAO;AACd,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,2BAA2B,kDAAiD;AACnF,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,mBAAmB,EACnBC,qBAAqB,EACrBC,mBAAmB,QACd,mCAAkC;AACzC,SACEC,eAAe,EACfC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,sBAAsB,EACtBC,0BAA0B,QACrB,mCAAkC;AACzC,SAASC,UAAU,QAAQ,gCAA+B;AAC1D,OAAOC,qBAAqB,6BAA4B;AACxD,SACEC,mBAAmB,EACnBC,eAAe,QACV,uCAAsC;AAC7C,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,mCAAkC;AACzC,SAASC,mBAAmB,QAAQ,2CAA0C;AAE9E,SACEC,2BAA2B,QAEtB,yCAAwC;AAU/C,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEjE,SAASC,oBAAoBC,UAAsB,EAAEC,UAAmB;IACtE,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,cAAcH,UAAsB,EAAEC,UAAmB;IAChED,WAAWI,SAAS;IACpB,IAAIH,YAAY;QACdD,WAAWK,SAAS;IACtB;AACF;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCf,4BAA4Be;AAC9B;AAEA,mDAAmD;AACnD,SAASC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOhB,8BAA8BiB;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEC,WAAgB;IACnD,MAAMC,aACJF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDN,IAAIO,OAAO,IACXP,MAAM,EAAC;IAEXC,YACEO,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPR;QACAS,iBAAiB,CAAC,CAAC/C,oBAAoB+C,eAAe;IACxD;IAGFC,OAAOC,QAAQ,CAACC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAiD,EACjDhB,WAAgB,EAChBrB,UAAsB;IAEtB,IAAI,CAACQ,uBAAuB,CAACE,mBAAmB;QAC9CV,WAAWI,SAAS;QACpB;IACF;IAEA,SAASkC,mBAAmBlB,GAAQ,EAAEmB,cAA4B;QAChE,IAAInB,OAAOpC,oBAAoB+C,eAAe,IAAI,CAACQ,gBAAgB;YACjE,IAAInB,KAAK;gBACPoB,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIzD,oBAAoB+C,eAAe,EAAE;gBAC9CS,QAAQC,IAAI,CACV;YAEJ;YACAtB,kBAAkBC,KAAKC;YACvB;QACF;QAEA,MAAMpB,aAAayC,QAAQH,eAAeI,MAAM;QAChD,IAAI,OAAON,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBpC;QACrB;QAEA,IAAIO,qBAAqB;YACvB,+DAA+D;YAC/D2B,gBACElC,aAAa,KAAO,IAAImC,gBACxBnC,aAAa,IAAMD,WAAWI,SAAS,KAAKiC,oBAC5ChB,aACArB;QAEJ,OAAO;YACLA,WAAWI,SAAS;YACpB,IAAIwC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChChC,kBAAkB;oBAChB,IAAIiC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrCrC,OAAOC,GAAG,CACPqC,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACX;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOH,mBAAmB,YAAY;YACxC,MAAMnC,aAAayC,QAAQH,eAAeI,MAAM;YAChDP,eAAenC;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOU,OAAOC,GAAG,CAACuC,KAAK;IACzB,GACCD,IAAI,CACH,CAACX;QACCD,mBAAmB,MAAMC;IAC3B,GACA,CAACnB;QACCkB,mBAAmBlB,KAAK;IAC1B;AAEN;AAEA,SAASgC,eACPC,GAAqB,EACrBhC,WAAgB,EAChBiC,MAAoC,EACpCtD,UAAsB;IAEtB,IAAI,CAAE,CAAA,YAAYqD,GAAE,GAAI;QACtB;IACF;IAEA,SAASE,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYtF,sBAAsB;YACtCqF,QAAQA;YACRE,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B1D,WAAW2D,YAAY,CAACF,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAII,IAAI,GAAGA,IAAIH,UAAUD,MAAM,CAACb,MAAM,EAAEiB,IAAK;YAChDpB,QAAQqB,KAAK,CAAC3F,UAAUuF,UAAUD,MAAM,CAACI,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIhB,QAAQC,GAAG,CAACC,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACS,UAAUD,MAAM,CAAC,EAAE;gBACtCT,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,OAAQK,IAAIS,MAAM;QAChB,KAAKvE,4BAA4BwE,QAAQ;YAAE;gBACzCvB,QAAQwB,GAAG,CAAC;gBACZ;YACF;QACA,KAAKzE,4BAA4B0E,KAAK;QACtC,KAAK1E,4BAA4B2E,IAAI;YAAE;gBACrC,IAAIb,IAAI9C,IAAI,EAAE;oBACZD,oBAAoB+C,IAAI9C,IAAI;gBAC9B;gBAEA,MAAM,EAAEiD,MAAM,EAAEE,QAAQ,EAAE,GAAGL;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAK;oBACxBrD,WAAWmE,aAAa,CAACd,IAAIe,WAAW;gBAC1C;gBACA,MAAMC,YAAY3B,QAAQc,UAAUA,OAAOb,MAAM;gBACjD,kEAAkE;gBAClE,IAAI0B,WAAW;oBACbhD,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPwC,YAAYd,OAAOb,MAAM;wBACzB4B,UAAU9E;oBACZ;oBAGF8D,aAAaC;oBACb;gBACF;gBAEA,MAAMgB,cAAc9B,QAAQgB,YAAYA,SAASf,MAAM;gBACvD,IAAI6B,aAAa;oBACfnD,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP2C,cAAcf,SAASf,MAAM;wBAC7B4B,UAAU9E;oBACZ;oBAGF,2CAA2C;oBAC3C,MAAMiF,cAAcrB,IAAIS,MAAM,KAAKvE,4BAA4B2E,IAAI;oBAEnE,iCAAiC;oBACjC,MAAMS,oBAAoBxG,sBAAsB;wBAC9CuF,UAAUA;wBACVF,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAII,IAAI,GAAGA,IAAIe,kBAAkBjB,QAAQ,CAACf,MAAM,EAAEiB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXpB,QAAQC,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAD,QAAQC,IAAI,CAACvE,UAAUyG,kBAAkBjB,QAAQ,CAACE,EAAE;oBACtD;oBAEA,0CAA0C;oBAC1C,IAAIc,aAAa;wBACfvC,gBACE,SAASyC,kBAAkB3E,UAAmB;4BAC5CF,oBAAoBC,YAAYC;wBAClC,GACA,SAAS4E,sBAAsB5E,UAAe;4BAC5C,qDAAqD;4BACrD,sDAAsD;4BACtDE,cAAcH,YAAYC;wBAC5B,GACAoB,aACArB;oBAEJ;oBACA;gBACF;gBAEAqB,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPyC,UAAU9E;gBACZ;gBAGF,MAAMiF,cACJrB,IAAIS,MAAM,KAAKvE,4BAA4B2E,IAAI,IAC9C,CAAA,CAAClC,OAAO8C,aAAa,IAAI9C,OAAO8C,aAAa,CAACC,IAAI,KAAK,SAAQ,KAChEvE;gBAEF,0CAA0C;gBAC1C,IAAIkE,aAAa;oBACfvC,gBACE,SAASyC,kBAAkB3E,UAAmB;wBAC5CF,oBAAoBC,YAAYC;oBAClC,GACA,SAAS4E,sBAAsB5E,UAAe;wBAC5C,qDAAqD;wBACrD,sDAAsD;wBACtDE,cAAcH,YAAYC;oBAC5B,GACAoB,aACArB;gBAEJ;gBACA;YACF;QACA,uDAAuD;QACvD,KAAKT,4BAA4ByF,wBAAwB;YAAE;gBACzD3D,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPyC,UAAU9E;gBACZ;gBAEF,IAAIT,oBAAoB+C,eAAe,EAAE;oBACvC,OAAOC,OAAOC,QAAQ,CAACC,MAAM;gBAC/B;gBACAjE,gBAAgB;oBACd,yCAAyC;oBACzCqF,OAAO2B,WAAW;oBAClBjF,WAAWK,SAAS;gBACtB;gBAEA,IAAIuC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAKzD,4BAA4B2F,WAAW;YAAE;gBAC5C7D,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPyC,UAAU9E;gBACZ;gBAEF,OAAOuC,OAAOC,QAAQ,CAACC,MAAM;YAC/B;QACA,KAAK3C,4BAA4B4F,YAAY;YAAE;gBAC7C,+EAA+E;gBAC/E,yCAAyC;gBACzC7B,OAAO2B,WAAW;gBAClB;YACF;QACA,KAAK1F,4BAA4B6F,UAAU;YAAE;gBAC3C,6EAA6E;gBAC7E,yCAAyC;gBACzC9B,OAAO2B,WAAW;gBAClB;YACF;QACA,KAAK1F,4BAA4B8F,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGjC;gBACtB,IAAIiC,WAAW;oBACb,MAAM,EAAE3D,OAAO,EAAEJ,KAAK,EAAE,GAAGK,KAAK2D,KAAK,CAACD;oBACtC,MAAMzB,QAAQ,IAAI2B,MAAM7D;oBACxBkC,MAAMtC,KAAK,GAAGA;oBACdgC,aAAa;wBAACM;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKtE,4BAA4BkG,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS;gBACP,MAAM,IAAID,MAAM,uBAAuB5D,KAAKC,SAAS,CAACwB;YACxD;IACF;AACF;AAEA,eAAe,SAASqC,UAAU,KAMjC;IANiC,IAAA,EAChCC,WAAW,EACXC,QAAQ,EAIT,GANiC;IAOhC,MAAM,CAACC,OAAOC,SAAS,GAAG/H,WACxBQ,qBACAD;IAEF,MAAM0B,aAAahC,QAAQ;QACzB,OAAO;YACLoC;gBACE0F,SAAS;oBAAEC,MAAMvH;gBAAgB;YACnC;YACAmF,cAAahC,OAAO;gBAClBmE,SAAS;oBAAEC,MAAMtH;oBAAoBkD;gBAAQ;YAC/C;YACAzB;gBACE4F,SAAS;oBAAEC,MAAMrH;gBAAsB;YACzC;YACA2B;gBACEyF,SAAS;oBAAEC,MAAMpH;gBAAe;YAClC;YACAwF,eAAcC,WAAW;gBACvB0B,SAAS;oBAAEC,MAAM1H;oBAAqB+F;gBAAY;YACpD;QACF;IACF,GAAG;QAAC0B;KAAS;IAEb,MAAME,yBAAyBnI,YAAY,CAACgG;QAC1C,kGAAkG;QAClG,MAAMoC,iBAAiB,AAACpC,MAAcqC,eAAe;QACrDJ,SAAS;YACPC,MAAMnH;YACNuH,QAAQtC;YACRuC,QAAQtH,WAAW+E,MAAMtC,KAAK;YAC9B8E,sBACEJ,kBAAkB3G,oBAAoB2G;QAC1C;IACF,GAAG,EAAE;IACL,MAAMK,6BAA6BzI,YAAY,CAACsI;QAC9CL,SAAS;YACPC,MAAMlH;YACNsH,QAAQA;YACRC,QAAQtH,WAAWqH,OAAO5E,KAAK;QACjC;IACF,GAAG,EAAE;IACL,MAAMgF,qBAAqB1I,YAAY;QACrCmB,oBAAoB+C,eAAe,GAAG;IACxC,GAAG,EAAE;IACL9C,gBAAgB+G,wBAAwBM;IAExC,MAAME,eAAepH,aAAauG;IAClCtG,iBAAiBmH;IACjB,MAAMnF,cAAcnC,eAAesH;IACnC,MAAMC,0BAA0BtH,aAAakC;IAE7C,MAAMiC,SAASlF;IAEfN,UAAU;QACR,MAAMkD,UAAU,CAACc;YACf,IAAI;gBACF,MAAMuB,MAAMzB,KAAK2D,KAAK,CAACzD,MAAM4E,IAAI;gBACjC,MAAMC,qBAAqBF,2CAAAA,wBAA0BpD;gBACrD,IAAI,CAACsD,oBAAoB;oBACvBvD,eAAeC,KAAKhC,aAAaiC,QAAQtD;gBAC3C;YACF,EAAE,OAAOoB,KAAU;oBAEkCA;gBADnDoB,QAAQC,IAAI,CACV,4BAA4BX,MAAM4E,IAAI,GAAG,OAAQtF,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKG,KAAK,YAAVH,aAAc,EAAC;YAEpE;QACF;QAEA,MAAMwF,YAAYJ,aAAaK,OAAO;QACtC,IAAID,WAAW;YACbA,UAAUE,gBAAgB,CAAC,WAAW9F;QACxC;QAEA,OAAO,IAAM4F,aAAaA,UAAUG,mBAAmB,CAAC,WAAW/F;IACrE,GAAG;QAACK;QAAaiC;QAAQkD;QAAcxG;QAAYyG;KAAwB;IAE3E,qBACE,oBAAC1H;QAAgBiI,cAAcT;QAAoBV,OAAOA;OACvDD;AAGP"}