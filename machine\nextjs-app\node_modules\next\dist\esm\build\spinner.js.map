{"version": 3, "sources": ["../../src/build/spinner.ts"], "names": ["ora", "Log", "dots<PERSON>pinner", "frames", "interval", "createSpinner", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "prefixes", "info", "suffixText", "event", "process", "stdout", "isTTY", "undefined", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "resetLog"], "mappings": "AAAA,OAAOA,SAAS,yBAAwB;AACxC,YAAYC,SAAS,eAAc;AAEnC,MAAMC,cAAc;IAClBC,QAAQ;QAAC;QAAK;QAAM;KAAM;IAC1BC,UAAU;AACZ;AAEA,eAAe,SAASC,cACtBC,IAAY,EACZC,UAAuB,CAAC,CAAC,EACzBC,QAAkCC,QAAQC,GAAG;IAE7C,IAAIC;IAEJ,MAAMC,aAAa,CAAC,CAAC,EAAEX,IAAIY,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAER,KAAK,CAAC;IAClD,uEAAuE;IACvE,MAAMS,aAAa,CAAC,GAAG,EAAEd,IAAIY,QAAQ,CAACG,KAAK,CAAC,CAAC,EAAEV,KAAK,CAAC;IAErD,IAAIW,QAAQC,MAAM,CAACC,KAAK,EAAE;QACxBR,UAAUX,IAAI;YACZM,MAAMc;YACNR;YACAD,SAAST;YACTmB,QAAQJ,QAAQC,MAAM;YACtB,GAAGX,OAAO;QACZ,GAAGe,KAAK;QAER,2DAA2D;QAC3D,+DAA+D;QAC/D,MAAMC,UAAUd,QAAQC,GAAG;QAC3B,MAAMc,WAAWf,QAAQgB,IAAI;QAC7B,MAAMC,YAAYjB,QAAQkB,KAAK;QAC/B,MAAMC,WAAWjB,QAAQkB,IAAI,CAACC,IAAI,CAACnB;QACnC,MAAMoB,qBAAqBpB,QAAQqB,cAAc,CAACF,IAAI,CAACnB;QAEvD,MAAMsB,YAAY,CAACC,QAAaC;YAC9BP;YACAM,UAAUC;YACVxB,QAASW,KAAK;QAChB;QAEAb,QAAQC,GAAG,GAAG,CAAC,GAAGyB,OAAcF,UAAUV,SAASY;QACnD1B,QAAQgB,IAAI,GAAG,CAAC,GAAGU,OAAcF,UAAUT,UAAUW;QACrD1B,QAAQkB,KAAK,GAAG,CAAC,GAAGQ,OAAcF,UAAUP,WAAWS;QAEvD,MAAMC,WAAW;YACf3B,QAAQC,GAAG,GAAGa;YACdd,QAAQgB,IAAI,GAAGD;YACff,QAAQkB,KAAK,GAAGD;QAClB;QACAf,QAAQkB,IAAI,GAAG;YACbD;YACAQ;YACA,OAAOzB;QACT;QACAA,QAAQqB,cAAc,GAAG;YACvB,IAAIjB,YAAY;gBACd,IAAIJ,SAAS;oBACXA,QAAQL,IAAI,GAAGS;gBACjB,OAAO;oBACLP,MAAMO;gBACR;YACF;YACAgB;YACAK;YACA,OAAOzB;QACT;IACF,OAAO,IAAIC,cAAcN,MAAM;QAC7BE,MAAMI,aAAaA,aAAa,QAAQN;IAC1C;IAEA,OAAOK;AACT"}