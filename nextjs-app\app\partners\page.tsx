import { Card, CardContent } from '@/components/ui/card';

export default function Partners() {
  const partners = [
    {
      name: "Nike",
      category: "Athletic Apparel",
      description: "Official apparel partner providing high-quality workout gear for our members.",
      logo: "https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop"
    },
    {
      name: "Optimum Nutrition",
      category: "Supplements",
      description: "Premium sports nutrition and supplements for optimal performance and recovery.",
      logo: "https://images.pexels.com/photos/4088012/pexels-photo-4088012.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop"
    },
    {
      name: "TechnoGym",
      category: "Equipment",
      description: "State-of-the-art fitness equipment and technology solutions.",
      logo: "https://images.pexels.com/photos/1552103/pexels-photo-1552103.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop"
    },
    {
      name: "MyFitnessPal",
      category: "Technology",
      description: "Nutrition tracking and meal planning integration for our members.",
      logo: "https://images.pexels.com/photos/4498479/pexels-photo-4498479.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop"
    },
    {
      name: "Local Sports Medicine",
      category: "Healthcare",
      description: "Professional sports medicine and physical therapy services.",
      logo: "https://images.pexels.com/photos/356040/pexels-photo-356040.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop"
    },
    {
      name: "Healthy Eats",
      category: "Nutrition",
      description: "Fresh, healthy meal prep and nutrition counseling services.",
      logo: "https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop"
    }
  ];

  const benefits = [
    {
      title: "Member Discounts",
      description: "Exclusive discounts on products and services from our partner companies.",
      icon: "💰"
    },
    {
      title: "Premium Products",
      description: "Access to high-quality equipment, supplements, and gear.",
      icon: "⭐"
    },
    {
      title: "Expert Services",
      description: "Professional services from certified specialists and experts.",
      icon: "🏆"
    },
    {
      title: "Latest Technology",
      description: "Cutting-edge fitness technology and innovative solutions.",
      icon: "🚀"
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">Our Partners</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We collaborate with industry leaders to bring you the best fitness experience possible.
            </p>
          </div>
        </div>
      </section>

      {/* Partners Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Trusted Partners</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These partnerships allow us to offer you premium products, services, and exclusive member benefits.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {partners.map((partner, index) => (
              <Card key={index} className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="h-48 overflow-hidden">
                  <img 
                    src={partner.logo}
                    alt={partner.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-gray-900">{partner.name}</h3>
                    <span className="text-sm bg-gray-100 text-gray-600 px-2 py-1 rounded">
                      {partner.category}
                    </span>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    {partner.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Partnership Benefits</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our partnerships bring exclusive advantages to all Hardwork Gym members.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center shadow-lg border-0">
                <CardContent className="p-8">
                  <div className="text-4xl mb-4">{benefit.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Inquiry */}
      <section className="py-20 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Interested in Partnering?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            We're always looking for like-minded companies that share our commitment to fitness and wellness. 
            Join our network of trusted partners.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="mailto:<EMAIL>"
              className="bg-white text-black px-8 py-4 rounded-md font-semibold hover:bg-gray-200 transition-colors"
            >
              Contact Partnership Team
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}