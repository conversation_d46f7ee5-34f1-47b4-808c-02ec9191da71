{"version": 3, "sources": ["../../../src/client/dev/hot-middleware-client.ts"], "names": ["connect", "sendMessage", "mode", "devClient", "subscribeToHmrEvent", "obj", "isOnErrorPage", "window", "next", "router", "pathname", "action", "JSON", "stringify", "event", "clientId", "__nextDevClientId", "location", "reload", "page", "data", "components", "Error"], "mappings": "AAAA,OAAOA,aAAa,iCAAgC;AACpD,SAASC,WAAW,QAAQ,4BAA2B;AAEvD,eAAe,CAAA,CAACC;IACd,MAAMC,YAAYH,QAAQE;IAE1BC,UAAUC,mBAAmB,CAAC,CAACC;QAC7B,wFAAwF;QACxF,uHAAuH;QACvH,MAAMC,gBACJC,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK,UAChCH,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK;QAElC,OAAQL,IAAIM,MAAM;YAChB,KAAK;gBAAc;oBACjBV,YACEW,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,UAAUR,OAAOS,iBAAiB;oBACpC;oBAEF,OAAOT,OAAOU,QAAQ,CAACC,MAAM;gBAC/B;YACA,KAAK;gBAAe;oBAClB,MAAM,CAACC,KAAK,GAAGd,IAAIe,IAAI;oBACvB,IAAID,SAASZ,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,IAAIJ,eAAe;wBACzDL,YACEW,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUR,OAAOS,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOZ,OAAOU,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChB,MAAM,CAACC,KAAK,GAAGd,IAAIe,IAAI;oBACvB,IACE,AAACD,SAASZ,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,IACnC,OAAOH,OAAOC,IAAI,CAACC,MAAM,CAACY,UAAU,CAACF,KAAK,KAAK,eACjDb,eACA;wBACAL,YACEW,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUR,OAAOS,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOZ,OAAOU,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;YACL,KAAK;gBAA0B;oBAC7B;gBACF;YACA;gBAAS;oBACP,MAAM,IAAII,MAAM,uBAAuBjB,IAAIM,MAAM;gBACnD;QACF;IACF;IAEA,OAAOR;AACT,CAAA,EAAC"}