{"version": 3, "sources": ["../../../../src/build/webpack/plugins/pages-manifest-plugin.ts"], "names": ["webpack", "sources", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "getRouteFromEntrypoint", "normalizePathSep", "edgeServerPages", "nodeServerPages", "edgeServerAppPaths", "nodeServerAppPaths", "PagesManifestPlugin", "constructor", "dev", "isEdgeRuntime", "appDirEnabled", "createAssets", "compilation", "assets", "entrypoints", "pages", "appPaths", "entrypoint", "values", "pagePath", "name", "files", "getFiles", "filter", "file", "includes", "endsWith", "length", "slice", "startsWith", "RawSource", "JSON", "stringify", "apply", "compiler", "hooks", "make", "tap", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,cAAc,EACdC,kBAAkB,QACb,gCAA+B;AACtC,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,gBAAgB,QAAQ,mDAAkD;AAInF,OAAO,IAAIC,kBAAkB,CAAC,EAAC;AAC/B,OAAO,IAAIC,kBAAkB,CAAC,EAAC;AAC/B,OAAO,IAAIC,qBAAqB,CAAC,EAAC;AAClC,OAAO,IAAIC,qBAAqB,CAAC,EAAC;AAElC,mEAAmE;AACnE,2GAA2G;AAC3G,0DAA0D;AAC1D,eAAe,MAAMC;IAOnBC,YAAY,EACVC,GAAG,EACHC,aAAa,EACbC,aAAa,EAKd,CAAE;QACD,IAAI,CAACF,GAAG,GAAGA;QACX,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACC,aAAa,GAAGA;IACvB;IAEAC,aAAaC,WAAgB,EAAEC,MAAW,EAAE;QAC1C,MAAMC,cAAcF,YAAYE,WAAW;QAC3C,MAAMC,QAAuB,CAAC;QAC9B,MAAMC,WAA0B,CAAC;QAEjC,KAAK,MAAMC,cAAcH,YAAYI,MAAM,GAAI;YAC7C,MAAMC,WAAWnB,uBACfiB,WAAWG,IAAI,EACf,IAAI,CAACV,aAAa;YAGpB,IAAI,CAACS,UAAU;gBACb;YACF;YAEA,MAAME,QAAQJ,WACXK,QAAQ,GACRC,MAAM,CACL,CAACC,OACC,CAACA,KAAKC,QAAQ,CAAC,sBACf,CAACD,KAAKC,QAAQ,CAAC,0BACfD,KAAKE,QAAQ,CAAC;YAGpB,+BAA+B;YAC/B,IAAI,CAACL,MAAMM,MAAM,EAAE;gBACjB;YACF;YACA,mHAAmH;YACnH,IAAIH,OAAOH,KAAK,CAACA,MAAMM,MAAM,GAAG,EAAE;YAElC,IAAI,CAAC,IAAI,CAACnB,GAAG,EAAE;gBACb,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;oBACvBe,OAAOA,KAAKI,KAAK,CAAC;gBACpB;YACF;YACAJ,OAAOvB,iBAAiBuB;YAExB,IAAIP,WAAWG,IAAI,CAACS,UAAU,CAAC,SAAS;gBACtCb,QAAQ,CAACG,SAAS,GAAGK;YACvB,OAAO;gBACLT,KAAK,CAACI,SAAS,GAAGK;YACpB;QACF;QAEA,yEAAyE;QACzE,6DAA6D;QAC7D,IAAI,IAAI,CAACf,aAAa,EAAE;YACtBP,kBAAkBa;YAClBX,qBAAqBY;QACvB,OAAO;YACLb,kBAAkBY;YAClBV,qBAAqBW;QACvB;QAEAH,MAAM,CACJ,CAAC,EAAE,CAAC,IAAI,CAACL,GAAG,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,QAAQ,GAAG,CAAC,GAAGX,eACtD,GAAG,IAAID,QAAQiC,SAAS,CACvBC,KAAKC,SAAS,CACZ;YACE,GAAG9B,eAAe;YAClB,GAAGC,eAAe;QACpB,GACA,MACA;QAIJ,IAAI,IAAI,CAACO,aAAa,EAAE;YACtBG,MAAM,CACJ,CAAC,EAAE,CAAC,IAAI,CAACL,GAAG,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,QAAQ,GAAG,CAAC,GAAGV,mBACtD,GAAG,IAAIF,QAAQiC,SAAS,CACvBC,KAAKC,SAAS,CACZ;gBACE,GAAG5B,kBAAkB;gBACrB,GAAGC,kBAAkB;YACvB,GACA,MACA;QAGN;IACF;IAEA4B,MAAMC,QAA0B,EAAQ;QACtCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACzB;YAC9CA,YAAYuB,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEjB,MAAM;gBACNmB,OAAO3C,QAAQ4C,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAAC5B;gBACC,IAAI,CAACF,YAAY,CAACC,aAAaC;YACjC;QAEJ;IACF;AACF"}