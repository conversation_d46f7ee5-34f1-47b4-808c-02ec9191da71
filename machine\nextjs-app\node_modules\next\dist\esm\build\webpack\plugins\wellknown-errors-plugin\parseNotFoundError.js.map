{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "names": ["Chalk", "SimpleWebpackError", "createOriginalStackFrame", "chalk", "constructor", "enabled", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "getSourceFrame", "fileName", "result", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "line", "start", "column", "source", "rootDirectory", "options", "context", "modulePath", "frame", "originalCodeFrame", "lineNumber", "originalStackFrame", "toString", "getFormattedFileName", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "cyan", "yellow", "getNotFoundError", "name", "message", "errorMessage", "error", "replace", "green", "importTrace", "readableIdentifier", "requestShortener", "length", "join", "red", "bold", "err", "getImageError", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": "AAAA,OAAOA,WAAW,2BAA0B;AAC5C,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SAASC,wBAAwB,QAAQ,6DAA4D;AAGrG,MAAMC,QAAQ,IAAIH,MAAMI,WAAW,CAAC;IAAEC,SAAS;AAAK;AAEpD,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,eAAeS,eACbb,KAAU,EACVc,QAAa,EACbb,WAAgB;IAEhB,IAAI;YAiBYc,uCAAAA,4BACJA,mCAAAA;QAjBV,MAAMC,MAAMhB,MAAMgB,GAAG,GACjBhB,MAAMgB,GAAG,GACThB,MAAMiB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAChE,MAAMC,iBAAiBtB,MAAMM,MAAM,CAACgB,cAAc;QAElD,MAAMP,SAAS,MAAMpB,yBAAyB;YAC5C4B,MAAMP,IAAIQ,KAAK,CAACD,IAAI;YACpBE,QAAQT,IAAIQ,KAAK,CAACC,MAAM;YACxBC,QAAQJ;YACRK,eAAe1B,YAAY2B,OAAO,CAACC,OAAO;YAC1CC,YAAYhB;YACZiB,OAAO,CAAC;QACV;QAEA,OAAO;YACLA,OAAOhB,CAAAA,0BAAAA,OAAQiB,iBAAiB,KAAI;YACpCC,YAAYlB,CAAAA,2BAAAA,6BAAAA,OAAQmB,kBAAkB,sBAA1BnB,wCAAAA,2BAA4BkB,UAAU,qBAAtClB,sCAAwCoB,QAAQ,OAAM;YAClEV,QAAQV,CAAAA,2BAAAA,8BAAAA,OAAQmB,kBAAkB,sBAA1BnB,oCAAAA,4BAA4BU,MAAM,qBAAlCV,kCAAoCoB,QAAQ,OAAM;QAC5D;IACF,EAAE,OAAM;QACN,OAAO;YAAEJ,OAAO;YAAIE,YAAY;YAAIR,QAAQ;QAAG;IACjD;AACF;AAEA,SAASW,qBACPtB,QAAgB,EAChBR,MAAW,EACX2B,UAAmB,EACnBR,MAAe;QAGbnB;IADF,KACEA,kBAAAA,OAAO+B,OAAO,qBAAd/B,gBAAgBgC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAACpC,OAAOqC,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BnD,MAAMoD,IAAI,CAAClC;QAC3C,IAAImB,cAAcR,QAAQ;YACxBsB,qBAAqB,CAAC,CAAC,EAAEnD,MAAMqD,MAAM,CAAChB,YAAY,CAAC,EAAErC,MAAMqD,MAAM,CAC/DxB,QACA,CAAC;QACL;QAEA,OAAOsB;IACT;AACF;AAEA,OAAO,eAAeG,iBACpBjD,WAAgC,EAChCD,KAAU,EACVc,QAAgB,EAChBR,MAAW;IAEX,IACEN,MAAMmD,IAAI,KAAK,yBACf,CACEnD,CAAAA,MAAMmD,IAAI,KAAK,sBACf,gCAAgCX,IAAI,CAACxC,MAAMoD,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAErB,KAAK,EAAEE,UAAU,EAAER,MAAM,EAAE,GAAG,MAAMZ,eAC1Cb,OACAc,UACAb;QAGF,MAAMoD,eAAerD,MAAMsD,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAE3D,MAAM4D,KAAK,CAAC,MAAM,CAAC,CAAC;QAEzE,MAAMC,cAAc;YAClB,MAAMrD,cAAcL,eAAeC,OAAOC,aACvCiB,GAAG,CAAC,CAAC,EAAET,MAAM,EAAE,GACdA,OAAOiD,kBAAkB,CAACzD,YAAY0D,gBAAgB,GAEvDvC,MAAM,CACL,CAAC+B,OACCA,QACA,CAAC,0FAA0FX,IAAI,CAC7FW,SAEF,CAAC,+BAA+BX,IAAI,CAACW,SACrC,CAAC,mBAAmBX,IAAI,CAACW;YAE/B,IAAI/C,YAAYwD,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAExD,YAAYyD,IAAI,CAAC,MAAM,CAAC;QAC1E;QAEA,IAAIT,UACFxD,MAAMkE,GAAG,CAACC,IAAI,CAAC,sBACf,CAAC,EAAE,EAAEV,aAAa,CAAC,GACnB,OACAtB,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACA0B;QAEF,MAAMV,oBAAoBX,qBACxBtB,UACAR,QACA2B,YACAR;QAGF,OAAO,IAAI/B,mBAAmBqD,mBAAmBK;IACnD,EAAE,OAAOY,KAAK;QACZ,8CAA8C;QAC9C,OAAOhE;IACT;AACF;AAEA,OAAO,eAAeiE,cACpBhE,WAAgB,EAChBD,KAAU,EACVgE,GAAU;IAEV,IAAIA,IAAIb,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAM/C,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,MAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,QAAQ;QACtB,OAAO;IACT;IACA,MAAM4D,OAAOzD,OAAO0D,UAAU,CAACZ,OAAO,CAAC,uBAAuB;IAC9D,MAAMa,eAAe9D,OAAO6D,UAAU;IACtC,MAAMzC,SAASjB,OAAOa,cAAc,GAAG+C,MAAM,GAAGlC,QAAQ,CAAC;IACzD,IAAIF,aAAa,CAAC;IAClBP,OAAO4C,KAAK,CAAC,MAAMC,IAAI,CAAC,CAAChD;QACvBU;QACA,OAAOV,KAAKiD,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAI1E,mBACT,CAAC,EAAEE,MAAMoD,IAAI,CAACkB,MAAM,CAAC,EAAEtE,MAAMqD,MAAM,CAAChB,WAAWE,QAAQ,IAAI,CAAC,EAC5DvC,MAAMkE,GAAG,CACNC,IAAI,CAAC,SACLU,MAAM,CACL,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAG1H"}