{"version": 3, "sources": ["../../../src/client/components/bailout-to-client-rendering.ts"], "names": ["suspense", "staticGenerationAsyncStorage", "bailoutToClientRendering", "staticGenerationStore", "getStore", "forceStatic", "isStaticGeneration"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,+CAA8C;AACvE,SAASC,4BAA4B,QAAQ,6CAA4C;AAEzF,OAAO,SAASC;IACd,MAAMC,wBAAwBF,6BAA6BG,QAAQ;IAEnE,IAAID,yCAAAA,sBAAuBE,WAAW,EAAE;QACtC,OAAO;IACT;IAEA,IAAIF,yCAAAA,sBAAuBG,kBAAkB,EAAE;QAC7CN;IACF;IAEA,OAAO;AACT"}