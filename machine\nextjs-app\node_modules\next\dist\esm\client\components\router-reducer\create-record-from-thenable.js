/**
 * Create data fetching record for Promise.
 */ // TODO-APP: change `any` to type inference.
export function createRecordFromThenable(thenable) {
    thenable.status = "pending";
    thenable.then((value)=>{
        if (thenable.status === "pending") {
            thenable.status = "fulfilled";
            thenable.value = value;
        }
    }, (err)=>{
        if (thenable.status === "pending") {
            thenable.status = "rejected";
            thenable.value = err;
        }
    });
    return thenable;
}

//# sourceMappingURL=create-record-from-thenable.js.map