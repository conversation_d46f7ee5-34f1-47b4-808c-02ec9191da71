import { <PERSON>, <PERSON>, Dumb<PERSON>, <PERSON>, <PERSON>ap, Target } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function Activities() {
  const activities = [
    {
      title: "Strength Training",
      icon: <Dumbbell className="h-8 w-8" />,
      description: "Build muscle and increase strength with our comprehensive weight training programs.",
      duration: "45-60 mins",
      level: "All Levels",
      image: "https://images.pexels.com/photos/791763/pexels-photo-791763.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    },
    {
      title: "HIIT Classes",
      icon: <Zap className="h-8 w-8" />,
      description: "High-intensity interval training for maximum calorie burn and fitness improvement.",
      duration: "30-45 mins",
      level: "Intermediate",
      image: "https://images.pexels.com/photos/1552103/pexels-photo-1552103.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    },
    {
      title: "Cardio Workouts",
      icon: <Heart className="h-8 w-8" />,
      description: "Improve cardiovascular health with our variety of cardio equipment and classes.",
      duration: "20-60 mins",
      level: "All Levels",
      image: "https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    },
    {
      title: "Personal Training",
      icon: <Target className="h-8 w-8" />,
      description: "One-on-one sessions with certified trainers for personalized fitness plans.",
      duration: "60 mins",
      level: "All Levels",
      image: "https://images.pexels.com/photos/1552252/pexels-photo-1552252.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    },
    {
      title: "Group Classes",
      icon: <Users className="h-8 w-8" />,
      description: "Join our motivating group fitness classes including yoga, pilates, and more.",
      duration: "45-60 mins",
      level: "All Levels",
      image: "https://images.pexels.com/photos/3757942/pexels-photo-3757942.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    },
    {
      title: "Functional Training",
      icon: <Zap className="h-8 w-8" />,
      description: "Real-world movement patterns to improve daily life activities and performance.",
      duration: "45 mins",
      level: "Intermediate",
      image: "https://images.pexels.com/photos/1552106/pexels-photo-1552106.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    }
  ];

  const schedule = [
    { time: "6:00 AM", monday: "HIIT", tuesday: "Strength", wednesday: "Cardio", thursday: "HIIT", friday: "Strength", saturday: "Group Yoga", sunday: "Rest" },
    { time: "7:00 AM", monday: "Strength", tuesday: "Cardio", wednesday: "HIIT", thursday: "Strength", friday: "Cardio", saturday: "Pilates", sunday: "Rest" },
    { time: "12:00 PM", monday: "Group Fitness", tuesday: "HIIT", wednesday: "Strength", thursday: "Group Fitness", friday: "HIIT", saturday: "Open Gym", sunday: "Open Gym" },
    { time: "6:00 PM", monday: "Strength", tuesday: "Group Fitness", wednesday: "HIIT", thursday: "Strength", friday: "Group Fitness", saturday: "Open Gym", sunday: "Rest" },
    { time: "7:00 PM", monday: "HIIT", tuesday: "Strength", wednesday: "Cardio", thursday: "HIIT", friday: "Strength", saturday: "Rest", sunday: "Rest" }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">Our Activities</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Discover a wide range of fitness activities designed to challenge, motivate, and transform you.
            </p>
          </div>
        </div>
      </section>

      {/* Activities Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {activities.map((activity, index) => (
              <Card key={index} className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-48">
                  <img 
                    src={activity.image}
                    alt={activity.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4 bg-black text-white rounded-full p-2">
                    {activity.icon}
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-gray-900">
                    {activity.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    {activity.description}
                  </p>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-500">{activity.duration}</span>
                    </div>
                    <Badge variant="outline">{activity.level}</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Schedule Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Weekly Schedule</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Plan your week with our structured class schedule. All classes are included with membership.
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full bg-white rounded-lg shadow-lg">
              <thead className="bg-black text-white">
                <tr>
                  <th className="px-6 py-4 text-left font-semibold">Time</th>
                  <th className="px-6 py-4 text-left font-semibold">Monday</th>
                  <th className="px-6 py-4 text-left font-semibold">Tuesday</th>
                  <th className="px-6 py-4 text-left font-semibold">Wednesday</th>
                  <th className="px-6 py-4 text-left font-semibold">Thursday</th>
                  <th className="px-6 py-4 text-left font-semibold">Friday</th>
                  <th className="px-6 py-4 text-left font-semibold">Saturday</th>
                  <th className="px-6 py-4 text-left font-semibold">Sunday</th>
                </tr>
              </thead>
              <tbody>
                {schedule.map((row, index) => (
                  <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-6 py-4 font-semibold text-gray-900">{row.time}</td>
                    <td className="px-6 py-4 text-gray-600">{row.monday}</td>
                    <td className="px-6 py-4 text-gray-600">{row.tuesday}</td>
                    <td className="px-6 py-4 text-gray-600">{row.wednesday}</td>
                    <td className="px-6 py-4 text-gray-600">{row.thursday}</td>
                    <td className="px-6 py-4 text-gray-600">{row.friday}</td>
                    <td className="px-6 py-4 text-gray-600">{row.saturday}</td>
                    <td className="px-6 py-4 text-gray-600">{row.sunday}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
  );
}