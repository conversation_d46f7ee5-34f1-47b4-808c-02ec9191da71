{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/hot-linked-text/index.tsx"], "names": ["React", "getWordsAndWhitespaces", "linkRegex", "HotlinkedText", "props", "text", "wordsAndWhitespaces", "test", "map", "word", "index", "Fragment", "key", "a", "href"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,sBAAsB,QAAQ,8BAA6B;AAEpE,MAAMC,YAAY;AAElB,OAAO,MAAMC,gBAER,SAASA,cAAcC,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAE,GAAGD;IAEjB,MAAME,sBAAsBL,uBAAuBI;IAEnD,qBACE,0CACGH,UAAUK,IAAI,CAACF,QACZC,oBAAoBE,GAAG,CAAC,CAACC,MAAMC;QAC7B,IAAIR,UAAUK,IAAI,CAACE,OAAO;YACxB,qBACE,oBAACT,MAAMW,QAAQ;gBAACC,KAAK,AAAC,UAAOF;6BAC3B,oBAACG;gBAAEC,MAAML;eAAOA;QAGtB;QACA,qBAAO,oBAACT,MAAMW,QAAQ;YAACC,KAAK,AAAC,UAAOF;WAAUD;IAChD,KACAJ;AAGV,EAAC"}