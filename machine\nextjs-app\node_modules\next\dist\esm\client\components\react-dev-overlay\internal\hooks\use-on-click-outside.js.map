{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/hooks/use-on-click-outside.ts"], "names": ["React", "useOnClickOutside", "el", "handler", "useEffect", "listener", "e", "contains", "target", "root", "getRootNode", "addEventListener", "removeEventListener"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAE9B,OAAO,SAASC,kBACdC,EAAe,EACfC,OAA2D;IAE3DH,MAAMI,SAAS,CAAC;QACd,IAAIF,MAAM,QAAQC,WAAW,MAAM;YACjC;QACF;QAEA,MAAME,WAAW,CAACC;YAChB,8DAA8D;YAC9D,IAAI,CAACJ,MAAMA,GAAGK,QAAQ,CAACD,EAAEE,MAAM,GAAc;gBAC3C;YACF;YAEAL,QAAQG;QACV;QAEA,MAAMG,OAAOP,GAAGQ,WAAW;QAC3BD,KAAKE,gBAAgB,CAAC,aAAaN;QACnCI,KAAKE,gBAAgB,CAAC,cAAcN;QACpC,OAAO;YACLI,KAAKG,mBAAmB,CAAC,aAAaP;YACtCI,KAAKG,mBAAmB,CAAC,cAAcP;QACzC;IACF,GAAG;QAACF;QAASD;KAAG;AAClB"}