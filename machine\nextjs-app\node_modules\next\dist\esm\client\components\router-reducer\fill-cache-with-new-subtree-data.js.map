{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts"], "names": ["CacheStates", "invalidateCacheByRouterState", "fillLazyItemsTillLeafWithHead", "createRouterCache<PERSON>ey", "fillCacheWithNewSubTreeData", "newCache", "existingCache", "flightDataPath", "wasPrefetched", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "data", "status", "READY", "subTreeData", "slice"], "mappings": "AAAA,SAEEA,WAAW,QACN,wDAAuD;AAE9D,SAASC,4BAA4B,QAAQ,qCAAoC;AACjF,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,4BACdC,QAAmB,EACnBC,aAAwB,EACxBC,cAA8B,EAC9BC,aAAuB;IAEvB,MAAMC,cAAcF,eAAeG,MAAM,IAAI;IAC7C,MAAM,CAACC,kBAAkBC,QAAQ,GAAGL;IAEpC,MAAMM,WAAWV,qBAAqBS;IAEtC,MAAME,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACL;IAEnC,IAAI,CAACG,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACL;IAClD,IAAI,CAACM,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACR,kBAAkBM;IAChD;IAEA,MAAMG,yBAAyBN,wBAAwBE,GAAG,CAACH;IAC3D,IAAIQ,iBAAiBJ,gBAAgBD,GAAG,CAACH;IAEzC,IAAIJ,aAAa;QACf,IACE,CAACY,kBACD,CAACA,eAAeC,IAAI,IACpBD,mBAAmBD,wBACnB;YACAC,iBAAiB;gBACfE,QAAQvB,YAAYwB,KAAK;gBACzBF,MAAM;gBACNG,aAAalB,cAAc,CAAC,EAAE;gBAC9B,oEAAoE;gBACpEQ,gBAAgBK,yBACZ,IAAIF,IAAIE,uBAAuBL,cAAc,IAC7C,IAAIG;YACV;YAEA,IAAIE,wBAAwB;gBAC1BnB,6BACEoB,gBACAD,wBACAb,cAAc,CAAC,EAAE;YAErB;YAEAL,8BACEmB,gBACAD,wBACAb,cAAc,CAAC,EAAE,EACjBA,cAAc,CAAC,EAAE,EACjBC;YAGFS,gBAAgBE,GAAG,CAACN,UAAUQ;QAChC;QACA;IACF;IAEA,IAAI,CAACA,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfE,QAAQF,eAAeE,MAAM;YAC7BD,MAAMD,eAAeC,IAAI;YACzBG,aAAaJ,eAAeI,WAAW;YACvCV,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;QACvD;QACAE,gBAAgBE,GAAG,CAACN,UAAUQ;IAChC;IAEAjB,4BACEiB,gBACAD,wBACAb,eAAemB,KAAK,CAAC,IACrBlB;AAEJ"}