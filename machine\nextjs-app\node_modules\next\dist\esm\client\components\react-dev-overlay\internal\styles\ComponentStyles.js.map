{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/styles/ComponentStyles.tsx"], "names": ["React", "styles", "codeFrame", "dialog", "leftRightDialogHeader", "overlay", "terminal", "toast", "versionStaleness", "buildErrorStyles", "rootLayoutErrorStyles", "containerErrorStyles", "containerRuntimeErrorStyles", "noop", "css", "ComponentStyles", "style"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,YAAYA,WAAW,QAAO;AAE9B,SAASC,UAAUC,SAAS,QAAQ,iCAAgC;AACpE,SAASD,UAAUE,MAAM,QAAQ,uBAAsB;AACvD,SAASF,UAAUG,qBAAqB,QAAQ,6CAA4C;AAC5F,SAASH,UAAUI,OAAO,QAAQ,+BAA8B;AAChE,SAASJ,UAAUK,QAAQ,QAAQ,gCAA+B;AAClE,SAASL,UAAUM,KAAK,QAAQ,sBAAqB;AACrD,SAASN,UAAUO,gBAAgB,QAAQ,qCAAoC;AAC/E,SAASP,UAAUQ,gBAAgB,QAAQ,0BAAyB;AACpE,SAASR,UAAUS,qBAAqB,QAAQ,+BAA8B;AAC9E,SAAST,UAAUU,oBAAoB,QAAQ,sBAAqB;AACpE,SAASV,UAAUW,2BAA2B,QAAQ,4BAA2B;AACjF,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AAEtD,OAAO,SAASC;IACd,qBACE,oBAACC,eACEF,uBACGT,SACAE,OACAJ,QACAC,uBACAF,WACAI,UAEAG,kBACAC,uBACAC,sBACAC,6BACAJ;AAIV"}