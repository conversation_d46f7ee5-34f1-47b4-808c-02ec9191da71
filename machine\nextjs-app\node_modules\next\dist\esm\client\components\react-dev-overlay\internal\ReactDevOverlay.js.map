{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/internal/ReactDevOverlay.tsx"], "names": ["React", "ACTION_UNHANDLED_ERROR", "ShadowPort<PERSON>", "BuildError", "Errors", "RootLayoutError", "parseStack", "Base", "ComponentStyles", "CssReset", "ReactDevOverlay", "PureComponent", "getDerivedStateFromError", "error", "e", "event", "type", "reason", "frames", "stack", "errorEvent", "id", "reactError", "componentDidCatch", "componentErr", "props", "onReactError", "render", "state", "children", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "rootLayoutMissingTagsError", "isMounted", "html", "head", "body", "missingTags", "message", "versionInfo", "initialDisplayState", "undefined"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,SACEC,sBAAsB,QAGjB,0BAAyB;AAEhC,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,UAAU,QAAQ,yBAAwB;AACnD,SAASC,MAAM,QAA6B,qBAAoB;AAChE,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,IAAI,QAAQ,gBAAe;AACpC,SAASC,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,QAAQ,QAAQ,oBAAmB;AAK5C,MAAMC,wBAAwBV,MAAMW,aAAa;IAU/C,OAAOC,yBAAyBC,KAAY,EAAwB;QAClE,MAAMC,IAAID;QACV,MAAME,QAA8B;YAClCC,MAAMf;YACNgB,QAAQJ;YACRK,QAAQZ,WAAWQ,EAAEK,KAAK;QAC5B;QACA,MAAMC,aAAkC;YACtCC,IAAI;YACJN;QACF;QACA,OAAO;YAAEO,YAAYF;QAAW;IAClC;IAEAG,kBAAkBC,YAAmB,EAAE;QACrC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACF;IAC1B;IAEAG,SAAS;QACP,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACJ,KAAK;QACtC,MAAM,EAAEH,UAAU,EAAE,GAAG,IAAI,CAACM,KAAK;QAEjC,MAAME,gBAAgBF,MAAMG,UAAU,IAAI;QAC1C,MAAMC,mBAAmBC,QAAQL,MAAMM,MAAM,CAACC,MAAM;QACpD,MAAMC,6BAA6BR,MAAMQ,0BAA0B;QACnE,MAAMC,YACJP,iBACAE,oBACAV,cACAc;QAEF,qBACE,0CACGd,2BACC,oBAACgB,4BACC,oBAACC,6BACD,oBAACC,iBAGHX,UAEDQ,0BACC,oBAACnC,kCACC,oBAACO,+BACD,oBAACF,2BACD,oBAACC,wBAEA4B,2CACC,oBAAC/B;YACCoC,aAAaL,2BAA2BK,WAAW;aAEnDX,8BACF,oBAAC3B;YACCuC,SAASd,MAAMG,UAAU;YACzBY,aAAaf,MAAMe,WAAW;aAE9BrB,2BACF,oBAAClB;YACCuC,aAAaf,MAAMe,WAAW;YAC9BC,qBAAoB;YACpBV,QAAQ;gBAACZ;aAAW;aAEpBU,iCACF,oBAAC5B;YACCwC,qBAAoB;YACpBV,QAAQN,MAAMM,MAAM;YACpBS,aAAaf,MAAMe,WAAW;aAE9BE,aAEJA;IAGV;;;aA3EAjB,QAAQ;YAAEN,YAAY;QAAK;;AA4E7B;AAEA,eAAeZ,gBAAe"}