{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/index.ts"], "names": ["path", "webpack", "ModuleFilenameHelpers", "sources", "pLimit", "Worker", "spans", "getEcmaVersion", "environment", "arrowFunction", "const", "destructuring", "forOf", "module", "bigIntLiteral", "dynamicImport", "buildError", "error", "file", "line", "Error", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "terserOptions", "parallel", "swcMinify", "optimize", "compiler", "compilation", "assets", "optimizeOptions", "cache", "SourceMapSource", "RawSource", "compilationSpan", "get", "terserSpan", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "name", "traceAsyncFn", "numberOfAssetsForMinify", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "match", "info", "minimized", "map", "source", "eTag", "getLazyHashedEtag", "output", "getPromise", "dir", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "numberOfWorkers", "Math", "min", "availableNumberOfCores", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "JSON", "stringify", "compress", "mangle", "__dirname", "numWorkers", "enableWorkerThreads", "getStdout", "pipe", "stdout", "getStderr", "stderr", "limit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "javascriptModule", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "ecma", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": "AAAA,YAAYA,UAAU,OAAM;AAC5B,SACEC,OAAO,EACPC,qBAAqB,EACrBC,OAAO,QACF,qCAAoC;AAC3C,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,MAAM,QAAQ,iCAAgC;AACvD,SAASC,KAAK,QAAQ,yBAAwB;AAE9C,SAASC,eAAeC,WAAgB;IACtC,SAAS;IACT,IACEA,YAAYC,aAAa,IACzBD,YAAYE,KAAK,IACjBF,YAAYG,aAAa,IACzBH,YAAYI,KAAK,IACjBJ,YAAYK,MAAM,EAClB;QACA,OAAO;IACT;IAEA,UAAU;IACV,IAAIL,YAAYM,aAAa,IAAIN,YAAYO,aAAa,EAAE;QAC1D,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,MACT,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC5DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,CAAC,GAAG,GACpE,CAAC;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,CAAC,CAAC;IAC1E;IAEA,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,CAAC;AAC1D;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAEjD,OAAO,MAAMC;IAEXC,YAAYC,UAAe,CAAC,CAAC,CAAE;QAC7B,MAAM,EAAEC,gBAAgB,CAAC,CAAC,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;QAEpD,IAAI,CAACA,OAAO,GAAG;YACbG;YACAD;YACAD;QACF;IACF;IAEA,MAAMG,SACJC,QAAa,EACbC,WAAgB,EAChBC,MAAW,EACXC,eAAoB,EACpBC,KAAU,EACV,EAAEC,eAAe,EAAEC,SAAS,EAAO,EACnC;QACA,MAAMC,kBAAkBvC,MAAMwC,GAAG,CAACP,gBAAiBjC,MAAMwC,GAAG,CAACR;QAC7D,MAAMS,aAAaF,gBAAgBG,UAAU,CAC3C;QAEFD,WAAWE,YAAY,CAAC,mBAAmBV,YAAYW,IAAI;QAC3DH,WAAWE,YAAY,CAAC,aAAa,IAAI,CAAChB,OAAO,CAACG,SAAS;QAE3D,OAAOW,WAAWI,YAAY,CAAC;YAC7B,IAAIC,0BAA0B;YAC9B,MAAMC,aAAaC,OAAOC,IAAI,CAACf;YAE/B,MAAMgB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACT;gBACP,IACE,CAAChD,sBAAsB0D,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bb,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMc,MAAMzB,YAAY0B,QAAQ,CAACf;gBACjC,IAAI,CAACc,KAAK;oBACRE,QAAQC,GAAG,CAACjB;oBACZ,OAAO;gBACT;gBAEA,yDAAyD;gBACzD,gEAAgE;gBAChE,IACEA,KAAKkB,KAAK,CACR,2DAEF;oBACA,OAAO;gBACT;gBAEA,MAAM,EAAEC,IAAI,EAAE,GAAGL;gBAEjB,qDAAqD;gBACrD,IAAIK,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOrB;gBACV,MAAM,EAAEmB,IAAI,EAAEG,MAAM,EAAE,GAAGjC,YAAY0B,QAAQ,CAACf;gBAE9C,MAAMuB,OAAO/B,MAAMgC,iBAAiB,CAACF;gBACrC,MAAMG,SAAS,MAAMjC,MAAMkC,UAAU,CAAC1B,MAAMuB;gBAE5C,IAAI,CAACE,QAAQ;oBACXvB,2BAA2B;gBAC7B;gBAEA,IAAIzB,eAAeA,gBAAgB,KAAK;oBACtCuC,QAAQW,GAAG,CACT;wBACE3B;wBACAsB,QAAQA,OAAOA,MAAM,GAAGM,QAAQ;oBAClC,GACA;wBACEC,aAAaC;wBACbC,iBAAiBD;oBACnB;gBAEJ;gBACA,OAAO;oBAAE9B;oBAAMmB;oBAAMa,aAAaV;oBAAQG;oBAAQF;gBAAK;YACzD;YAGJ,MAAMU,kBAAkBC,KAAKC,GAAG,CAC9BjC,yBACAX,gBAAgB6C,sBAAsB;YAGxC,IAAIC;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,IAAI,IAAI,CAACvD,OAAO,CAACG,SAAS,EAAE;oBAC1B,OAAO;wBACLqD,QAAQ,OAAOxD;4BACb,MAAMyD,SAAS,MAAMC,QAAQ,mBAAmBF,MAAM,CACpDxD,QAAQ2D,KAAK,EACb;gCACE,GAAI3D,QAAQ4D,cAAc,GACtB;oCACEC,WAAW;wCACTC,SAASC,KAAKC,SAAS,CAAChE,QAAQ4D,cAAc;oCAChD;gCACF,IACA,CAAC,CAAC;gCACNK,UAAU;gCACVC,QAAQ;4BACV;4BAGF,OAAOT;wBACT;oBACF;gBACF;gBAEA,IAAIH,mBAAmB;oBACrB,OAAOA;gBACT;gBAEAA,oBAAoB,IAAIlF,OAAOL,KAAK0B,IAAI,CAAC0E,WAAW,gBAAgB;oBAClEC,YAAYlB;oBACZmB,qBAAqB;gBACvB;gBAEAf,kBAAkBgB,SAAS,GAAGC,IAAI,CAAC5E,QAAQ6E,MAAM;gBACjDlB,kBAAkBmB,SAAS,GAAGF,IAAI,CAAC5E,QAAQ+E,MAAM;gBAEjD,OAAOpB;YACT;YAEA,MAAMqB,QAAQxG,OACZ,mEAAmE;YACnE,IAAI,CAAC6B,OAAO,CAACG,SAAS,GAClB4C,WACA5B,0BAA0B,IAC1B+B,kBACAH;YAEN,MAAM6B,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAAStD,gBAAiB;gBACnCqD,eAAeE,IAAI,CACjBH,MAAM;oBACJ,MAAM,EAAE1D,IAAI,EAAEgC,WAAW,EAAEb,IAAI,EAAEI,IAAI,EAAE,GAAGqC;oBAC1C,IAAI,EAAEnC,MAAM,EAAE,GAAGmC;oBAEjB,MAAME,aAAajE,WAAWC,UAAU,CAAC;oBACzCgE,WAAW/D,YAAY,CAAC,QAAQC;oBAChC8D,WAAW/D,YAAY,CACrB,SACA,OAAO0B,WAAW,cAAc,SAAS;oBAG3C,OAAOqC,WAAW7D,YAAY,CAAC;wBAC7B,IAAI,CAACwB,QAAQ;4BACX,MAAM,EAAEH,QAAQyC,qBAAqB,EAAE1C,KAAKsB,cAAc,EAAE,GAC1DX,YAAYgC,YAAY;4BAE1B,MAAMtB,QAAQuB,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBnC,QAAQ,KAC9BmC;4BAEJ,MAAMhF,UAAU;gCACdiB;gCACA0C;gCACAC;gCACA3D,eAAe;oCAAE,GAAG,IAAI,CAACD,OAAO,CAACC,aAAa;gCAAC;4BACjD;4BAEA,IAAI,OAAOD,QAAQC,aAAa,CAACrB,MAAM,KAAK,aAAa;gCACvD,IAAI,OAAOwD,KAAKgD,gBAAgB,KAAK,aAAa;oCAChDpF,QAAQC,aAAa,CAACrB,MAAM,GAAGwD,KAAKgD,gBAAgB;gCACtD,OAAO,IAAI,iBAAiBtD,IAAI,CAACb,OAAO;oCACtCjB,QAAQC,aAAa,CAACrB,MAAM,GAAG;gCACjC,OAAO,IAAI,iBAAiBkD,IAAI,CAACb,OAAO;oCACtCjB,QAAQC,aAAa,CAACrB,MAAM,GAAG;gCACjC;4BACF;4BAEA,IAAI;gCACF8D,SAAS,MAAMa,YAAYC,MAAM,CAACxD;4BACpC,EAAE,OAAOhB,OAAO;gCACdsB,YAAY+E,MAAM,CAACP,IAAI,CAAC/F,WAAWC,OAAOiC;gCAE1C;4BACF;4BAEA,IAAIyB,OAAOJ,GAAG,EAAE;gCACdI,OAAOH,MAAM,GAAG,IAAI7B,gBAClBgC,OAAO4C,IAAI,EACXrE,MACAyB,OAAOJ,GAAG,EACVqB,OACAC,gBACA;4BAEJ,OAAO;gCACLlB,OAAOH,MAAM,GAAG,IAAI5B,UAAU+B,OAAO4C,IAAI;4BAC3C;4BAEA,MAAM7E,MAAM8E,YAAY,CAACtE,MAAMuB,MAAM;gCACnCD,QAAQG,OAAOH,MAAM;4BACvB;wBACF;wBAEA,MAAMiD,UAAU;4BAAEnD,WAAW;wBAAK;wBAClC,MAAM,EAAEE,MAAM,EAAE,GAAGG;wBAEnBpC,YAAYmF,WAAW,CAACxE,MAAMsB,QAAQiD;oBACxC;gBACF;YAEJ;YAEA,MAAMhE,QAAQC,GAAG,CAACmD;YAElB,IAAItB,mBAAmB;gBACrB,MAAMA,kBAAkBoC,GAAG;YAC7B;QACF;IACF;IAEAC,MAAMtF,QAAa,EAAE;YACoBA;QAAvC,MAAM,EAAEK,eAAe,EAAEC,SAAS,EAAE,GAAGN,CAAAA,6BAAAA,oBAAAA,SAAUrC,OAAO,qBAAjBqC,kBAAmBnC,OAAO,KAAIA;QACrE,MAAM,EAAEwE,MAAM,EAAE,GAAGrC,SAASL,OAAO;QAEnC,IAAI,OAAO,IAAI,CAACA,OAAO,CAACC,aAAa,CAAC2F,IAAI,KAAK,aAAa;YAC1D,IAAI,CAAC5F,OAAO,CAACC,aAAa,CAAC2F,IAAI,GAAGtH,eAAeoE,OAAOnE,WAAW,IAAI,CAAC;QAC1E;QAEA,MAAMsH,aAAa,IAAI,CAAC9F,WAAW,CAACkB,IAAI;QACxC,MAAMoC,yBAAyB,IAAI,CAACrD,OAAO,CAACE,QAAQ;QAEpDG,SAASyF,KAAK,CAACC,eAAe,CAACC,GAAG,CAACH,YAAY,CAACvF;YAC9C,MAAMG,QAAQH,YAAY2F,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJtI,QAAQuI,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5DnG;YAEJgG,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEArG,YAAYwF,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACE7F,MAAM4E;gBACNkB,OAAO/I,QAAQgJ,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAAC1G,SACC,IAAI,CAACH,QAAQ,CACXC,UACAC,aACAC,QACA;oBACE8C;gBACF,GACA5C,OACA;oBAAEC;oBAAiBC;gBAAU;YAInCL,YAAYwF,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAC3D,WAAgB,EAAEiF,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxClF,YAAYiF,MAAMC,WAAW,gBAAgB1F;YAErD;QACF;IACF;AACF"}