{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fetch-server-response.ts"], "names": ["createFromFetch", "process", "env", "NEXT_RUNTIME", "require", "NEXT_ROUTER_PREFETCH", "NEXT_ROUTER_STATE_TREE", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC", "RSC_CONTENT_TYPE_HEADER", "urlToUrlWithoutFlightMarker", "callServer", "PrefetchKind", "hexHash", "doMpaNavigation", "url", "toString", "undefined", "fetchServerResponse", "flightRouterState", "nextUrl", "currentBuildId", "prefetchKind", "headers", "encodeURIComponent", "JSON", "stringify", "AUTO", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "fetchUrl", "URL", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "searchParams", "set", "res", "fetch", "credentials", "responseUrl", "canonicalUrl", "redirected", "contentType", "get", "isFlightResponse", "startsWith", "ok", "buildId", "flightData", "Promise", "resolve", "err", "console", "error"], "mappings": "AAAA;AAEA,aAAa;AACb,6DAA6D;AAC7D,oEAAoE;AACpE,MAAM,EAAEA,eAAe,EAAE,GACvB,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAQd,SACEC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBC,QAAQ,EACRC,GAAG,EACHC,uBAAuB,QAClB,wBAAuB;AAC9B,SAASC,2BAA2B,QAAQ,gBAAe;AAC3D,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,YAAY,QAAQ,yBAAwB;AACrD,SAASC,OAAO,QAAQ,2BAA0B;AAOlD,SAASC,gBAAgBC,GAAW;IAClC,OAAO;QAACL,4BAA4BK,KAAKC,QAAQ;QAAIC;KAAU;AACjE;AAEA;;CAEC,GACD,OAAO,eAAeC,oBACpBH,GAAQ,EACRI,iBAAoC,EACpCC,OAAsB,EACtBC,cAAsB,EACtBC,YAA2B;IAE3B,MAAMC,UAKF;QACF,yBAAyB;QACzB,CAACf,IAAI,EAAE;QACP,mCAAmC;QACnC,CAACH,uBAAuB,EAAEmB,mBACxBC,KAAKC,SAAS,CAACP;IAEnB;IAEA;;;;;GAKC,GACD,IAAIG,iBAAiBV,aAAae,IAAI,EAAE;QACtCJ,OAAO,CAACnB,qBAAqB,GAAG;IAClC;IAEA,IAAIgB,SAAS;QACXG,OAAO,CAAChB,SAAS,GAAGa;IACtB;IAEA,MAAMQ,mBAAmBf,QACvB;QACEU,OAAO,CAACnB,qBAAqB,IAAI;QACjCmB,OAAO,CAAClB,uBAAuB;QAC/BkB,OAAO,CAAChB,SAAS;KAClB,CAACsB,IAAI,CAAC;IAGT,IAAI;QACF,IAAIC,WAAW,IAAIC,IAAIhB;QACvB,IAAIf,QAAQC,GAAG,CAAC+B,QAAQ,KAAK,cAAc;YACzC,IAAIhC,QAAQC,GAAG,CAACgC,oBAAoB,KAAK,UAAU;gBACjD,IAAIH,SAASI,QAAQ,CAACC,QAAQ,CAAC,MAAM;oBACnCL,SAASI,QAAQ,IAAI;gBACvB,OAAO;oBACLJ,SAASI,QAAQ,IAAI;gBACvB;YACF;QACF;QAEA,8FAA8F;QAC9FJ,SAASM,YAAY,CAACC,GAAG,CAAC/B,sBAAsBsB;QAEhD,MAAMU,MAAM,MAAMC,MAAMT,UAAU;YAChC,wFAAwF;YACxFU,aAAa;YACbjB;QACF;QAEA,MAAMkB,cAAc/B,4BAA4B4B,IAAIvB,GAAG;QACvD,MAAM2B,eAAeJ,IAAIK,UAAU,GAAGF,cAAcxB;QAEpD,MAAM2B,cAAcN,IAAIf,OAAO,CAACsB,GAAG,CAAC,mBAAmB;QACvD,IAAIC,mBAAmBF,gBAAgBnC;QAEvC,IAAIT,QAAQC,GAAG,CAAC+B,QAAQ,KAAK,cAAc;YACzC,IAAIhC,QAAQC,GAAG,CAACgC,oBAAoB,KAAK,UAAU;gBACjD,IAAI,CAACa,kBAAkB;oBACrBA,mBAAmBF,YAAYG,UAAU,CAAC;gBAC5C;YACF;QACF;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACD,oBAAoB,CAACR,IAAIU,EAAE,EAAE;YAChC,OAAOlC,gBAAgB2B,YAAYzB,QAAQ;QAC7C;QAEA,2EAA2E;QAC3E,MAAM,CAACiC,SAASC,WAAW,GAAuB,MAAMnD,gBACtDoD,QAAQC,OAAO,CAACd,MAChB;YACE3B;QACF;QAGF,IAAIU,mBAAmB4B,SAAS;YAC9B,OAAOnC,gBAAgBwB,IAAIvB,GAAG;QAChC;QAEA,OAAO;YAACmC;YAAYR;SAAa;IACnC,EAAE,OAAOW,KAAK;QACZC,QAAQC,KAAK,CACX,oEACAF;QAEF,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YAACtC,IAAIC,QAAQ;YAAIC;SAAU;IACpC;AACF"}