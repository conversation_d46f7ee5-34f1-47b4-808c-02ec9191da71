{"version": 3, "sources": ["../../../src/build/analysis/get-page-static-info.ts"], "names": ["promises", "fs", "L<PERSON><PERSON><PERSON>", "matcher", "extractExportedConstValue", "UnsupportedValueError", "parseModule", "Log", "SERVER_RUNTIME", "checkCustomRoutes", "tryToParsePath", "isAPIRoute", "isEdgeRuntime", "RSC_MODULE_TYPES", "AUTHORIZED_EXTRA_ROUTER_PROPS", "CLIENT_MODULE_LABEL", "ACTION_MODULE_LABEL", "CLIENT_DIRECTIVE", "SERVER_ACTION_DIRECTIVE", "getRSCModuleInformation", "source", "isServerLayer", "clientInfoMatch", "actions", "match", "split", "isClientRef", "type", "client", "clientRefs", "clientEntryType", "server", "warnedInvalidValueMap", "runtime", "Map", "preferredRegion", "warnInvalidValue", "pageFilePath", "key", "message", "has", "warn", "set", "checkExports", "swcAST", "exportsSet", "Set", "Array", "isArray", "body", "ssr", "ssg", "generateImageMetadata", "generateSitemaps", "generateStaticParams", "extraProperties", "directives", "hasLeadingNonDirectiveNode", "node", "expression", "directive", "value", "add", "declaration", "declarations", "id", "init", "elements", "element", "push", "identifier", "values", "specifiers", "map", "specifier", "orig", "err", "undefined", "tryToReadFile", "filePath", "shouldThrow", "readFile", "encoding", "error", "getMiddlewareMatchers", "matcherOrMatchers", "nextConfig", "matchers", "i18n", "originalSourceMap", "routes", "m", "middleware", "r", "isRoot", "locales", "locale", "basePath", "rest", "parsedPage", "regexStr", "Error", "originalSource", "get", "regexp", "getMiddlewareConfig", "config", "result", "regions", "unstable_allowDynamic", "unstable_allowDynamicGlobs", "glob", "apiRouteWarnings", "max", "warnAboutExperimentalEdge", "apiRoute", "process", "env", "NODE_ENV", "NEXT_PRIVATE_BUILD_WORKER", "warnedUnsupportedValueMap", "warnAboutUnsupportedValue", "page", "path", "isDynamicMetadataRoute", "fileContent", "test", "exportsInfo", "getPageStaticInfo", "params", "isDev", "pageType", "rscInfo", "rsc", "e", "extraConfig", "prop", "includes", "JSON", "stringify", "warnOnce", "resolvedRuntime", "nodejs", "options", "Object", "join", "requiresServerRuntime", "isAnAPIRoute", "replace", "experimentalEdge", "edge", "middlewareConfig", "amp"], "mappings": "AAGA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,cAAc,+BAA8B;AACnD,SAASC,OAAO,QAAQ,gCAA+B;AAEvD,SACEC,yBAAyB,EACzBC,qBAAqB,QAChB,wBAAuB;AAC9B,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,YAAYC,SAAS,gBAAe;AACpC,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,iBAAiB,QAAQ,+BAA8B;AAChE,SAASC,cAAc,QAAQ,8BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAwB;AACnD,SAASC,aAAa,QAAQ,4BAA2B;AACzD,SAASC,gBAAgB,QAAQ,6BAA4B;AAG7D,qCAAqC;AACrC,4DAA4D;AAC5D,MAAMC,gCAAgC;IAAC;CAAc;AA4BrD,MAAMC,sBACJ;AAEF,MAAMC,sBACJ;AAEF,MAAMC,mBAAmB;AACzB,MAAMC,0BAA0B;AAGhC,OAAO,SAASC,wBACdC,MAAc,EACdC,aAAsB;QAEND,gBAAAA,eAYGE;IAZnB,MAAMC,WAAUH,gBAAAA,OAAOI,KAAK,CAACR,0CAAbI,iBAAAA,aAAmC,CAAC,EAAE,qBAAtCA,eAAwCK,KAAK,CAAC;IAC9D,MAAMH,kBAAkBF,OAAOI,KAAK,CAACT;IACrC,MAAMW,cAAc,CAAC,CAACJ;IAEtB,IAAI,CAACD,eAAe;QAClB,OAAO;YACLM,MAAMd,iBAAiBe,MAAM;YAC7BL;YACAG;QACF;IACF;IAEA,MAAMG,aAAaP,oCAAAA,oBAAAA,eAAiB,CAAC,EAAE,qBAApBA,kBAAsBG,KAAK,CAAC;IAC/C,MAAMK,kBAAkBR,mCAAAA,eAAiB,CAAC,EAAE;IAE5C,MAAMK,OAAOE,aAAahB,iBAAiBe,MAAM,GAAGf,iBAAiBkB,MAAM;IAE3E,OAAO;QACLJ;QACAJ;QACAM;QACAC;QACAJ;IACF;AACF;AAEA,MAAMM,wBAAwB;IAC5BC,SAAS,IAAIC;IACbC,iBAAiB,IAAID;AACvB;AACA,SAASE,iBACPC,YAAoB,EACpBC,GAAuC,EACvCC,OAAe;IAEf,IAAIP,qBAAqB,CAACM,IAAI,CAACE,GAAG,CAACH,eAAe;IAElD9B,IAAIkC,IAAI,CACN,CAAC,uCAAuC,EAAEH,IAAI,aAAa,EAAED,aAAa,KAAK,EAAEE,QAAQ,CAAC,CAAC,GACzF,OACA;IAGJP,qBAAqB,CAACM,IAAI,CAACI,GAAG,CAACL,cAAc;AAC/C;AACA;;;;;;CAMC,GACD,SAASM,aACPC,MAAW,EACXP,YAAoB;IAYpB,MAAMQ,aAAa,IAAIC,IAAY;QACjC;QACA;QACA;QACA;QACA;KACD;IACD,IAAIC,MAAMC,OAAO,CAACJ,0BAAAA,OAAQK,IAAI,GAAG;QAC/B,IAAI;YACF,IAAIhB;YACJ,IAAIE;YACJ,IAAIe,MAAe;YACnB,IAAIC,MAAe;YACnB,IAAIC,wBAAiC;YACrC,IAAIC,mBAA4B;YAChC,IAAIC,uBAAuB;YAC3B,IAAIC,kBAAkB,IAAIT;YAC1B,IAAIU,aAAa,IAAIV;YACrB,IAAIW,6BAA6B;YAEjC,KAAK,MAAMC,QAAQd,OAAOK,IAAI,CAAE;oBAoB5BS,mBA2BAA,oBACeA,8BAYfA;gBA3DF,iEAAiE;gBACjE,IACEA,KAAK/B,IAAI,KAAK,yBACd+B,KAAKC,UAAU,CAAChC,IAAI,KAAK,iBACzB;oBACA,IAAI,CAAC8B,4BAA4B;wBAC/B,MAAMG,YAAYF,KAAKC,UAAU,CAACE,KAAK;wBACvC,IAAI5C,qBAAqB2C,WAAW;4BAClCJ,WAAWM,GAAG,CAAC;wBACjB;wBACA,IAAI5C,4BAA4B0C,WAAW;4BACzCJ,WAAWM,GAAG,CAAC;wBACjB;oBACF;gBACF,OAAO;oBACLL,6BAA6B;gBAC/B;gBACA,IACEC,KAAK/B,IAAI,KAAK,uBACd+B,EAAAA,oBAAAA,KAAKK,WAAW,qBAAhBL,kBAAkB/B,IAAI,MAAK,uBAC3B;wBAC0B+B;oBAA1B,KAAK,MAAMK,gBAAeL,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkBM,YAAY,CAAE;wBACxD,IAAID,YAAYE,EAAE,CAACJ,KAAK,KAAK,WAAW;4BACtC5B,UAAU8B,YAAYG,IAAI,CAACL,KAAK;wBAClC,OAAO,IAAIE,YAAYE,EAAE,CAACJ,KAAK,KAAK,mBAAmB;4BACrD,IAAIE,YAAYG,IAAI,CAACvC,IAAI,KAAK,mBAAmB;gCAC/C,MAAMwC,WAAqB,EAAE;gCAC7B,KAAK,MAAMC,WAAWL,YAAYG,IAAI,CAACC,QAAQ,CAAE;oCAC/C,MAAM,EAAER,UAAU,EAAE,GAAGS;oCACvB,IAAIT,WAAWhC,IAAI,KAAK,iBAAiB;wCACvC;oCACF;oCACAwC,SAASE,IAAI,CAACV,WAAWE,KAAK;gCAChC;gCACA1B,kBAAkBgC;4BACpB,OAAO;gCACLhC,kBAAkB4B,YAAYG,IAAI,CAACL,KAAK;4BAC1C;wBACF,OAAO;4BACLN,gBAAgBO,GAAG,CAACC,YAAYE,EAAE,CAACJ,KAAK;wBAC1C;oBACF;gBACF;gBAEA,IACEH,KAAK/B,IAAI,KAAK,uBACd+B,EAAAA,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkB/B,IAAI,MAAK,yBAC3BkB,WAAWL,GAAG,EAACkB,+BAAAA,KAAKK,WAAW,CAACO,UAAU,qBAA3BZ,6BAA6BG,KAAK,GACjD;oBACA,MAAMI,KAAKP,KAAKK,WAAW,CAACO,UAAU,CAACT,KAAK;oBAC5CV,MAAMc,OAAO;oBACbf,MAAMe,OAAO;oBACbb,wBAAwBa,OAAO;oBAC/BZ,mBAAmBY,OAAO;oBAC1BX,uBAAuBW,OAAO;gBAChC;gBAEA,IACEP,KAAK/B,IAAI,KAAK,uBACd+B,EAAAA,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkB/B,IAAI,MAAK,uBAC3B;wBACW+B,iCAAAA;oBAAX,MAAMO,MAAKP,qBAAAA,KAAKK,WAAW,sBAAhBL,kCAAAA,mBAAkBM,YAAY,CAAC,EAAE,qBAAjCN,gCAAmCO,EAAE,CAACJ,KAAK;oBACtD,IAAIhB,WAAWL,GAAG,CAACyB,KAAK;wBACtBd,MAAMc,OAAO;wBACbf,MAAMe,OAAO;wBACbb,wBAAwBa,OAAO;wBAC/BZ,mBAAmBY,OAAO;wBAC1BX,uBAAuBW,OAAO;oBAChC;gBACF;gBAEA,IAAIP,KAAK/B,IAAI,KAAK,0BAA0B;oBAC1C,MAAM4C,SAASb,KAAKc,UAAU,CAACC,GAAG,CAChC,CAACC;4BAECA,iBACAA;+BAFAA,UAAU/C,IAAI,KAAK,qBACnB+C,EAAAA,kBAAAA,UAAUC,IAAI,qBAAdD,gBAAgB/C,IAAI,MAAK,kBACzB+C,mBAAAA,UAAUC,IAAI,qBAAdD,iBAAgBb,KAAK;;oBAGzB,KAAK,MAAMA,SAASU,OAAQ;wBAC1B,IAAI,CAACpB,OAAOU,UAAU,kBAAkBV,MAAM;wBAC9C,IAAI,CAACD,OAAOW,UAAU,sBAAsBX,MAAM;wBAClD,IAAI,CAACE,yBAAyBS,UAAU,yBACtCT,wBAAwB;wBAC1B,IAAI,CAACC,oBAAoBQ,UAAU,oBACjCR,mBAAmB;wBACrB,IAAI,CAACC,wBAAwBO,UAAU,wBACrCP,uBAAuB;wBACzB,IAAI,CAACrB,WAAW4B,UAAU,WACxBzB,iBACEC,cACA,WACA;wBAEJ,IAAI,CAACF,mBAAmB0B,UAAU,mBAChCzB,iBACEC,cACA,mBACA;oBAEN;gBACF;YACF;YAEA,OAAO;gBACLa;gBACAC;gBACAlB;gBACAE;gBACAiB;gBACAC;gBACAC;gBACAC;gBACAC;YACF;QACF,EAAE,OAAOoB,KAAK,CAAC;IACjB;IAEA,OAAO;QACLzB,KAAK;QACLD,KAAK;QACLjB,SAAS4C;QACT1C,iBAAiB0C;QACjBzB,uBAAuB;QACvBC,kBAAkB;QAClBC,sBAAsB;QACtBC,iBAAiBsB;QACjBrB,YAAYqB;IACd;AACF;AAEA,eAAeC,cAAcC,QAAgB,EAAEC,WAAoB;IACjE,IAAI;QACF,OAAO,MAAM/E,GAAGgF,QAAQ,CAACF,UAAU;YACjCG,UAAU;QACZ;IACF,EAAE,OAAOC,OAAY;QACnB,IAAIH,aAAa;YACfG,MAAM5C,OAAO,GAAG,CAAC,mCAAmC,EAAEwC,SAAS,GAAG,EAAEI,MAAM5C,OAAO,CAAC,CAAC;YACnF,MAAM4C;QACR;IACF;AACF;AAEA,OAAO,SAASC,sBACdC,iBAA0B,EAC1BC,UAAsB;IAEtB,IAAIC,WAAsB,EAAE;IAC5B,IAAIxC,MAAMC,OAAO,CAACqC,oBAAoB;QACpCE,WAAWF;IACb,OAAO;QACLE,SAASlB,IAAI,CAACgB;IAChB;IACA,MAAM,EAAEG,IAAI,EAAE,GAAGF;IAEjB,MAAMG,oBAAoB,IAAIvD;IAC9B,IAAIwD,SAASH,SAASd,GAAG,CAAC,CAACkB;QACzB,IAAIC,aAAc,OAAOD,MAAM,WAAW;YAAEvE,QAAQuE;QAAE,IAAIA;QAC1D,IAAIC,YAAY;YACdH,kBAAkB/C,GAAG,CAACkD,YAAYA,WAAWxE,MAAM;QACrD;QACA,OAAOwE;IACT;IAEA,yDAAyD;IACzD,uBAAuB;IACvBnF,kBAAkBiF,QAAQ;IAE1BA,SAASA,OAAOjB,GAAG,CAAC,CAACoB;QACnB,IAAI,EAAEzE,MAAM,EAAE,GAAGyE;QAEjB,MAAMC,SAAS1E,WAAW;QAE1B,IAAIoE,CAAAA,wBAAAA,KAAMO,OAAO,KAAIF,EAAEG,MAAM,KAAK,OAAO;YACvC5E,SAAS,CAAC,yCAAyC,EACjD0E,SAAS,KAAK1E,OACf,CAAC;QACJ;QAEAA,SAAS,CAAC,gCAAgC,EAAEA,OAAO,EACjD0E,SACI,CAAC,CAAC,EAAER,WAAWE,IAAI,GAAG,cAAc,GAAG,wBAAwB,CAAC,GAChE,WACL,CAAC;QAEF,IAAIF,WAAWW,QAAQ,EAAE;YACvB7E,SAAS,CAAC,EAAEkE,WAAWW,QAAQ,CAAC,EAAE7E,OAAO,CAAC;QAC5C;QAEAyE,EAAEzE,MAAM,GAAGA;QACX,OAAOyE;IACT;IAEApF,kBAAkBiF,QAAQ;IAE1B,OAAOA,OAAOjB,GAAG,CAAC,CAACoB;QACjB,MAAM,EAAEzE,MAAM,EAAE,GAAG8E,MAAM,GAAGL;QAC5B,MAAMM,aAAazF,eAAeU;QAElC,IAAI+E,WAAWhB,KAAK,IAAI,CAACgB,WAAWC,QAAQ,EAAE;YAC5C,MAAM,IAAIC,MAAM,CAAC,gBAAgB,EAAEjF,OAAO,CAAC;QAC7C;QAEA,MAAMkF,iBAAiBb,kBAAkBc,GAAG,CAACV;QAE7C,OAAO;YACL,GAAGK,IAAI;YACPM,QAAQL,WAAWC,QAAQ;YAC3BE,gBAAgBA,kBAAkBlF;QACpC;IACF;AACF;AAEA,SAASqF,oBACPpE,YAAoB,EACpBqE,MAAW,EACXpB,UAAsB;IAEtB,MAAMqB,SAAoC,CAAC;IAE3C,IAAID,OAAOvG,OAAO,EAAE;QAClBwG,OAAOpB,QAAQ,GAAGH,sBAAsBsB,OAAOvG,OAAO,EAAEmF;IAC1D;IAEA,IAAI,OAAOoB,OAAOE,OAAO,KAAK,YAAY7D,MAAMC,OAAO,CAAC0D,OAAOE,OAAO,GAAG;QACvED,OAAOC,OAAO,GAAGF,OAAOE,OAAO;IACjC,OAAO,IAAI,OAAOF,OAAOE,OAAO,KAAK,aAAa;QAChDrG,IAAIkC,IAAI,CACN,CAAC,4FAA4F,EAAEJ,aAAa,CAAC,CAAC;IAElH;IAEA,IAAIqE,OAAOG,qBAAqB,EAAE;QAChCF,OAAOG,0BAA0B,GAAG/D,MAAMC,OAAO,CAC/C0D,OAAOG,qBAAqB,IAE1BH,OAAOG,qBAAqB,GAC5B;YAACH,OAAOG,qBAAqB;SAAC;QAClC,KAAK,MAAME,QAAQJ,OAAOG,0BAA0B,IAAI,EAAE,CAAE;YAC1D,IAAI;gBACF3G,QAAQ4G;YACV,EAAE,OAAOnC,KAAK;gBACZ,MAAM,IAAIyB,MACR,CAAC,EAAEhE,aAAa,mEAAmE,EAAE0E,KAAK,GAAG,EAC3F,AAACnC,IAAcrC,OAAO,CACvB,CAAC;YAEN;QACF;IACF;IAEA,OAAOoE;AACT;AAEA,MAAMK,mBAAmB,IAAI9G,SAAS;IAAE+G,KAAK;AAAI;AACjD,SAASC,0BAA0BC,QAAuB;IACxD,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,QAAQC,GAAG,CAACE,yBAAyB,KAAK,KAC1C;QACA;IACF;IACA,IAAIP,iBAAiBxE,GAAG,CAAC2E,WAAW;QAClC;IACF;IACA5G,IAAIkC,IAAI,CACN0E,WACI,CAAC,EAAEA,SAAS,2EAA2E,CAAC,GACxF,CAAC,iEAAiE,CAAC;IAEzEH,iBAAiBtE,GAAG,CAACyE,UAAU;AACjC;AAEA,MAAMK,4BAA4B,IAAItH,SAA0B;IAAE+G,KAAK;AAAI;AAE3E,SAASQ,0BACPpF,YAAoB,EACpBqF,IAAwB,EACxBvC,KAA4B;IAE5B,IAAIqC,0BAA0BhF,GAAG,CAACH,eAAe;QAC/C;IACF;IAEA9B,IAAIkC,IAAI,CACN,CAAC,yDAAyD,CAAC,GACxDiF,CAAAA,OAAO,CAAC,OAAO,EAAEA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAErF,aAAa,CAAC,CAAC,AAAD,IAC9C,QACA8C,MAAM5C,OAAO,GACZ4C,CAAAA,MAAMwC,IAAI,GAAG,CAAC,KAAK,EAAExC,MAAMwC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAC,IACvC,QACA,+CACA;IAGJH,0BAA0B9E,GAAG,CAACL,cAAc;AAC9C;AAEA,iEAAiE;AACjE,sDAAsD;AACtD,OAAO,eAAeuF,uBACpBvF,YAAoB;IAEpB,MAAMwF,cAAc,AAAC,MAAM/C,cAAczC,cAAc,SAAU;IACjE,IAAI,CAAC,yCAAyCyF,IAAI,CAACD,cAAc,OAAO;IAExE,MAAMjF,SAAS,MAAMtC,YAAY+B,cAAcwF;IAC/C,MAAME,cAAcpF,aAAaC,QAAQP;IAEzC,OAAO,CAAC0F,YAAY3E,qBAAqB,IAAI,CAAC2E,YAAY1E,gBAAgB;AAC5E;AAEA;;;;;;CAMC,GACD,OAAO,eAAe2E,kBAAkBC,MAMvC;IACC,MAAM,EAAEC,KAAK,EAAE7F,YAAY,EAAEiD,UAAU,EAAEoC,IAAI,EAAES,QAAQ,EAAE,GAAGF;IAE5D,MAAMJ,cAAc,AAAC,MAAM/C,cAAczC,cAAc,CAAC6F,UAAW;IACnE,IACE,8FAA8FJ,IAAI,CAChGD,cAEF;QACA,MAAMjF,SAAS,MAAMtC,YAAY+B,cAAcwF;QAC/C,MAAM,EACJ1E,GAAG,EACHD,GAAG,EACHjB,OAAO,EACPE,eAAe,EACfmB,oBAAoB,EACpBC,eAAe,EACfC,UAAU,EACX,GAAGb,aAAaC,QAAQP;QACzB,MAAM+F,UAAUjH,wBAAwB0G,aAAa;QACrD,MAAMQ,MAAMD,QAAQzG,IAAI;QAExB,sCAAsC;QACtC,IAAI+E;QACJ,IAAI;YACFA,SAAStG,0BAA0BwC,QAAQ;QAC7C,EAAE,OAAO0F,GAAG;YACV,IAAIA,aAAajI,uBAAuB;gBACtCoH,0BAA0BpF,cAAcqF,MAAMY;YAChD;QACA,mFAAmF;QACrF;QAEA,MAAMC,cAAmC,CAAC;QAE1C,IAAIhF,mBAAmB4E,aAAa,OAAO;YACzC,KAAK,MAAMK,QAAQjF,gBAAiB;gBAClC,IAAI,CAACzC,8BAA8B2H,QAAQ,CAACD,OAAO;gBACnD,IAAI;oBACFD,WAAW,CAACC,KAAK,GAAGpI,0BAA0BwC,QAAQ4F;gBACxD,EAAE,OAAOF,GAAG;oBACV,IAAIA,aAAajI,uBAAuB;wBACtCoH,0BAA0BpF,cAAcqF,MAAMY;oBAChD;gBACF;YACF;QACF,OAAO,IAAIH,aAAa,SAAS;YAC/B,IAAK,MAAM7F,OAAOoE,OAAQ;gBACxB,IAAI,CAAC5F,8BAA8B2H,QAAQ,CAACnG,MAAM;gBAClDiG,WAAW,CAACjG,IAAI,GAAGoE,MAAM,CAACpE,IAAI;YAChC;QACF;QAEA,IAAI6F,aAAa,OAAO;YACtB,IAAIzB,QAAQ;gBACV,IAAInE,UAAU,CAAC,eAAe,EAAEF,aAAa,qEAAqE,CAAC;gBAEnH,IAAIqE,OAAOzE,OAAO,EAAE;oBAClBM,WAAW,CAAC,+BAA+B,EAAEmG,KAAKC,SAAS,CACzDjC,OAAOzE,OAAO,EACd,EAAE,CAAC;gBACP;gBAEA,IAAIyE,OAAOE,OAAO,EAAE;oBAClBrE,WAAW,CAAC,uCAAuC,EAAEmG,KAAKC,SAAS,CACjEjC,OAAOE,OAAO,EACd,EAAE,CAAC;gBACP;gBAEArE,WAAW,CAAC,6GAA6G,CAAC;gBAE1H,IAAI2F,OAAO;oBACT3H,IAAIqI,QAAQ,CAACrG;gBACf,OAAO;oBACL,MAAM,IAAI8D,MAAM9D;gBAClB;gBACAmE,SAAS,CAAC;YACZ;QACF;QACA,IAAI,CAACA,QAAQA,SAAS,CAAC;QAEvB,4FAA4F;QAC5F,4EAA4E;QAC5E,iGAAiG;QACjG,yBAAyB;QACzB,IAAImC;QACJ,IAAIV,aAAa,OAAO;YACtBU,kBAAkB5G;QACpB,OAAO;YACL4G,kBAAkB5G,WAAWyE,OAAOzE,OAAO;QAC7C;QAEA,IACE,OAAO4G,oBAAoB,eAC3BA,oBAAoBrI,eAAesI,MAAM,IACzC,CAAClI,cAAciI,kBACf;YACA,MAAME,UAAUC,OAAOzE,MAAM,CAAC/D,gBAAgByI,IAAI,CAAC;YACnD,MAAM1G,UACJ,OAAOsG,oBAAoB,WACvB,CAAC,iFAAiF,EAAEE,QAAQ,CAAC,GAC7F,CAAC,kBAAkB,EAAEF,gBAAgB,4DAA4D,EAAEE,QAAQ,CAAC;YAClH,IAAIb,OAAO;gBACT3H,IAAI4E,KAAK,CAAC5C;YACZ,OAAO;gBACL,MAAM,IAAI8D,MAAM9D;YAClB;QACF;QAEA,MAAM2G,wBAAwBhG,OAAOC,OAAOgF,aAAa;QAEzD,MAAMgB,eAAexI,WAAW+G,wBAAAA,KAAM0B,OAAO,CAAC,wBAAwB;QAEtEP,kBACEjI,cAAciI,oBAAoBK,wBAC9BL,kBACAhE;QAEN,IAAIgE,oBAAoBrI,eAAe6I,gBAAgB,EAAE;YACvDnC,0BAA0BiC,eAAezB,OAAQ;QACnD;QAEA,IACEmB,oBAAoBrI,eAAe8I,IAAI,IACvCnB,aAAa,WACbT,QACA,CAACyB,cACD;YACA,MAAM5G,UAAU,CAAC,KAAK,EAAEmF,KAAK,4HAA4H,CAAC;YAC1J,IAAIQ,OAAO;gBACT3H,IAAI4E,KAAK,CAAC5C;YACZ,OAAO;gBACL,MAAM,IAAI8D,MAAM9D;YAClB;QACF;QAEA,MAAMgH,mBAAmB9C,oBACvBiB,QAAQ,6BACRhB,QACApB;QAGF,IACE6C,aAAa,UACb3E,8BAAAA,WAAYhB,GAAG,CAAC,cAChBc,sBACA;YACA,MAAM,IAAI+C,MACR,CAAC,MAAM,EAAEqB,KAAK,4EAA4E,CAAC;QAE/F;QAEA,OAAO;YACLxE;YACAC;YACAkF;YACA/E;YACAkG,KAAK9C,OAAO8C,GAAG,IAAI;YACnB,GAAID,oBAAoB;gBAAE3D,YAAY2D;YAAiB,CAAC;YACxD,GAAIV,mBAAmB;gBAAE5G,SAAS4G;YAAgB,CAAC;YACnD1G;YACAoG;QACF;IACF;IAEA,OAAO;QACLrF,KAAK;QACLC,KAAK;QACLkF,KAAKxH,iBAAiBkB,MAAM;QAC5BuB,sBAAsB;QACtBkG,KAAK;QACLvH,SAAS4C;IACX;AACF"}