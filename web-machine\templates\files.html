{% extends "base.html" %}
{% block title %}File Explorer{% endblock %}
{% block content %}
<h2>File Explorer</h2>

<p>
  {% if is_admin %}
    <a href="{{ url_for('upload_file') }}">Upload File</a> |
  {% endif %}
  <a href="{{ url_for('dashboard') }}">Back to Dashboard</a>
</p>

<table>
  <thead>
    <tr>
      <th>Filename</th>
      <th>Actions</th>
      {% if is_admin %}
      <th>Admin</th>
      {% endif %}
    </tr>
  </thead>
  <tbody>
    {% for file in files %}
    <tr>
      <td>{{ file }}</td>
      <td>
        <a href="{{ url_for('download_file', filename=file) }}">Download</a> |
        <a href="{{ url_for('view_file', filename=file) }}">View</a>
      </td>
      {% if is_admin %}
      <td>
        <form action="{{ url_for('delete_file', filename=file) }}" method="POST" class="inline" onsubmit="return confirm('Delete file?');">
          <button type="submit">Delete</button>
        </form>
      </td>
      {% endif %}
    </tr>
    {% endfor %}
  </tbody>
</table>

{% if not files %}
<p>No files available. {% if is_admin %} Upload one now!{% endif %}</p>
{% endif %}

{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    <div class="flash">
      {% for category, message in messages %}
        <p>{{ message }}</p>
      {% endfor %}
    </div>
  {% endif %}
{% endwith %}
{% endblock %}
